
# Server Configuration
SERVER_PORT=8080
SERVER_ENVIRONMENT=development
SERVER_READ_TIMEOUT=10
SERVER_WRITE_TIMEOUT=10
SERVER_IDLE_TIMEOUT=120
# MongoDB Configuration
MONGODB_URI=mongodb://mongodb:27017
MONGODB_DATABASE=realestate_dev
# JWT Configuration
JWT_SECRET_KEY=3e273437d5f6e1f20770bc3fe435a97078a7914a898679f321814c32a70e2755
JWT_DURATION=60
# Redis Configuration
REDIS_ADDRESS=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
# EmailJS Configuration
EMAILJS_SERVICE_ID=service_l9104jl
EMAILJS_RESET_PASSWORD_TEMPLATE_ID=template_qs9o169
EMAILJS_RESET_PASSWORD_TEMPLATE_ID=template_qs9o169
EMAILJS_USER_ID=5lI36Rb-7Zle3Ast7
# Frontend Configuration
FRONTEND_URL=http://localhost:3000
# Notification Configuration
ADMIN_USER_ID=000000000000000000000000
