# Server Configuration
SERVER_PORT=8080
SERVER_ENVIRONMENT=production

# MongoDB Configuration
MONGODB_URI=mongodb://mongodb:27017
MONGODB_DATABASE=realestate_prod

# JWT Configuration
JWT_SECRET_KEY=your-production-secret-key-change-this
JWT_DURATION=1440

# Redis Configuration
REDIS_ADDRESS=redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# EmailJS Configuration
EMAILJS_SERVICE_ID=your-production-emailjs-service-id
EMAILJS_RESET_PASSWORD_TEMPLATE_ID=your-production-emailjs-template-id
EMAILJS_RESET_PASSWORD_TEMPLATE_ID=your-production-emailjs-template-id
EMAILJS_USER_ID=your-production-emailjs-user-id

# Frontend Configuration
FRONTEND_URL=https://your-production-frontend-url.com