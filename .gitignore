# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
*.prof
*.trace
*.cover
*.log

# Go workspace files
go.work

# Binary files
bin/
dist/
build/

# Dependency directories
vendor/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local

# Keep example env files
!.env.example
!.env.development.example
!.env.production.example

# Docker volumes
mongodb-data/
redis-data/

# Log files
logs/
*.log

# Temporary files
tmp/
temp/

# Test coverage
coverage/

# Documentation
docs/_build/
site/

# Compiled files
*.o
*.a
*.test
*.prof

# Debug files
debug/

# Local development files
local/ 