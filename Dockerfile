# Build stage
FROM golang:1.24-alpine AS builder

# Set build arguments
ARG APP_ENV=development

# Set environment variables
ENV APP_ENV=${APP_ENV}
ENV GO111MODULE=on
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# Install build dependencies
RUN apk add --no-cache git make

WORKDIR /app

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN go build -o /app/bin/app ./cmd/main.go

# Create a minimal production image
FROM alpine:latest AS production

# Set environment variables
ENV APP_ENV=production

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata

WORKDIR /app

# Create uploads directory
RUN mkdir -p /app/uploads && chmod 777 /app/uploads

# Copy the binary from builder
COPY --from=builder /app/bin/app /app/app
COPY --from=builder /app/config.yaml /app/config.yaml
COPY --from=builder /app/.env.production /app/.env.production
COPY --from=builder /app/docs /app/docs

# Expose port
EXPOSE 8080

# Run the application
CMD ["/app/app"]

# Create a development image with additional tools
FROM golang:1.24-alpine AS development

# Set environment variables
ENV APP_ENV=development

# Install development dependencies
RUN apk add --no-cache git make curl

WORKDIR /app

# Create uploads directory
RUN mkdir -p /app/uploads && chmod 777 /app/uploads

# Copy the binary from builder
COPY --from=builder /app/bin/app /app/app
COPY --from=builder /app/config.yaml /app/config.yaml
COPY --from=builder /app/.env.development /app/.env.development
COPY --from=builder /app/docs /app/docs

# Expose port
EXPOSE 8080

# Run the application
CMD ["/app/app"]