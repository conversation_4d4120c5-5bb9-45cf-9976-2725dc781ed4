.PHONY: run-dev run-prod build-dev build-prod docker-compose-dev docker-compose-prod docker-compose-build-dev docker-compose-build-prod docker-compose-down docker-compose-dev-d docker-compose-prod-d logs-app logs-mongo logs-redis logs-app-f logs-mongo-f logs-redis-f logs-app-prod logs-mongo-prod logs-redis-prod logs-app-prod-f logs-mongo-prod-f logs-redis-prod-f help test test-auth test-api-auth test-api-swagger seed-db

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
BINARY_NAME=realestate-platform

# Run tests
test:
	$(GOTEST) -v ./...

# Test API authentication endpoints
test-api-auth:
	powershell -File scripts/test-api-auth-simple.ps1

# Test API endpoints using Swagger
test-api-swagger:
	powershell -File scripts/test-api-swagger.ps1

# Clean build files
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -rf bin/

# Install dependencies
deps:
	$(GOGET) -v -t -d ./...

# Build development version
build-dev:
	powershell -Command "$$env:APP_ENV='development'; go build -o bin/app-dev.exe ./cmd/main.go"

# Build production version
build-prod:
	powershell -Command "$$env:APP_ENV='production'; go build -o bin/app-prod.exe ./cmd/main.go"

#===========================================================================================================================
# Build all services with Docker Compose in development mode
docker-compose-build-dev:
	docker-compose -f docker-compose.yml build

# Build all services with Docker Compose in production mode
docker-compose-build-prod:
	docker-compose -f docker-compose.prod.yml build

# Run with Docker Compose in development mode
docker-compose-dev:
	docker-compose -f docker-compose.yml up --build

# Run with Docker Compose in development mode (detached)
docker-compose-dev-d: swagger
	docker-compose -f docker-compose.yml up -d --build

# Run with Docker Compose in production mode
docker-compose-prod:
	docker-compose -f docker-compose.prod.yml up --build

# Run with Docker Compose in production mode (detached)
docker-compose-prod-d: swagger
	docker-compose -f docker-compose.prod.yml up -d --build
#==================================================================================================================
# Stop Docker Compose
docker-compose-down:
	docker-compose down

#==================================================================================================================
# Log commands for development environment
# View logs for app service
logs-app:
	docker-compose -f docker-compose.yml logs app

# View logs for MongoDB service
logs-mongo:
	docker-compose -f docker-compose.yml logs mongodb

# View logs for Redis service
logs-redis:
	docker-compose -f docker-compose.yml logs redis

# Follow logs for app service
logs-app-f:
	docker-compose -f docker-compose.yml logs -f app

# Follow logs for MongoDB service
logs-mongo-f:
	docker-compose -f docker-compose.yml logs -f mongodb

# Follow logs for Redis service
logs-redis-f:
	docker-compose -f docker-compose.yml logs -f redis

# Log commands for production environment
# View logs for app service
logs-app-prod:
	docker-compose -f docker-compose.prod.yml logs app

# View logs for MongoDB service
logs-mongo-prod:
	docker-compose -f docker-compose.prod.yml logs mongodb

# View logs for Redis service
logs-redis-prod:
	docker-compose -f docker-compose.prod.yml logs redis

# Follow logs for app service
logs-app-prod-f:
	docker-compose -f docker-compose.prod.yml logs -f app

# Follow logs for MongoDB service
logs-mongo-prod-f:
	docker-compose -f docker-compose.prod.yml logs -f mongodb

# Follow logs for Redis service
logs-redis-prod-f:
	docker-compose -f docker-compose.prod.yml logs -f redis
#==================================================================================================================

# Generate API documentation
swagger:
	swag init -g cmd/main.go

# Run linter
lint:
	golangci-lint run

# Format code
fmt:
	$(GOCMD) fmt ./...

# Run security check
security-check:
	gosec ./...

# Seed database with all data (users, property types, facilities, properties)
seed-db:
	powershell -Command "$$env:APP_ENV='development'; go run scripts/seed_db.go -all"

# Help command
help:
	@echo "Available commands:"
	@echo "  make run-dev              - Run in development environment"
	@echo "  make run-prod             - Run in production environment"
	@echo "  make test                 - Run tests"
	@echo "  make test-auth            - Run authentication tests"
	@echo "  make test-api-auth        - Test API authentication endpoints"
	@echo "  make test-api-swagger     - Test API endpoints using Swagger"
	@echo "  make clean                - Clean build files"
	@echo "  make deps                 - Install dependencies"
	@echo "  make build-dev            - Build development version"
	@echo "  make build-prod           - Build production version"
	@echo "  make docker-compose-build-dev - Build all services with Docker Compose in development mode"
	@echo "  make docker-compose-build-prod - Build all services with Docker Compose in production mode"
	@echo "  make docker-compose-dev   - Run with Docker Compose in development mode"
	@echo "  make docker-compose-dev-d - Run with Docker Compose in development mode (detached) and generate Swagger docs"
	@echo "  make docker-compose-prod  - Run with Docker Compose in production mode"
	@echo "  make docker-compose-prod-d - Run with Docker Compose in production mode (detached) and generate Swagger docs"
	@echo "  make docker-compose-down  - Stop Docker Compose"
	@echo "  make logs-app             - View logs for app service in development"
	@echo "  make logs-mongo           - View logs for MongoDB service in development"
	@echo "  make logs-redis           - View logs for Redis service in development"
	@echo "  make logs-app-f           - Follow logs for app service in development"
	@echo "  make logs-mongo-f         - Follow logs for MongoDB service in development"
	@echo "  make logs-redis-f         - Follow logs for Redis service in development"
	@echo "  make logs-app-prod        - View logs for app service in production"
	@echo "  make logs-mongo-prod      - View logs for MongoDB service in production"
	@echo "  make logs-redis-prod      - View logs for Redis service in production"
	@echo "  make logs-app-prod-f      - Follow logs for app service in production"
	@echo "  make logs-mongo-prod-f    - Follow logs for MongoDB service in production"
	@echo "  make logs-redis-prod-f    - Follow logs for Redis service in production"
	@echo "  make swagger              - Generate API documentation"
	@echo "  make lint                 - Run linter"
	@echo "  make fmt                  - Format code"
	@echo "  make security-check       - Run security check"
	@echo "  make seed-db              - Seed database with all data (users, property types, facilities, properties)"
	@echo "  make migrate-create       - Create database migration"
	@echo "  make migrate-up           - Run database migrations"
	@echo "  make migrate-down         - Rollback database migrations"