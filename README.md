# Real Estate Platform

A modern, scalable real estate platform built with Go, MongoDB, and Redis.

## 🏠 Overview

This project is a real estate platform that allows users to browse, search, and manage property listings. It's built with a microservices architecture using Go for the backend, MongoDB for data storage, and Redis for caching.

## 🚀 Features

- Property listing management
- User authentication and authorization
- Search and filtering capabilities
- Real-time updates with Redis
- RESTful API design
- Docker containerization
- Multi-environment support (development, production)

## 🛠️ Tech Stack

- **Backend**: Go 1.24
- **Web Framework**: Gin
- **Database**: MongoDB
- **Caching**: Redis
- **Authentication**: JWT
- **Configuration**: Viper
- **Logging**: Zap
- **Containerization**: Docker
- **Orchestration**: Docker Compose

## 📋 Prerequisites

- Go 1.24 or higher
- Docker and Docker Compose
- Make (optional, for using Makefile commands)

## 🚀 Getting Started

### Local Development

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/realestate-platform.git
   cd realestate-platform
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.development
   # Edit .env.development with your configuration

   # For production
   cp .env.example .env.production
   # Edit .env.production with your production configuration
   ```

3. **Run with Docker Compose (recommended)**
   ```bash
   make docker-compose-dev-d
   ```
   or
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```

4. **Access the application**
   - API: http://localhost:8080
   - MongoDB: mongodb://localhost:27018
   - Redis: localhost:6379

### Running Without Docker

1. **Install dependencies**
   ```bash
   go mod download
   ```

2. **Run the application**
   ```bash
   make run-dev
   ```
   or
   ```bash
   go run ./cmd/main.go
   ```

## 🏗️ Project Structure

```
realestate-platform/
├── cmd/                  # Application entry points
│   └── main.go           # Main application entry point
├── internal/             # Private application code
│   ├── api/              # API handlers and routes
│   ├── config/           # Configuration management
│   ├── logger/           # Logging utilities
│   ├── models/           # Data models
│   ├── repository/       # Data access layer
│   ├── server/           # Server setup and configuration
│   └── services/         # Business logic
├── bin/                  # Compiled binaries
├── .env.development      # Development environment variables
├── .env.production       # Production environment variables
├── config.yaml           # Application configuration
├── docker-compose.yml    # Docker Compose configuration for development
├── docker-compose.prod.yml # Docker Compose configuration for production
├── Dockerfile            # Docker build configuration
├── go.mod                # Go module definition
├── go.sum                # Go module checksums
├── Makefile              # Build and development commands
└── README.md             # Project documentation
```

## 🛠️ Available Commands

### Development

- `make run-dev` - Run the application in development mode
- `make run-prod` - Run the application in production mode
- `make test` - Run tests
- `make clean` - Clean build files
- `make deps` - Install dependencies
- `make build-dev` - Build development version
- `make build-prod` - Build production version
- `make docker-compose-build-dev` - Build Docker images for development
- `make docker-compose-build-prod` - Build Docker images for production
- `make docker-compose-dev` - Run Docker Compose in development mode
- `make docker-compose-prod` - Run Docker Compose in production mode
- `make docker-compose-dev-d` - Run Docker Compose in development mode (detached)
- `make docker-compose-prod-d` - Run Docker Compose in production mode (detached)
- `make docker-compose-down` - Stop Docker Compose
- `make logs-app` - View logs for app service in development
- `make logs-mongo` - View logs for MongoDB service in development
- `make logs-redis` - View logs for Redis service in development
- `make logs-app-f` - Follow logs for app service in development
- `make logs-mongo-f` - Follow logs for MongoDB service in development
- `make logs-redis-f` - Follow logs for Redis service in development
- `make logs-app-prod` - View logs for app service in production
- `make logs-mongo-prod` - View logs for MongoDB service in production
- `make logs-redis-prod` - View logs for Redis service in production
- `make logs-app-prod-f` - Follow logs for app service in production
- `make logs-mongo-prod-f` - Follow logs for MongoDB service in production
- `make logs-redis-prod-f` - Follow logs for Redis service in production

## 🔧 Configuration

The application can be configured using:

1. **Environment Variables**: Set in `.env.development` or `.env.production`
2. **Configuration File**: `config.yaml`
3. **Docker Environment Variables**: Set in `docker-compose.yml` or `docker-compose.prod.yml`

### How Environment Variables Are Loaded

The application loads environment variables in the following order:

1. The application checks the `APP_ENV` environment variable to determine the environment (defaults to "development")
2. It loads the corresponding `.env.{environment}` file (e.g., `.env.development` or `.env.production`)
3. Environment variables from the .env file are loaded into the application
4. The application then reads the `config.yaml` file for any values not set by environment variables
5. Environment variables always take precedence over values in the config file

## 🔐 Environment Variables

Key environment variables:

- `APP_ENV` - Application environment (development, production)
- `SERVER_PORT` - Server port
- `MONGODB_URI` - MongoDB connection string
- `MONGODB_DATABASE` - MongoDB database name
- `REDIS_ADDRESS` - Redis address
- `REDIS_PASSWORD` - Redis password
- `REDIS_DB` - Redis database number
- `JWT_SECRET_KEY` - JWT secret key
- `JWT_DURATION` - JWT token duration

## 🐳 Docker

The application is containerized using Docker and orchestrated with Docker Compose.

### Development

```bash
make docker-compose-dev-d
```

### Production

```bash
make docker-compose-prod-d
```

## 📝 API Documentation

API documentation is available at `/swagger` when the application is running.

## 🔍 Health Check

The application provides a health check endpoint at `/health`.

## 📊 Monitoring

The application uses Zap for structured logging, which can be integrated with monitoring tools.

## 🔒 Security

- JWT-based authentication
- Environment variable-based secrets management
- Input validation using validator

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Authors

- Your Name - Initial work

## 🙏 Acknowledgments

- Gin Web Framework
- MongoDB Go Driver
- Redis Go Client
- Zap Logger
- Viper Configuration