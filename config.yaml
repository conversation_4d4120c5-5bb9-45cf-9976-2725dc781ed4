
server:
  port: "8080"
  environment: "development"
  read_timeout: 15
  write_timeout: 15
  idle_timeout: 60
mongodb:
  uri: "mongodb://mongodb:27017"
  database: "realestate_dev"
jwt:
  secret_key: "your-secret-key-here"
  duration: 1440 # 24 hours in minutes
redis:
  address: "redis:6379"
  password: ""
  db: 0
notification:
  adminid: "6847d47060d63684707eff87"  # Replace with actual admin ObjectID
