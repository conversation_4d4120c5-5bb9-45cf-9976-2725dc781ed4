version: '3.8'

services:
  app:
    build:
      context: .
      target: production
    ports:
      - "8080:8080"
    env_file:
      - .env.production
    environment:
      - APP_ENV=production
    volumes:
      - uploads-data:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - realestate-network
    restart: unless-stopped

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb-data-prod:/data/db
    networks:
      - realestate-network
    restart: unless-stopped

  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data-prod:/data
    networks:
      - realestate-network
    restart: unless-stopped

networks:
  realestate-network:
    driver: bridge

volumes:
  mongodb-data-prod:
  redis-data-prod:
  uploads-data:
