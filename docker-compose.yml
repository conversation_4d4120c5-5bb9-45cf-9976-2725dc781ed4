version: '3.8'
services:
  app:
    build:
      context: .
      target: development
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=development
    volumes:
      - ./.env.development:/app/.env.development
      - ./uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - realestate-network
  mongodb:
    image: mongo:latest
    ports:
      - "27018:27017"
    volumes:
      - mongodb-data:/data/db
    networks:
      - realestate-network
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - realestate-network
networks:
  realestate-network:
    driver: bridge
volumes:
  mongodb-data:
  redis-data:
