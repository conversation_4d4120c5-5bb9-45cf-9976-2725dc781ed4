# 📄 Create Review API with Image Uploads

## Overview
The Create Review API now supports image uploads using the same multipart/form-data approach as the Create Property API. This allows users to upload review images directly during review creation.

## Endpoint
```
POST /api/v1/reviews
```

## Authentication
✅ **Required** - Bearer Token

## Content Type
```
Content-Type: multipart/form-data
```

## Request Parameters

### Form Data Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `review` | string (JSON) | Yes | Review details as JSON string |
| `images` | file[] | No | Review images (multiple files allowed) |

### Review JSON Structure
```json
{
  "property_id": "507f1f77bcf86cd799439011",
  "rating": 4.5,
  "comment": "Great property with excellent amenities!"
}
```

### Image Upload Details
- **Field Name**: `images` (array of files)
- **Max File Size**: 32MB total
- **Supported Formats**: JPG, JPEG, PNG, GIF
- **Max Files**: No limit (reasonable usage expected)
- **Storage**: `/uploads/reviews/` directory

## Success Response

**Status Code:** `201 Created`

```json
{
  "success": true,
  "message": "review created successfully",
  "review": {
    "id": "507f1f77bcf86cd799439013",
    "user_id": "507f1f77bcf86cd799439012",
    "property_id": "507f1f77bcf86cd799439011",
    "user_name": "John Doe",
    "rating": 4.5,
    "comment": "Great property with excellent amenities!",
    "likes": 0,
    "dislikes": 0,
    "photos": [
      "/uploads/reviews/review_507f1f77bcf86cd799439014_1703123456789_image1.jpg",
      "/uploads/reviews/review_507f1f77bcf86cd799439015_1703123456790_image2.jpg"
    ],
    "is_active": true,
    "created_at": "2023-12-20T10:30:00Z",
    "updated_at": "2023-12-20T10:30:00Z"
  }
}
```

## Error Responses

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| `400` | `"review details are required"` | Missing review JSON in form data |
| `400` | `"invalid review details format"` | Invalid JSON format in review field |
| `400` | `"invalid property ID format"` | Invalid MongoDB ObjectID format |
| `400` | `"failed to parse form data"` | Multipart form parsing failed |
| `401` | `"user not authenticated"` | Missing or invalid authentication token |
| `404` | `"property not found"` | Property doesn't exist |
| `500` | `"failed to create uploads directory"` | Server filesystem error |
| `500` | `"failed to save file"` | Image upload failed |
| `500` | `"failed to create review"` | Database error |

## Postman Setup

### Step 1: Set Request Type
- **Method**: `POST`
- **URL**: `http://localhost:8080/api/v1/reviews`

### Step 2: Set Headers
```
Authorization: Bearer your_jwt_token
```
**Note**: Do NOT set `Content-Type` header manually - Postman will set it automatically for multipart/form-data

### Step 3: Set Body
- **Type**: `form-data`

#### Add Review Details:
| Key | Value | Type |
|-----|-------|------|
| `review` | `{"property_id":"507f1f77bcf86cd799439011","rating":4.5,"comment":"Great property!"}` | Text |

#### Add Images:
| Key | Value | Type |
|-----|-------|------|
| `images` | Select file | File |
| `images` | Select file | File |
| `images` | Select file | File |

**Note**: Add multiple `images` entries for multiple files

## cURL Example

```bash
curl -X POST http://localhost:8080/api/v1/reviews \
  -H "Authorization: Bearer your_jwt_token" \
  -F 'review={"property_id":"507f1f77bcf86cd799439011","rating":4.5,"comment":"Great property with excellent amenities!"}' \
  -F 'images=@/path/to/image1.jpg' \
  -F 'images=@/path/to/image2.jpg'
```

## JavaScript Example

```javascript
const formData = new FormData();

// Add review details
const reviewData = {
  property_id: "507f1f77bcf86cd799439011",
  rating: 4.5,
  comment: "Great property with excellent amenities!"
};
formData.append('review', JSON.stringify(reviewData));

// Add images
const fileInput = document.getElementById('images');
for (let i = 0; i < fileInput.files.length; i++) {
  formData.append('images', fileInput.files[i]);
}

// Make request
fetch('/api/v1/reviews', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

## Implementation Features

### 🚀 **Performance Optimizations**
- ✅ **Concurrent Image Processing**: Multiple images processed in parallel
- ✅ **Unique Filenames**: Timestamp + ObjectID prevents conflicts
- ✅ **Safe Filename Generation**: URL-safe characters, no special chars
- ✅ **Error Handling**: Graceful handling of upload failures
- ✅ **Memory Efficient**: Streaming file uploads

### 🔒 **Security Features**
- ✅ **Authentication Required**: JWT token validation
- ✅ **File Size Limits**: 32MB maximum total upload
- ✅ **Safe File Paths**: Prevents directory traversal attacks
- ✅ **Unique Storage**: Files stored in dedicated review directory

### 📁 **File Management**
- ✅ **Organized Storage**: `/uploads/reviews/` directory
- ✅ **Unique Naming**: `review_{objectid}_{timestamp}_{filename}.ext`
- ✅ **URL Generation**: Proper URL paths for frontend access
- ✅ **Concurrent Safe**: Thread-safe file operations

## Comparison with Property API

| Feature | Property API | Review API |
|---------|-------------|------------|
| **Endpoint** | `POST /properties` | `POST /reviews` |
| **Form Field** | `property` (JSON) | `review` (JSON) |
| **Image Field** | `images[]` | `images` |
| **Storage** | `/uploads/` | `/uploads/reviews/` |
| **Filename Prefix** | `{objectid}_{timestamp}_` | `review_{objectid}_{timestamp}_` |
| **Max Size** | 32MB | 32MB |
| **Concurrency** | ✅ Yes | ✅ Yes |

## Testing Checklist

### ✅ **Basic Functionality**
- [ ] Create review without images
- [ ] Create review with single image
- [ ] Create review with multiple images
- [ ] Verify image URLs in response
- [ ] Check files saved in correct directory

### ✅ **Error Handling**
- [ ] Missing review JSON
- [ ] Invalid JSON format
- [ ] Invalid property ID
- [ ] Missing authentication
- [ ] File upload failures
- [ ] Large file uploads (>32MB)

### ✅ **Security Testing**
- [ ] Unauthorized access attempts
- [ ] Invalid file types
- [ ] Malicious filenames
- [ ] Directory traversal attempts

### ✅ **Performance Testing**
- [ ] Multiple concurrent uploads
- [ ] Large number of images
- [ ] Network interruption handling
- [ ] Memory usage monitoring

## Migration Notes

### For Existing Clients
- **Old API**: Still works for reviews without images
- **New API**: Required for reviews with images
- **Backward Compatibility**: Existing review creation still supported

### Frontend Changes Required
- **Form Type**: Change from JSON to multipart/form-data
- **Data Structure**: Wrap review data in JSON string
- **File Handling**: Add file input and FormData handling
- **Error Handling**: Update for new error messages

The enhanced Create Review API now provides the same robust image upload capabilities as the Property API, ensuring consistency across the platform! 🎉
