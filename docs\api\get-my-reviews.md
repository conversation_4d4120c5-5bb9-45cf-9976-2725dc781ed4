# 📝 Get My Reviews for Property API - User's Reviews for Specific Property

## Overview
The Get My Reviews for Property API allows authenticated users to retrieve all reviews they have written for a specific property. The user ID is automatically extracted from the authentication context (backend) rather than being passed as a query parameter, ensuring security and preventing users from accessing other users' reviews.

## Endpoint
```
GET /api/v1/reviews/my-reviews/property/{propertyId}
```

## Authentication
✅ **Required** - Bearer Token  
✅ **User ID** - Extracted from authentication context (backend)

## Content Type
```
Content-Type: application/json
```

## Request Parameters

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `propertyId` | string | Yes | MongoDB ObjectID of the property |

### Query Parameters (Optional)
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number for pagination |
| `limit` | integer | 10 | Number of reviews per page (max: 100) |

## Security Features

### 🔒 **Backend User ID Extraction**
- ✅ **No Query Params**: User ID is NOT passed in URL or query parameters
- ✅ **Authentication Context**: User ID extracted from JWT token in backend
- ✅ **Secure**: Users can only see their own reviews
- ✅ **Admin Override**: Admins can see inactive reviews too

### 🛡️ **Authorization Logic**
```go
// User ID extracted from authentication context
userID, exists := c.Get("user_id")
reviewAuthorID := userID.(primitive.ObjectID)

// User can only see their own reviews
// Admin can see inactive reviews too
```

## Success Response

**Status Code:** `200 OK`

```json
{
  "data": [
    {
      "id": "507f1f77bcf86cd799439013",
      "user_id": "507f1f77bcf86cd799439012",
      "property_id": "507f1f77bcf86cd799439011",
      "user_name": "John Doe",
      "rating": 4.5,
      "comment": "Great property with excellent amenities! I really enjoyed my stay here.",
      "likes": 15,
      "dislikes": 2,
      "photos": [
        "/uploads/reviews/review_507f1f77bcf86cd799439014_1703123456789_image1.jpg",
        "/uploads/reviews/review_507f1f77bcf86cd799439015_1703123456790_image2.jpg"
      ],
      "is_active": true,
      "created_at": "2023-12-20T10:30:00Z",
      "updated_at": "2023-12-20T11:45:00Z",
      "is_liked": true,
      "is_disliked": false
    },
    {
      "id": "507f1f77bcf86cd799439016",
      "user_id": "507f1f77bcf86cd799439012",
      "property_id": "507f1f77bcf86cd799439017",
      "user_name": "John Doe",
      "rating": 3.5,
      "comment": "Good location but needs some improvements.",
      "likes": 8,
      "dislikes": 1,
      "photos": [],
      "is_active": true,
      "created_at": "2023-12-19T15:20:00Z",
      "updated_at": "2023-12-19T15:20:00Z",
      "is_liked": false,
      "is_disliked": false
    }
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false
  }
}
```

## Response Fields Explained

### Review Fields
- **`id`**: Review's unique identifier
- **`user_id`**: Always matches the authenticated user's ID
- **`property_id`**: Property that was reviewed
- **`user_name`**: User's display name
- **`rating`**: Rating given (1.0 to 5.0)
- **`comment`**: Review text content
- **`likes`**: Total number of likes received
- **`dislikes`**: Total number of dislikes received
- **`photos`**: Array of review image URLs
- **`is_active`**: Whether review is active (admins see inactive too)
- **`created_at`**: When review was created
- **`updated_at`**: When review was last modified

### Like Status Fields
- **`is_liked`**: Whether user liked their own review (usually false)
- **`is_disliked`**: Whether user disliked their own review (usually false)

## Error Responses

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| `401` | `"user not authenticated"` | Missing or invalid authentication token |
| `500` | `"failed to list user reviews"` | Database or server error |

## Postman Setup

### Step 1: Set Request Type
- **Method**: `GET`
- **URL**: `http://localhost:8080/api/v1/reviews/my-reviews/property/{propertyId}`

### Step 2: Set Headers
```
Authorization: Bearer your_jwt_token
```

### Step 3: Add Query Parameters (Optional)
In Postman's **Params** tab:

| Key | Value | Description |
|-----|-------|-------------|
| `page` | `1` | Page number |
| `limit` | `20` | Reviews per page |

## cURL Examples

### Example 1: Get My Reviews for Property (Default Pagination)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer your_jwt_token"
```

### Example 2: Get My Reviews for Property with Custom Pagination
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews/property/507f1f77bcf86cd799439011?page=2&limit=5" \
  -H "Authorization: Bearer your_jwt_token"
```

### Example 3: Get All My Reviews for Property (Large Limit)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews/property/507f1f77bcf86cd799439011?limit=100" \
  -H "Authorization: Bearer your_jwt_token"
```

## JavaScript Example

```javascript
// Function to get current user's reviews for a specific property
async function getMyReviewsForProperty(propertyId, page = 1, limit = 10) {
  const token = localStorage.getItem('authToken');
  
  if (!token) {
    throw new Error('Authentication required');
  }
  
  try {
    const response = await fetch(
      `/api/v1/reviews/my-reviews/property/${propertyId}?page=${page}&limit=${limit}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log(`Found ${data.pagination.total} reviews by you for property ${propertyId}`);
    data.data.forEach(review => {
      console.log(`Review ID: ${review.id}`);
      console.log(`Rating: ${review.rating}/5`);
      console.log(`Comment: ${review.comment}`);
      console.log(`Likes: ${review.likes}, Dislikes: ${review.dislikes}`);
      console.log(`Created: ${new Date(review.created_at).toLocaleDateString()}`);
      console.log('---');
    });
    
    return data;
  } catch (error) {
    console.error('Error fetching my reviews:', error);
    throw error;
  }
}

// Usage examples
getMyReviewsForProperty('507f1f77bcf86cd799439011');                    // First page, 10 items
getMyReviewsForProperty('507f1f77bcf86cd799439011', 2, 20);              // Page 2, 20 items
getMyReviewsForProperty('507f1f77bcf86cd799439011', 1, 100);             // All reviews (up to 100)
```

## React Component Example

```jsx
import React, { useState, useEffect } from 'react';

const MyReviewsList = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    fetchMyReviews(currentPage);
  }, [currentPage]);

  const fetchMyReviews = async (page = 1) => {
    setLoading(true);
    const token = localStorage.getItem('authToken');
    
    try {
      const response = await fetch(
        `/api/v1/reviews/my-reviews?page=${page}&limit=10`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      const data = await response.json();
      setReviews(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  if (loading) return <div>Loading your reviews...</div>;

  return (
    <div className="my-reviews">
      <h2>My Reviews ({pagination.total})</h2>
      
      {reviews.length === 0 ? (
        <p>You haven't written any reviews yet.</p>
      ) : (
        <>
          {reviews.map(review => (
            <div key={review.id} className="review-card">
              <div className="review-header">
                <span className="rating">★ {review.rating}/5</span>
                <span className="date">
                  {new Date(review.created_at).toLocaleDateString()}
                </span>
              </div>
              
              <p className="comment">{review.comment}</p>
              
              <div className="review-stats">
                <span>👍 {review.likes} likes</span>
                <span>👎 {review.dislikes} dislikes</span>
                <span className={`status ${review.is_active ? 'active' : 'inactive'}`}>
                  {review.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              
              {review.photos.length > 0 && (
                <div className="review-photos">
                  {review.photos.map((photo, index) => (
                    <img 
                      key={index} 
                      src={`http://localhost:8080${photo}`} 
                      alt={`Review photo ${index + 1}`}
                      className="review-photo"
                    />
                  ))}
                </div>
              )}
              
              <div className="review-actions">
                <button onClick={() => editReview(review.id)}>
                  Edit Review
                </button>
                <button onClick={() => deleteReview(review.id)}>
                  Delete Review
                </button>
              </div>
            </div>
          ))}
          
          {/* Pagination */}
          <div className="pagination">
            {pagination.has_previous && (
              <button onClick={() => handlePageChange(currentPage - 1)}>
                Previous
              </button>
            )}
            
            <span>Page {pagination.page} of {pagination.total_pages}</span>
            
            {pagination.has_next && (
              <button onClick={() => handlePageChange(currentPage + 1)}>
                Next
              </button>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default MyReviewsList;
```

## Use Cases

### 1. **User Dashboard**
```javascript
// Show user's review statistics
const myReviews = await getMyReviews(1, 100);
const totalReviews = myReviews.pagination.total;
const averageRating = myReviews.data.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
const totalLikes = myReviews.data.reduce((sum, r) => sum + r.likes, 0);

console.log(`You've written ${totalReviews} reviews`);
console.log(`Your average rating: ${averageRating.toFixed(1)}/5`);
console.log(`Total likes received: ${totalLikes}`);
```

### 2. **Review Management**
```javascript
// Find reviews that need attention
const myReviews = await getMyReviews();
const lowRatedReviews = myReviews.data.filter(r => r.rating < 3);
const unpopularReviews = myReviews.data.filter(r => r.likes < r.dislikes);

console.log(`${lowRatedReviews.length} reviews with low ratings`);
console.log(`${unpopularReviews.length} reviews with more dislikes than likes`);
```

### 3. **Content Analysis**
```javascript
// Analyze review content
const myReviews = await getMyReviews(1, 100);
const reviewsWithPhotos = myReviews.data.filter(r => r.photos.length > 0);
const recentReviews = myReviews.data.filter(r => {
  const reviewDate = new Date(r.created_at);
  const monthAgo = new Date();
  monthAgo.setMonth(monthAgo.getMonth() - 1);
  return reviewDate > monthAgo;
});

console.log(`${reviewsWithPhotos.length} reviews have photos`);
console.log(`${recentReviews.length} reviews written in the last month`);
```

## Performance Features

### 🚀 **Optimized for High Traffic**
- ✅ **Batch Operations**: Single query for reviews + single query for like status
- ✅ **Indexed Queries**: Uses optimized database indexes
- ✅ **Pagination**: Efficient handling of large review lists
- ✅ **Memory Efficient**: Pre-allocated data structures

### 📊 **Database Optimization**
```javascript
// Optimized indexes for user reviews
db.reviews.createIndex({
  "user_id": 1, 
  "created_at": -1 
});

db.review_likes.createIndex({
  "review_id": 1, 
  "user_id": 1, 
  "type": 1 
});
```

## Testing Examples

### Test 1: Basic Functionality
```bash
# Login first
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  | jq -r '.token')

# Get my reviews
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews" \
  -H "Authorization: Bearer $TOKEN"
```

### Test 2: Pagination
```bash
# Test different page sizes
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews?page=1&limit=5" \
  -H "Authorization: Bearer $TOKEN"

curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews?page=2&limit=10" \
  -H "Authorization: Bearer $TOKEN"
```

### Test 3: Security (Should Fail)
```bash
# Test without authentication
curl -X GET "http://localhost:8080/api/v1/reviews/my-reviews"
# Expected: 401 Unauthorized
```

## Summary

The Get My Reviews API provides:

1. **🔒 Secure Access**: User ID extracted from authentication context
2. **📝 Personal Reviews**: Shows only the authenticated user's reviews
3. **🎯 Like Status**: Includes user's own like/dislike status
4. **📄 Pagination**: Efficient handling of large review lists
5. **⚡ Performance**: Optimized for high traffic with batch operations
6. **👑 Admin Support**: Admins can see inactive reviews too
7. **📱 UI Ready**: Perfect for user dashboards and profile pages

Perfect for building user profile pages, review management interfaces, and personal review analytics! 🎉
