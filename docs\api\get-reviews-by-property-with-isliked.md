# 📝 Get Reviews by Property ID API with isLiked Field

## Overview
The Get Reviews by Property ID API has been enhanced to include the `isLiked` field for each review, indicating whether the current logged-in user has liked that specific review. This provides personalized review data for better user experience.

## Endpoint
```
GET /api/v1/reviews/property/{propertyId}
```

## Authentication
✅ **Optional** - Bear<PERSON> (for `isLiked` field)  
✅ **Public Access** - Works without authentication (isLiked will be false)

## Content Type
```
Content-Type: application/json
```

## Request Parameters

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `propertyId` | string | Yes | MongoDB ObjectID of the property |

### Query Parameters (Optional)
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | integer | 1 | Page number for pagination |
| `limit` | integer | 10 | Number of reviews per page |

## Enhanced Response with isLiked Field

**Status Code:** `200 OK`

```json
{
  "data": [
    {
      "id": "507f1f77bcf86cd799439013",
      "user_id": "507f1f77bcf86cd799439012",
      "property_id": "507f1f77bcf86cd799439011",
      "user_name": "John Doe",
      "rating": 4.5,
      "comment": "Great property with excellent amenities!",
      "likes": 15,
      "dislikes": 2,
      "photos": [
        "/uploads/reviews/review_507f1f77bcf86cd799439014_1703123456789_image1.jpg",
        "/uploads/reviews/review_507f1f77bcf86cd799439015_1703123456790_image2.jpg"
      ],
      "is_active": true,
      "created_at": "2023-12-20T10:30:00Z",
      "updated_at": "2023-12-20T11:45:00Z",
      "is_liked": true,
      "is_disliked": false,
      "user_profile_photo": "/uploads/profiles/profile_abc123_1703123456789_john_doe.jpg"
    },
    {
      "id": "507f1f77bcf86cd799439016",
      "user_id": "507f1f77bcf86cd799439017",
      "property_id": "507f1f77bcf86cd799439011",
      "user_name": "Jane Smith",
      "rating": 3.5,
      "comment": "Good location but needs some improvements.",
      "likes": 8,
      "dislikes": 1,
      "photos": [],
      "is_active": true,
      "created_at": "2023-12-19T15:20:00Z",
      "updated_at": "2023-12-19T15:20:00Z",
      "is_liked": false,
      "is_disliked": true,
      "user_profile_photo": ""
    }
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 10,
    "total_pages": 3,
    "has_next": true,
    "has_previous": false
  }
}
```

## New Fields Explained

### isLiked Field
- **Type**: `boolean`
- **Description**: Indicates if the current logged-in user has liked this specific review
- **Values**:
  - `true`: User has liked this review
  - `false`: User has not liked this review or is not logged in

### isDisliked Field
- **Type**: `boolean`
- **Description**: Indicates if the current logged-in user has disliked this specific review
- **Values**:
  - `true`: User has disliked this review
  - `false`: User has not disliked this review or is not logged in

## Authentication Scenarios

### 1. Authenticated User (With Token)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer your_jwt_token"
```

**Response**: Reviews with personalized `isLiked` and `isDisliked` fields

### 2. Unauthenticated User (No Token)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011"
```

**Response**: Reviews with `isLiked: false` and `isDisliked: false` for all reviews

## Postman Setup

### Step 1: Set Request Type
- **Method**: `GET`
- **URL**: `http://localhost:8080/api/v1/reviews/property/{propertyId}`

### Step 2: Set Headers (Optional for isLiked)
```
Authorization: Bearer your_jwt_token
```

### Step 3: Add Query Parameters (Optional)
In Postman's **Params** tab:

| Key | Value | Description |
|-----|-------|-------------|
| `page` | `1` | Page number |
| `limit` | `20` | Reviews per page |

## cURL Examples

### Example 1: Get Reviews with Like Status (Authenticated)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer your_jwt_token"
```

### Example 2: Get Reviews without Like Status (Public)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011"
```

### Example 3: Get Reviews with Pagination
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011?page=2&limit=5" \
  -H "Authorization: Bearer your_jwt_token"
```

### Example 4: Admin View (Shows Inactive Reviews)
```bash
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer your_admin_token"
```

## JavaScript Example

```javascript
// Function to get reviews with like status
async function getReviewsWithLikeStatus(propertyId, page = 1, limit = 10) {
  const token = localStorage.getItem('authToken'); // Get user token
  
  const headers = {
    'Content-Type': 'application/json'
  };
  
  // Add authorization header if user is logged in
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  try {
    const response = await fetch(
      `/api/v1/reviews/property/${propertyId}?page=${page}&limit=${limit}`,
      {
        method: 'GET',
        headers: headers
      }
    );
    
    const data = await response.json();
    
    // Process reviews with like status
    data.data.forEach(review => {
      console.log(`Review by ${review.user_name}:`);
      console.log(`Rating: ${review.rating}/5`);
      console.log(`Liked by you: ${review.is_liked}`);
      console.log(`Disliked by you: ${review.is_disliked}`);
      console.log(`Total likes: ${review.likes}`);
      console.log('---');
    });
    
    return data;
  } catch (error) {
    console.error('Error fetching reviews:', error);
  }
}

// Usage examples
getReviewsWithLikeStatus('507f1f77bcf86cd799439011'); // First page
getReviewsWithLikeStatus('507f1f77bcf86cd799439011', 2, 20); // Page 2, 20 items
```

## React Component Example

```jsx
import React, { useState, useEffect } from 'react';

const ReviewsList = ({ propertyId }) => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});

  useEffect(() => {
    fetchReviews();
  }, [propertyId]);

  const fetchReviews = async (page = 1) => {
    setLoading(true);
    const token = localStorage.getItem('authToken');
    
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(
        `/api/v1/reviews/property/${propertyId}?page=${page}&limit=10`,
        { headers }
      );
      
      const data = await response.json();
      setReviews(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (reviewId) => {
    const token = localStorage.getItem('authToken');
    if (!token) {
      alert('Please login to like reviews');
      return;
    }

    try {
      await fetch(`/api/v1/reviews/${reviewId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // Refresh reviews to update like status
      fetchReviews();
    } catch (error) {
      console.error('Error liking review:', error);
    }
  };

  if (loading) return <div>Loading reviews...</div>;

  return (
    <div className="reviews-list">
      <h3>Reviews ({pagination.total})</h3>
      
      {reviews.map(review => (
        <div key={review.id} className="review-card">
          <div className="review-header">
            <span className="user-name">{review.user_name}</span>
            <span className="rating">★ {review.rating}/5</span>
          </div>
          
          <p className="comment">{review.comment}</p>
          
          <div className="review-actions">
            <button 
              className={`like-btn ${review.is_liked ? 'liked' : ''}`}
              onClick={() => handleLike(review.id)}
            >
              👍 {review.likes} {review.is_liked && '(You liked this)'}
            </button>
            
            <span className="dislikes">
              👎 {review.dislikes} {review.is_disliked && '(You disliked this)'}
            </span>
          </div>
          
          {review.photos.length > 0 && (
            <div className="review-photos">
              {review.photos.map((photo, index) => (
                <img 
                  key={index} 
                  src={`http://localhost:8080${photo}`} 
                  alt={`Review photo ${index + 1}`}
                  className="review-photo"
                />
              ))}
            </div>
          )}
        </div>
      ))}
      
      {/* Pagination */}
      <div className="pagination">
        {pagination.has_previous && (
          <button onClick={() => fetchReviews(pagination.page - 1)}>
            Previous
          </button>
        )}
        
        <span>Page {pagination.page} of {pagination.total_pages}</span>
        
        {pagination.has_next && (
          <button onClick={() => fetchReviews(pagination.page + 1)}>
            Next
          </button>
        )}
      </div>
    </div>
  );
};

export default ReviewsList;
```

## Error Responses

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| `400` | `"invalid property ID format"` | Invalid MongoDB ObjectID format |
| `500` | `"failed to list reviews"` | Database or server error |

## Performance Considerations

### Like Status Checking
- **Efficient Queries**: Uses indexed MongoDB queries for like status
- **Batch Processing**: Checks like status for all reviews in single operation
- **Optional Feature**: Only processes like status if user is authenticated

### Caching Recommendations
```javascript
// Cache reviews for short periods
const CACHE_DURATION = 2 * 60 * 1000; // 2 minutes
let reviewsCache = new Map();

async function getCachedReviews(propertyId, page = 1) {
  const cacheKey = `${propertyId}_${page}`;
  const cached = reviewsCache.get(cacheKey);
  
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }
  
  const data = await fetchReviews(propertyId, page);
  reviewsCache.set(cacheKey, {
    data: data,
    timestamp: Date.now()
  });
  
  return data;
}
```

## Testing Scenarios

### Test 1: Authenticated User
```bash
# Login first
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}' \
  | jq -r '.token')

# Get reviews with like status
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer $TOKEN"
```

### Test 2: Unauthenticated User
```bash
# Get reviews without like status
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011"
```

### Test 3: Like a Review and Check Status
```bash
# Like a review
curl -X POST "http://localhost:8080/api/v1/reviews/507f1f77bcf86cd799439013/like" \
  -H "Authorization: Bearer $TOKEN"

# Check if isLiked is now true
curl -X GET "http://localhost:8080/api/v1/reviews/property/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer $TOKEN"
```

## Summary

The enhanced Get Reviews by Property ID API now provides:

1. **🎯 Personalized Experience**: `isLiked` field shows user's interaction with each review
2. **🔓 Public Access**: Works without authentication (isLiked defaults to false)
3. **⚡ Performance**: Efficient batch checking of like status
4. **📱 UI Ready**: Perfect for building interactive review interfaces
5. **🔄 Real-time**: Reflects current user's like/dislike status

Perfect for building engaging review systems with personalized user interactions! 🎉
