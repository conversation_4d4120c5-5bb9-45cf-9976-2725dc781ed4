# 📄 Profile APIs with Image Upload

## Overview
The Complete Profile and Update Profile APIs now support profile image uploads using the same multipart/form-data approach as Property and Review APIs. This allows users to upload their profile images directly during profile completion or updates.

## Endpoints

### 1. Complete Profile with Image Upload
```
POST /api/v1/user/complete-profile
```

### 2. Update Profile with Image Upload
```
PUT /api/v1/user/profile
```

## Authentication
✅ **Required** - <PERSON><PERSON>ken

## Content Type
```
Content-Type: multipart/form-data
```

## Request Parameters

### Form Data Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `profile` | string (JSON) | Yes | Profile details as JSON string |
| `profile_image` | file | No | Profile image (single file) |

### Complete Profile JSON Structure
```json
{
  "phn_number": "+91 9876543210",
  "address": "123 Main Street",
  "city": "Mumbai",
  "state": "Maharashtra",
  "country": "India",
  "zip_code": "400001"
}
```

### Update Profile JSON Structure
```json
{
  "first_name": "<PERSON>",
  "last_name": "Doe",
  "phn_number": "+91 9876543210",
  "birth_date": "1990-01-15",
  "location": {
    "address": "123 Main Street",
    "city": "Mumbai",
    "state": "Maharashtra",
    "country": "India",
    "zip_code": "400001",
    "latitude": 19.0760,
    "longitude": 72.8777,
    "neighborhood": "Bandra"
  }
}
```

### Profile Image Upload Details
- **Field Name**: `profile_image` (single file)
- **Max File Size**: 32MB
- **Supported Formats**: JPG, JPEG, PNG, GIF
- **Storage**: `/uploads/profiles/` directory
- **Filename Format**: `profile_{user_id}_{timestamp}_{original_name}.ext`

## Success Responses

### Complete Profile Response
**Status Code:** `200 OK`

```json
{
  "user": {
    "id": "507f1f77bcf86cd799439012",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "user",
    "phn_number": "+91 9876543210",
    "birth_date": "1990-01-15T00:00:00Z",
    "location": {
      "address": "123 Main Street",
      "city": "Mumbai",
      "area": "",
      "state": "Maharashtra",
      "country": "India",
      "zip_code": "400001",
      "latitude": 0,
      "longitude": 0,
      "neighborhood": ""
    },
    "profile_photo": "/uploads/profiles/profile_507f1f77bcf86cd799439012_1703123456789_john_profile.jpg",
    "is_profile_complete": true,
    "is_subscribed_user": false,
    "is_blocked": false,
    "total_listed_properties": 0,
    "is_document_verified": false,
    "created_at": "2023-12-20T10:00:00Z",
    "updated_at": "2023-12-20T10:30:00Z"
  }
}
```

### Update Profile Response
**Status Code:** `200 OK`

```json
{
  "user": {
    "id": "507f1f77bcf86cd799439012",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "profile_photo": "/uploads/profiles/profile_507f1f77bcf86cd799439012_1703123456790_updated_profile.jpg",
    // ... other user fields
  }
}
```

## Error Responses

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| `400` | `"profile details are required"` | Missing profile JSON in form data |
| `400` | `"invalid profile details format"` | Invalid JSON format in profile field |
| `400` | `"failed to parse form data"` | Multipart form parsing failed |
| `400` | `"failed to get profile image"` | Profile image upload error |
| `401` | `"Unauthorized"` | Missing or invalid authentication token |
| `500` | `"failed to create uploads directory"` | Server filesystem error |
| `500` | `"failed to save profile image"` | Image upload failed |
| `500` | `"Failed to update your profile"` | Database error |

## Postman Setup

### Complete Profile API

#### Step 1: Set Request Type
- **Method**: `POST`
- **URL**: `http://localhost:8080/api/v1/user/complete-profile`

#### Step 2: Set Headers
```
Authorization: Bearer your_jwt_token
```

#### Step 3: Set Body
- **Type**: `form-data`

| Key | Value | Type |
|-----|-------|------|
| `profile` | `{"phn_number":"+91 9876543210","address":"123 Main Street","city":"Mumbai","state":"Maharashtra","country":"India","zip_code":"400001"}` | Text |
| `profile_image` | Select file | File |

### Update Profile API

#### Step 1: Set Request Type
- **Method**: `PUT`
- **URL**: `http://localhost:8080/api/v1/user/profile`

#### Step 2: Set Headers
```
Authorization: Bearer your_jwt_token
```

#### Step 3: Set Body
- **Type**: `form-data`

| Key | Value | Type |
|-----|-------|------|
| `profile` | `{"first_name":"John","last_name":"Doe","phn_number":"+91 9876543210"}` | Text |
| `profile_image` | Select file | File |

## cURL Examples

### Complete Profile
```bash
curl -X POST http://localhost:8080/api/v1/user/complete-profile \
  -H "Authorization: Bearer your_jwt_token" \
  -F 'profile={"phn_number":"+91 9876543210","address":"123 Main Street","city":"Mumbai","state":"Maharashtra","country":"India","zip_code":"400001"}' \
  -F 'profile_image=@/path/to/profile.jpg'
```

### Update Profile
```bash
curl -X PUT http://localhost:8080/api/v1/user/profile \
  -H "Authorization: Bearer your_jwt_token" \
  -F 'profile={"first_name":"John","last_name":"Doe"}' \
  -F 'profile_image=@/path/to/new_profile.jpg'
```

## Implementation Features

### 🚀 **Performance & Security**
- ✅ **Single File Upload**: Optimized for profile images (one file per user)
- ✅ **Unique Filenames**: `profile_{user_id}_{timestamp}_{filename}.ext`
- ✅ **Safe File Handling**: URL-safe characters, no directory traversal
- ✅ **Error Handling**: Graceful handling of upload failures
- ✅ **Optional Upload**: Profile image is optional in both APIs

### 🔒 **Security Features**
- ✅ **Authentication Required**: JWT token validation
- ✅ **File Size Limits**: 32MB maximum upload
- ✅ **Safe File Paths**: Prevents security vulnerabilities
- ✅ **User-Specific Storage**: Files organized by user ID

### 📁 **File Management**
- ✅ **Organized Storage**: `/uploads/profiles/` directory
- ✅ **Unique Naming**: User ID + timestamp prevents conflicts
- ✅ **URL Generation**: Proper URL paths for frontend access
- ✅ **Overwrite Protection**: New uploads don't overwrite existing files

## API Comparison

| Feature | Property API | Review API | Profile API |
|---------|-------------|------------|-------------|
| **Endpoint** | `POST /properties` | `POST /reviews` | `POST /user/complete-profile` |
| **Form Field** | `property` (JSON) | `review` (JSON) | `profile` (JSON) |
| **Image Field** | `images[]` (multiple) | `images` (multiple) | `profile_image` (single) |
| **Storage** | `/uploads/` | `/uploads/reviews/` | `/uploads/profiles/` |
| **Filename** | `{objectid}_{timestamp}_` | `review_{objectid}_{timestamp}_` | `profile_{user_id}_{timestamp}_` |
| **Max Files** | Multiple | Multiple | Single |
| **Use Case** | Property photos | Review photos | User profile picture |

## Testing Checklist

### ✅ **Complete Profile API**
- [ ] Complete profile without image
- [ ] Complete profile with image
- [ ] Verify image URL in response
- [ ] Check file saved in `/uploads/profiles/`
- [ ] Test with various image formats

### ✅ **Update Profile API**
- [ ] Update profile without image
- [ ] Update profile with new image
- [ ] Update only profile data (no image)
- [ ] Update only image (minimal profile data)
- [ ] Verify old image is not overwritten

### ✅ **Error Handling**
- [ ] Missing profile JSON
- [ ] Invalid JSON format
- [ ] Missing authentication
- [ ] File upload failures
- [ ] Large file uploads (>32MB)

### ✅ **Security Testing**
- [ ] Unauthorized access attempts
- [ ] Invalid file types
- [ ] Malicious filenames
- [ ] Directory traversal attempts

## Migration Notes

### For Existing Clients
- **Old APIs**: Still work for profile updates without images
- **New APIs**: Required for profile image uploads
- **Backward Compatibility**: Existing profile completion/update still supported

### Frontend Changes Required
- **Form Type**: Change from JSON to multipart/form-data
- **Data Structure**: Wrap profile data in JSON string
- **File Handling**: Add file input for profile image
- **Error Handling**: Update for new error messages

The enhanced Profile APIs now provide robust image upload capabilities, ensuring consistency with Property and Review APIs across the platform! 🎉
