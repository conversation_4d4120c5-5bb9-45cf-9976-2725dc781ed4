# 🔄 Update Facility API with Intelligent Icon Management

## Overview
The Update Facility API has been enhanced with intelligent icon management, similar to the Update Profile and Edit Review APIs. It now supports multipart/form-data uploads with smart duplicate detection and automatic cleanup of old icons.

## Endpoint
```
PUT /api/v1/admin/facilities/{id}
```

## Authentication
✅ **Required** - Bearer Token  
✅ **Authorization** - Admin role required

## Content Type
```
Content-Type: multipart/form-data
```

## Request Parameters

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | MongoDB ObjectID of the facility to update |

### Form Data Fields
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `facility` | string (JSON) | Yes | Facility details as JSO<PERSON> string |
| `icon` | file | No | Facility icon image file |

### Facility JSON Structure
```json
{
  "name": "Updated Swimming Pool",
  "available": true,
  "active": true
}
```

### Icon Upload Details
- **Field Name**: `icon` (single file)
- **Max File Size**: 32MB
- **Supported Formats**: JPG, JPEG, PNG, GIF, SVG
- **Storage**: `/uploads/facilities/` directory
- **Filename Format**: `facility_{random_objectid}_{timestamp}_{safe_filename}.ext`

## Smart Icon Management Features

### 🔄 **Intelligent Processing**
- ✅ **Duplicate Detection**: Skips processing if same icon is uploaded
- ✅ **Automatic Cleanup**: Deletes old icon when new one replaces it
- ✅ **Selective Updates**: Only processes changed icons
- ✅ **Optional Upload**: Can update text fields without changing icon

### 📁 **Icon Handling Logic**
1. **Compare uploaded icon** with existing facility icon
2. **Skip duplicate icons** to save processing time
3. **Process only new/changed icons** with unique filenames
4. **Delete old icon** that is no longer used
5. **Update facility** with final icon URL

## Success Response

**Status Code:** `200 OK`

```json
{
  "message": "facility updated successfully"
}
```

## Error Responses

| Status Code | Error Message | Description |
|-------------|---------------|-------------|
| `400` | `"invalid facility id"` | Invalid MongoDB ObjectID format |
| `400` | `"facility details are required"` | Missing facility JSON in form data |
| `400` | `"invalid facility details format"` | Invalid JSON format in facility field |
| `400` | `"failed to parse form data"` | Multipart form parsing failed |
| `401` | `"user not authenticated"` | Missing or invalid authentication token |
| `403` | `"admin access required"` | User is not an admin |
| `404` | `"facility not found"` | Facility doesn't exist |
| `500` | `"failed to create uploads directory"` | Server filesystem error |
| `500` | `"failed to save facility icon"` | Icon upload failed |
| `500` | `"server error"` | Database or other server error |

## Postman Setup

### Step 1: Set Request Type
- **Method**: `PUT`
- **URL**: `http://localhost:8080/api/v1/admin/facilities/{facility_id}`

### Step 2: Set Headers
```
Authorization: Bearer your_admin_jwt_token
```
**⚠️ Important**: Do NOT set `Content-Type` header manually

### Step 3: Set Body
- **Type**: `form-data`

#### Add Facility Details:
| Key | Value | Type |
|-----|-------|------|
| `facility` | `{"name":"Updated Pool","available":true,"active":true}` | Text |

#### Add Icon File (Optional):
| Key | Value | Type |
|-----|-------|------|
| `icon` | Select new icon file | File |

## cURL Examples

### Example 1: Update Text Only (No Icon Change)
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer your_admin_token" \
  -F 'facility={"name":"Updated Swimming Pool","available":false,"active":true}'
```

### Example 2: Update with New Icon
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer your_admin_token" \
  -F 'facility={"name":"Updated Pool","available":true,"active":true}' \
  -F 'icon=@new_pool_icon.png'
```

### Example 3: Update Icon Only (Keep Same Name)
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer your_admin_token" \
  -F 'facility={}' \
  -F 'icon=@updated_icon.svg'
```

## JavaScript Example

```javascript
const formData = new FormData();

// Add facility details
const facilityData = {
  name: "Updated Swimming Pool",
  available: true,
  active: true
};
formData.append('facility', JSON.stringify(facilityData));

// Add icon file (optional)
const iconInput = document.getElementById('icon');
if (iconInput.files[0]) {
  formData.append('icon', iconInput.files[0]);
}

// Make request
fetch('/api/v1/admin/facilities/507f1f77bcf86cd799439013', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer ' + adminToken
  },
  body: formData
})
.then(response => response.json())
.then(data => console.log(data));
```

## Icon Management Scenarios

### **Scenario 1: Update text only (no icon)**
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer $TOKEN" \
  -F 'facility={"name":"Updated Name","available":false}'
```
**Result**: Text updated, existing icon preserved

### **Scenario 2: Upload new icon**
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer $TOKEN" \
  -F 'facility={"name":"Pool with New Icon"}' \
  -F 'icon=@new_icon.png'
```
**Result**: Old icon deleted, new icon saved

### **Scenario 3: Upload same icon (duplicate detection)**
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer $TOKEN" \
  -F 'facility={"name":"Same Icon Again"}' \
  -F 'icon=@existing_icon.png'
```
**Result**: Processing skipped, existing icon URL preserved

### **Scenario 4: Replace icon with different format**
```bash
curl -X PUT http://localhost:8080/api/v1/admin/facilities/507f1f77bcf86cd799439013 \
  -H "Authorization: Bearer $TOKEN" \
  -F 'facility={"active":true}' \
  -F 'icon=@icon.svg'  # PNG to SVG
```
**Result**: Old PNG deleted, new SVG saved

## Field Validation

### Optional Fields (All fields are optional for updates)
- **name**: Facility name (string)
- **available**: Whether facility is available (boolean)
- **active**: Whether facility is active (boolean)
- **icon**: Icon file (file)

### Update Examples
```json
// Update name only
{"name": "New Facility Name"}

// Update availability only
{"available": false}

// Update multiple fields
{"name": "Updated Pool", "available": true, "active": false}

// Empty update (only icon change)
{}
```

## Implementation Features

### 🚀 **Performance Optimizations**
- ✅ **Duplicate Detection**: Skips processing for unchanged icons
- ✅ **Selective Updates**: Only processes new/changed icons
- ✅ **Efficient Cleanup**: Removes only replaced old icons
- ✅ **Optional Processing**: Can update without icon changes

### 🔒 **Security Features**
- ✅ **Admin Only**: Requires admin authentication
- ✅ **File Size Limits**: 32MB maximum upload
- ✅ **Safe File Paths**: Prevents directory traversal attacks
- ✅ **Validation**: Comprehensive input validation

### 📁 **File Management**
- ✅ **Organized Storage**: `/uploads/facilities/` directory
- ✅ **Unique Naming**: `facility_{objectid}_{timestamp}_{filename}.ext`
- ✅ **Automatic Cleanup**: Old unused icons deleted
- ✅ **Consistent URLs**: Forward slash format for web compatibility

## API Consistency

| Feature | Update Profile | Edit Review | Update Facility |
|---------|----------------|-------------|-----------------|
| **Content Type** | multipart/form-data | multipart/form-data | multipart/form-data |
| **Form Field** | `profile` (JSON) | `review` (JSON) | `facility` (JSON) |
| **File Field** | `profile_image` (single) | `images` (multiple) | `icon` (single) |
| **Duplicate Detection** | ✅ Yes | ✅ Yes | ✅ Yes |
| **Automatic Cleanup** | ✅ Yes | ✅ Yes | ✅ Yes |
| **Optional Upload** | ✅ Yes | ✅ Yes | ✅ Yes |

## Testing Checklist

### ✅ **Basic Functionality**
- [ ] Update facility without icon
- [ ] Update facility with same icon (duplicate detection)
- [ ] Update facility with new icon (cleanup old one)
- [ ] Update only icon (keep other fields)
- [ ] Verify icon URLs in database

### ✅ **Authorization Testing**
- [ ] Admin updating facility (should succeed)
- [ ] Non-admin updating facility (should fail)
- [ ] Unauthenticated update attempt (should fail)

### ✅ **Error Handling**
- [ ] Invalid facility ID format
- [ ] Missing facility JSON
- [ ] Invalid JSON format
- [ ] File upload failures
- [ ] Large file uploads (>32MB)
- [ ] Non-existent facility ID

### ✅ **Icon Management**
- [ ] Verify old icon deleted when replaced
- [ ] Verify duplicate icon skipped
- [ ] Check file system for proper cleanup
- [ ] Test with various icon formats (PNG, JPG, SVG)

## Migration Notes

### **Backward Compatibility**
- ✅ **Old JSON API**: Still works for text-only updates
- ✅ **New Multipart API**: Required for icon updates
- ✅ **Gradual Migration**: Clients can migrate at their own pace

### **Frontend Changes Required**
- **Form Type**: Change from JSON to multipart/form-data for icon updates
- **Data Structure**: Wrap facility data in JSON string
- **File Handling**: Add file input and FormData handling
- **Error Handling**: Update for new error messages

## Summary

The enhanced Update Facility API now provides:

1. **🔄 Intelligent Icon Management**: Same smart handling as other APIs
2. **🚀 Performance Optimized**: Duplicate detection and selective processing
3. **🛡️ Robust Error Handling**: Comprehensive validation and cleanup
4. **📱 Consistent Design**: Matches Update Profile and Edit Review patterns
5. **🔧 Flexible Updates**: Can update text, icon, or both independently

Your Update Facility API now provides the same professional icon management experience as your other APIs! 🎉
