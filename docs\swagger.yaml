basePath: /
definitions:
  models.AuthResponse:
    properties:
      token:
        type: string
      user:
        $ref: '#/definitions/models.User'
    type: object
  models.ChangePasswordRequest:
    properties:
      current_password:
        type: string
      new_password:
        type: string
    required:
    - current_password
    - new_password
    type: object
  models.ChangePasswordResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  models.ComprehensiveSearchRequest:
    properties:
      area:
        type: string
      city:
        type: string
      property_status:
        enum:
        - rent
        - sell
        - rented
        - sold
        type: string
      property_type:
        type: string
      trending_area:
        type: string
    required:
    - area
    - city
    - property_status
    - property_type
    type: object
  models.ComprehensiveSearchResponse:
    properties:
      premium_listings:
        items:
          $ref: '#/definitions/models.Property'
        type: array
      properties:
        items:
          $ref: '#/definitions/models.Property'
        type: array
      top_agents:
        items:
          $ref: '#/definitions/models.User'
        type: array
      trending_areas:
        items:
          type: string
        type: array
    type: object
  models.CreatePackageRequest:
    properties:
      description:
        type: string
      discount:
        type: number
      email:
        type: string
      is_offer:
        type: boolean
      is_popular:
        type: boolean
      package_id:
        type: integer
      package_type: {}
      per_week_price:
        type: string
      plan_duration:
        type: string
      product_package: {}
      total_price:
        type: string
      user_id:
        type: string
    required:
    - description
    - email
    - package_id
    - per_week_price
    - plan_duration
    - total_price
    - user_id
    type: object
  models.FacilitiesResponse:
    properties:
      icon_url:
        type: string
      id:
        type: string
      name:
        type: string
    type: object
  models.Facility:
    properties:
      active:
        type: boolean
      available:
        type: boolean
      created_at:
        type: string
      icon_url:
        type: string
      id:
        type: string
      name:
        type: string
      updated_at:
        type: string
    type: object
  models.FavoriteRequest:
    properties:
      propertyId:
        type: string
    required:
    - propertyId
    type: object
  models.FavoriteResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  models.ForgotPasswordRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  models.ForgotPasswordResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  models.Location:
    properties:
      address:
        type: string
      area:
        type: string
      city:
        type: string
      country:
        type: string
      latitude:
        type: number
      longitude:
        type: number
      neighborhood:
        type: string
      state:
        type: string
      zip_code:
        type: string
    type: object
  models.LocationRequest:
    properties:
      address:
        type: string
      area:
        type: string
      city:
        type: string
      country:
        type: string
      latitude:
        type: number
      longitude:
        type: number
      neighborhood:
        type: string
      state:
        type: string
      zip_code:
        type: string
    type: object
  models.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  models.OTPResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  models.Package:
    properties:
      created_at:
        type: string
      description:
        type: string
      discount:
        type: number
      email:
        type: string
      id:
        type: string
      is_offer:
        type: boolean
      is_popular:
        type: boolean
      package_id:
        type: integer
      package_type: {}
      per_week_price:
        type: string
      plan_duration:
        description: e.g., "1 month", "3 months", "1 year"
        type: string
      product_package:
        description: Can store any type of data
      total_price:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  models.PaginatedResponse:
    properties:
      data: {}
      pagination:
        $ref: '#/definitions/models.Pagination'
    type: object
  models.Pagination:
    properties:
      has_next:
        type: boolean
      has_previous:
        type: boolean
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  models.Property:
    properties:
      area:
        type: number
      bathroom:
        type: integer
      bedroom:
        type: integer
      bhk:
        type: integer
      created_at:
        type: string
      description:
        type: string
      facilities:
        items:
          $ref: '#/definitions/models.PropertyFacility'
        type: array
      facing_direction:
        type: string
      furniture:
        type: string
      id:
        type: string
      images:
        items:
          type: string
        type: array
      is_property_active:
        type: boolean
      is_property_verified:
        type: boolean
      location:
        $ref: '#/definitions/models.Location'
      no_of_parking:
        type: integer
      owner_email:
        type: string
      owner_id:
        type: string
      owner_name:
        type: string
      owner_phone_number:
        type: string
      owner_profile_photo:
        type: string
      rating:
        type: number
      status:
        description: available, sold, rented
        type: string
      title:
        type: string
      total_floor:
        type: number
      total_price:
        type: number
      type:
        description: apartment, house, etc.
        type: string
      updated_at:
        type: string
      year_built:
        type: integer
    type: object
  models.PropertyDetailResponse:
    properties:
      is_favorite:
        description: Whether the property is in user's favorites
        type: boolean
      property:
        $ref: '#/definitions/models.Property'
      related_property:
        allOf:
        - $ref: '#/definitions/models.RelatedProperty'
        description: Related property from same area/city
      review:
        allOf:
        - $ref: '#/definitions/models.ReviewWithLikeStatus'
        description: Single latest review with like status, omitempty if no reviews
    type: object
  models.PropertyFacility:
    properties:
      facility_id:
        type: string
      image_url:
        type: string
      name:
        type: string
    type: object
  models.PropertyType:
    properties:
      active:
        type: boolean
      created_at:
        type: string
      id:
        type: string
      image_url:
        type: string
      name:
        type: string
      order:
        type: integer
      updated_at:
        type: string
    type: object
  models.RegisterRequest:
    properties:
      birth_date:
        type: string
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        type: string
      phn_number:
        type: string
      role:
        type: string
    required:
    - birth_date
    - email
    - first_name
    - last_name
    - password
    - phn_number
    type: object
  models.RelatedProperty:
    properties:
      area:
        type: number
      bathroom:
        type: integer
      bedroom:
        type: integer
      bhk:
        type: integer
      id:
        type: string
      image:
        type: string
      is_favorite:
        type: boolean
      location:
        $ref: '#/definitions/models.Location'
      status:
        type: string
      title:
        type: string
      total_price:
        description: Total price of the property
        type: number
      type:
        description: apartment, house, etc.
        type: string
      year_built:
        type: integer
    type: object
  models.ResendOTPRequest:
    properties:
      email:
        type: string
    required:
    - email
    type: object
  models.ResetPasswordRequest:
    properties:
      email:
        type: string
      new_password:
        type: string
      token:
        type: string
    required:
    - email
    - new_password
    - token
    type: object
  models.ResetPasswordResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  models.Review:
    properties:
      comment:
        type: string
      created_at:
        type: string
      dislikes:
        type: integer
      id:
        type: string
      is_active:
        type: boolean
      likes:
        type: integer
      photos:
        items:
          type: string
        type: array
      property_id:
        type: string
      rating:
        type: number
      updated_at:
        type: string
      user_id:
        type: string
      user_name:
        type: string
    type: object
  models.ReviewListWithLikeStatusResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/models.ReviewWithLikeStatus'
        type: array
      pagination:
        $ref: '#/definitions/models.Pagination'
    type: object
  models.ReviewResponse:
    properties:
      message:
        type: string
      review:
        $ref: '#/definitions/models.Review'
      success:
        type: boolean
    type: object
  models.ReviewWithLikeStatus:
    properties:
      comment:
        type: string
      created_at:
        type: string
      dislikes:
        type: integer
      id:
        type: string
      is_active:
        type: boolean
      is_disliked:
        type: boolean
      is_liked:
        type: boolean
      likes:
        type: integer
      photos:
        items:
          type: string
        type: array
      property_id:
        type: string
      rating:
        type: number
      updated_at:
        type: string
      user_id:
        type: string
      user_name:
        type: string
      user_profile_photo:
        type: string
    type: object
  models.SendOTPRequest:
    properties:
      birth_date:
        type: string
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        type: string
      phn_number:
        type: string
      role:
        type: string
    required:
    - birth_date
    - email
    - first_name
    - last_name
    - password
    - phn_number
    type: object
  models.UpdatePackageRequest:
    properties:
      description:
        type: string
      discount:
        type: number
      is_offer:
        type: boolean
      is_popular:
        type: boolean
      package_type: {}
      per_week_price:
        type: string
      plan_duration:
        type: string
      product_package: {}
      total_price:
        type: string
    type: object
  models.UpdatePropertyRequest:
    properties:
      area:
        type: number
      bathroom:
        minimum: 0
        type: integer
      bedroom:
        minimum: 0
        type: integer
      bhk:
        minimum: 0
        type: integer
      description:
        type: string
      facilities:
        items:
          $ref: '#/definitions/models.PropertyFacility'
        type: array
      facing_direction:
        type: string
      furniture:
        enum:
        - furnished
        - semi-furnished
        - unfurnished
        type: string
      images:
        items:
          type: string
        type: array
      is_property_active:
        type: boolean
      is_property_verified:
        type: boolean
      location:
        $ref: '#/definitions/models.LocationRequest'
      no_of_parking:
        minimum: 0
        type: integer
      owner_name:
        type: string
      rating:
        maximum: 5
        minimum: 0
        type: number
      status:
        enum:
        - rent
        - rented
        - sold
        - sell
        type: string
      title:
        type: string
      total_floor:
        minimum: 0
        type: number
      total_price:
        type: number
      type:
        enum:
        - apartment
        - house
        - villa
        - commercial
        - land
        type: string
      year_built:
        type: integer
    type: object
  models.User:
    properties:
      birth_date:
        type: string
      created_at:
        type: string
      email:
        type: string
      first_name:
        type: string
      id:
        type: string
      is_blocked:
        type: boolean
      is_document_verified:
        type: boolean
      is_profile_complete:
        type: boolean
      is_subscribed_user:
        type: boolean
      last_name:
        type: string
      location:
        $ref: '#/definitions/models.Location'
      metadata:
        additionalProperties: true
        type: object
      password_reset_expiry:
        type: string
      password_reset_token:
        type: string
      phn_number:
        type: string
      profile_photo:
        type: string
      role:
        description: admin, agent, user
        type: string
      signup_verification_code:
        type: string
      total_listed_properties:
        type: integer
      updated_at:
        type: string
    type: object
  models.VerifyOTPRequest:
    properties:
      email:
        type: string
      otp:
        type: string
    required:
    - email
    - otp
    type: object
  models.VerifyOTPResponse:
    properties:
      message:
        type: string
      token:
        type: string
      user:
        $ref: '#/definitions/models.User'
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.realestate-platform.com/support
  description: A modern, scalable real estate platform API
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Real Estate Platform API
  version: "1.0"
paths:
  /api/v1/admin/facilities:
    get:
      consumes:
      - application/json
      description: Get a list of facilities with pagination and filtering options
        (admin only)
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by name
        in: query
        name: name
        type: string
      - description: Filter by active status
        in: query
        name: active
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List facilities
      tags:
      - admin
      - facilities
    post:
      consumes:
      - multipart/form-data
      description: Create a new facility with support for icon file upload (admin
        only)
      parameters:
      - description: Facility details (JSON string)
        in: formData
        name: facility
        required: true
        type: string
      - description: Facility icon (single file)
        in: formData
        name: icon
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Facility'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create a new facility with icon upload
      tags:
      - admin
      - facilities
  /api/v1/admin/facilities/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a facility by ID (admin only)
      parameters:
      - description: Facility ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Facility not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete a facility
      tags:
      - admin
      - facilities
    get:
      consumes:
      - application/json
      description: Get a facility by ID (admin only)
      parameters:
      - description: Facility ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Facility'
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Facility not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get a facility
      tags:
      - admin
      - facilities
    put:
      consumes:
      - multipart/form-data
      description: Update an existing facility with support for icon file upload (admin
        only)
      parameters:
      - description: Facility ID
        in: path
        name: id
        required: true
        type: string
      - description: Facility details (JSON string)
        in: formData
        name: facility
        required: true
        type: string
      - description: Facility icon (single file)
        in: formData
        name: icon
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input or ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Facility not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update a facility with icon upload
      tags:
      - admin
      - facilities
  /api/v1/admin/property-types:
    get:
      consumes:
      - application/json
      description: Get a list of property types with pagination and filtering options
        (admin only)
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by name
        in: query
        name: name
        type: string
      - description: Filter by active status
        in: query
        name: active
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List property types
      tags:
      - admin
      - property-types
    post:
      consumes:
      - multipart/form-data
      description: Create a new property type with support for image file upload (admin
        only)
      parameters:
      - description: Property Type details (JSON string)
        in: formData
        name: property_type
        required: true
        type: string
      - description: Property Type image (single file)
        in: formData
        name: image
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.PropertyType'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create a new property type with image upload
      tags:
      - admin
      - property-types
  /api/v1/admin/property-types/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a property type by ID (admin only)
      parameters:
      - description: Property Type ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property type not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete a property type
      tags:
      - admin
      - property-types
    get:
      consumes:
      - application/json
      description: Get a property type by ID (admin only)
      parameters:
      - description: Property Type ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PropertyType'
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property type not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get a property type
      tags:
      - admin
      - property-types
    put:
      consumes:
      - multipart/form-data
      description: Update an existing property type with support for image file upload
        (admin only)
      parameters:
      - description: Property Type ID
        in: path
        name: id
        required: true
        type: string
      - description: Property Type details (JSON string)
        in: formData
        name: property_type
        required: true
        type: string
      - description: Property Type image (single file)
        in: formData
        name: image
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input or ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property type not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update a property type with image upload
      tags:
      - admin
      - property-types
  /api/v1/admin/users:
    get:
      consumes:
      - application/json
      description: Get a list of users with pagination and filtering options (admin
        only)
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by email
        in: query
        name: email
        type: string
      - description: Filter by role
        in: query
        name: role
        type: string
      - description: Filter by blocked status
        in: query
        name: is_blocked
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List users
      tags:
      - admin
      - users
  /api/v1/admin/users/{id}:
    get:
      consumes:
      - application/json
      description: Get a user by ID (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get a user
      tags:
      - admin
      - users
  /api/v1/admin/users/{id}/block:
    put:
      consumes:
      - application/json
      description: Block a user by ID (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Block a user
      tags:
      - admin
      - users
  /api/v1/admin/users/{id}/unblock:
    put:
      consumes:
      - application/json
      description: Unblock a user by ID (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Unblock a user
      tags:
      - admin
      - users
  /api/v1/metadata/facilities:
    get:
      consumes:
      - application/json
      description: Get a simplified list of facilities (id, name, icon_url) with pagination
        and filtering options (requires authentication)
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by name
        in: query
        name: name
        type: string
      - description: Filter by active status
        in: query
        name: active
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/models.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.FacilitiesResponse'
                  type: array
              type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List facilities for authenticated users
      tags:
      - metadata
  /api/v1/metadata/property-types:
    get:
      consumes:
      - application/json
      description: Get a list of property types with pagination and filtering options
        (requires authentication)
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by name
        in: query
        name: name
        type: string
      - description: Filter by active status
        in: query
        name: active
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List property types for authenticated users
      tags:
      - metadata
  /api/v1/subscription/packages:
    get:
      description: Get a paginated list of subscription packages
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: List subscription packages
      tags:
      - subscription
    post:
      consumes:
      - application/json
      description: Create a new subscription package with the provided details
      parameters:
      - description: Package details
        in: body
        name: package
        required: true
        schema:
          $ref: '#/definitions/models.CreatePackageRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Package'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create a new subscription package
      tags:
      - subscription
  /api/v1/subscription/packages/{id}:
    delete:
      description: Delete an existing subscription package
      parameters:
      - description: Package ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID format
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Package not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete a subscription package
      tags:
      - subscription
    get:
      description: Get a subscription package by ID
      parameters:
      - description: Package ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Package'
        "400":
          description: Invalid ID format
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Package not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get a subscription package
      tags:
      - subscription
    put:
      consumes:
      - application/json
      description: Update an existing subscription package
      parameters:
      - description: Package ID
        in: path
        name: id
        required: true
        type: string
      - description: Package details
        in: body
        name: package
        required: true
        schema:
          $ref: '#/definitions/models.UpdatePackageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Package not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update a subscription package
      tags:
      - subscription
  /api/v1/subscription/packages/popular:
    get:
      description: Get a list of popular subscription packages
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Package'
            type: array
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get popular packages
      tags:
      - subscription
  /api/v1/user/change-password:
    post:
      consumes:
      - application/json
      description: Change the authenticated user's password
      parameters:
      - description: Password Change Info
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ChangePasswordResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized or invalid current password
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Change user password
      tags:
      - user
  /api/v1/user/complete-profile:
    post:
      consumes:
      - multipart/form-data
      description: Complete user profile with address, contact information, and profile
        image upload
      parameters:
      - description: Profile completion details (JSON string)
        in: formData
        name: profile
        required: true
        type: string
      - description: Profile image (single file)
        in: formData
        name: profile_image
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: User profile data
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Complete user profile with image upload
      tags:
      - user
  /api/v1/user/details:
    get:
      consumes:
      - application/json
      description: Get complete user details including all profile information, location,
        and status fields
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get user details
      tags:
      - user
  /api/v1/user/profile:
    put:
      consumes:
      - multipart/form-data
      description: Update user profile information with optional profile image upload
      parameters:
      - description: Profile update details (JSON string)
        in: formData
        name: profile
        required: true
        type: string
      - description: Profile image (single file)
        in: formData
        name: profile_image
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: User profile data
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update user profile with image upload
      tags:
      - user
  /api/v1/user/top-agents:
    get:
      consumes:
      - application/json
      description: Get a list of top performing agents (subscribed users with at least
        one property listed, ordered by total properties in descending order)
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of top performing agents
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: List top performing agents
      tags:
      - User
  /auth/forgot-password:
    post:
      consumes:
      - application/json
      description: Send a password reset link to the user's email
      parameters:
      - description: Email for password reset
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ForgotPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ForgotPasswordResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Request password reset
      tags:
      - auth
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate a user and return a JWT token
      parameters:
      - description: Login Credentials
        in: body
        name: credentials
        required: true
        schema:
          $ref: '#/definitions/models.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.AuthResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Invalid credentials
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Login a user
      tags:
      - auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user with the provided information
      parameters:
      - description: User Registration Info
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: User already exists
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Register a new user
      tags:
      - auth
  /auth/resend-otp:
    post:
      consumes:
      - application/json
      description: Resend OTP to user's email for verification
      parameters:
      - description: Email for OTP Resend
        in: body
        name: verification
        required: true
        schema:
          $ref: '#/definitions/models.ResendOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OTPResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: User already verified
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Resend OTP for registration
      tags:
      - auth
  /auth/reset-password:
    post:
      consumes:
      - application/json
      description: Reset user password using the token received via email
      parameters:
      - description: Password Reset Info
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ResetPasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ResetPasswordResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Invalid token
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Reset password with token
      tags:
      - auth
  /auth/send-otp:
    post:
      consumes:
      - application/json
      description: Send OTP to user's email for verification
      parameters:
      - description: User Registration Info with Email
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.SendOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.OTPResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: User already exists
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Send OTP for registration
      tags:
      - auth
  /auth/verify-otp:
    post:
      consumes:
      - application/json
      description: Verify OTP and complete user registration with JWT token
      parameters:
      - description: OTP Verification Info
        in: body
        name: verification
        required: true
        schema:
          $ref: '#/definitions/models.VerifyOTPRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.VerifyOTPResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Invalid OTP
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Verify OTP and register user
      tags:
      - auth
  /favorites:
    get:
      consumes:
      - application/json
      description: Get all favorite properties for the authenticated user with filtering
        options
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      - description: Filter by property type (apartment, house, villa, commercial,
          land)
        in: query
        name: type
        type: string
      - description: Filter by property status (rent, sell, rented, sold)
        in: query
        name: status
        type: string
      - description: Minimum price
        in: query
        name: min_price
        type: number
      - description: Maximum price
        in: query
        name: max_price
        type: number
      - description: Filter by BHK
        in: query
        name: bhk
        type: integer
      - description: Filter by bedroom count
        in: query
        name: bedroom
        type: integer
      - description: Filter by bathroom count
        in: query
        name: bathroom
        type: integer
      - description: Filter by furniture type (furnished, semi-furnished, unfurnished)
        in: query
        name: furniture
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of favorite properties
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List user's favorite properties
      tags:
      - favorites
    post:
      consumes:
      - application/json
      description: Add a property to the authenticated user's favorites
      parameters:
      - description: Property ID
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.FavoriteRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Success message
          schema:
            $ref: '#/definitions/models.FavoriteResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property not found
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Property already in favorites
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Add a property to favorites
      tags:
      - favorites
  /favorites/{propertyId}:
    delete:
      consumes:
      - application/json
      description: Remove a property from the authenticated user's favorites
      parameters:
      - description: Property ID
        in: path
        name: propertyId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            $ref: '#/definitions/models.FavoriteResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Favorite not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Remove a property from favorites
      tags:
      - favorites
  /properties:
    get:
      consumes:
      - application/json
      description: Get a list of properties with pagination and filtering options
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page
        in: query
        name: limit
        type: integer
      - description: Filter by city
        in: query
        name: city
        type: string
      - description: Filter by area (e.g., Kamrej, Varacha)
        in: query
        name: area
        type: string
      - description: Filter by property type
        in: query
        name: type
        type: string
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Minimum price
        in: query
        name: min_price
        type: number
      - description: Maximum price
        in: query
        name: max_price
        type: number
      - description: Filter by BHK
        in: query
        name: bhk
        type: integer
      - description: Filter by bedroom count
        in: query
        name: bedroom
        type: integer
      - description: Filter by bathroom count
        in: query
        name: bathroom
        type: integer
      - description: Filter by furniture type
        in: query
        name: furniture
        type: string
      - description: Filter by facilities (comma-separated)
        in: query
        name: facilities
        type: string
      - default: '"created_at"'
        description: Sort field
        in: query
        name: sort_by
        type: string
      - default: '"desc"'
        description: Sort order
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Properties list with pagination metadata
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List properties
      tags:
      - properties
    post:
      consumes:
      - multipart/form-data
      description: Create a new property listing with images and facilities with image URLs
      parameters:
      - description: Property Information (JSON) - facilities should include image_url field
        in: formData
        name: property
        required: true
        type: string
      - description: Property Images
        in: formData
        name: images
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.Property'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create a new property
      tags:
      - properties
  /properties/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a property by ID
      parameters:
      - description: Property ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid ID format
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete a property
      tags:
      - properties
    get:
      consumes:
      - application/json
      description: Get detailed information about a property by its ID including the
        latest review
      parameters:
      - description: Property ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PropertyDetailResponse'
        "400":
          description: Invalid ID format
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get a property by ID with latest review
      tags:
      - properties
    put:
      consumes:
      - application/json
      description: Update an existing property by ID
      parameters:
      - description: Property ID
        in: path
        name: id
        required: true
        type: string
      - description: Property fields to update
        in: body
        name: property
        required: true
        schema:
          $ref: '#/definitions/models.UpdatePropertyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Invalid input or ID
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update a property
      tags:
      - properties
  /properties/comprehensive-search:
    post:
      consumes:
      - application/json
      description: Get comprehensive property search results including trending areas,
        properties, top agents, and premium listings
      parameters:
      - description: Search parameters
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ComprehensiveSearchRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Comprehensive search results
          schema:
            $ref: '#/definitions/models.ComprehensiveSearchResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Comprehensive property search
      tags:
      - properties
  /properties/premium:
    get:
      consumes:
      - application/json
      description: Get a list of premium properties (added by subscribed users)
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of premium properties
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: List premium properties
      tags:
      - properties
  /properties/user:
    get:
      consumes:
      - application/json
      description: Get all properties owned by the authenticated user with pagination
        and filtering options
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Items per page (max 100)
        in: query
        name: limit
        type: integer
      - description: Filter by property type (apartment, house, villa, commercial,
          land)
        in: query
        name: type
        type: string
      - description: Filter by property status (available, sold, rented)
        in: query
        name: status
        type: string
      - description: Minimum price filter
        in: query
        name: min_price
        type: number
      - description: Maximum price filter
        in: query
        name: max_price
        type: number
      - description: Filter by BHK count
        in: query
        name: bhk
        type: integer
      - description: Filter by bedroom count
        in: query
        name: bedroom
        type: integer
      - description: Filter by bathroom count
        in: query
        name: bathroom
        type: integer
      - description: Filter by furniture type (furnished, semi-furnished, unfurnished)
        in: query
        name: furniture
        type: string
      - description: Filter by city
        in: query
        name: city
        type: string
      - description: Filter by area
        in: query
        name: area
        type: string
      - default: '"created_at"'
        description: Sort field (created_at, updated_at, total_price, area)
        in: query
        name: sort_by
        type: string
      - default: '"desc"'
        description: Sort order (asc, desc)
        in: query
        name: sort_order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of user's properties with favorite status
          schema:
            $ref: '#/definitions/models.PaginatedResponse'
        "400":
          description: Invalid parameters
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get properties by user ID with filtering
      tags:
      - properties
  /reviews:
    post:
      consumes:
      - multipart/form-data
      description: Create a new review for a property with support for image uploads
      parameters:
      - description: Review details (JSON string)
        in: formData
        name: review
        required: true
        type: string
      - description: Review images (multiple files allowed)
        in: formData
        name: images
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Property not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Create a new review with image uploads
      tags:
      - reviews
  /reviews/{reviewId}:
    delete:
      consumes:
      - application/json
      description: Delete an existing review
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Delete a review
      tags:
      - reviews
    put:
      consumes:
      - multipart/form-data
      description: Update an existing review with support for image uploads
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      - description: Review details (JSON string)
        in: formData
        name: review
        required: true
        type: string
      - description: Review images (multiple files allowed)
        in: formData
        name: images
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Update a review with image uploads
      tags:
      - reviews
  /reviews/{reviewId}/dislike:
    delete:
      consumes:
      - application/json
      description: Remove a dislike from a review
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found or not disliked
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Remove dislike from a review
      tags:
      - reviews
    post:
      consumes:
      - application/json
      description: Add a dislike to a review
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Already disliked
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Dislike a review
      tags:
      - reviews
  /reviews/{reviewId}/like:
    delete:
      consumes:
      - application/json
      description: Remove a like from a review
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found or not liked
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Remove like from a review
      tags:
      - reviews
    post:
      consumes:
      - application/json
      description: Add a like to a review
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Already liked
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Like a review
      tags:
      - reviews
  /reviews/{reviewId}/toggle-active:
    put:
      consumes:
      - application/json
      description: Toggle the active status of a review (admin only)
      parameters:
      - description: Review ID
        in: path
        name: reviewId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Review not found
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: Toggle review active status
      tags:
      - reviews
  /reviews/my-reviews/property/{propertyId}:
    get:
      consumes:
      - application/json
      description: Get all reviews written by the current logged-in user for a specific
        property with pagination and like status
      parameters:
      - description: Property ID
        in: path
        name: propertyId
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewListWithLikeStatusResponse'
        "400":
          description: Invalid property ID
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: List reviews by current user for a specific property
      tags:
      - reviews
  /reviews/property/{propertyId}:
    get:
      consumes:
      - application/json
      description: Get all reviews for a specific property with pagination and like
        status for logged-in user
      parameters:
      - description: Property ID
        in: path
        name: propertyId
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReviewListWithLikeStatusResponse'
        "400":
          description: Invalid input
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Server error
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - ApiKeyAuth: []
      summary: List reviews for a property with like status
      tags:
      - reviews
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and the JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
