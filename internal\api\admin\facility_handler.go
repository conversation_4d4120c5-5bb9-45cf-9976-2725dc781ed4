package admin

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/property_metadata"
)

type FacilityHandler struct {
	service *property_metadata.Service
	logger  *zap.Logger
}

func NewFacilityHandler(service *property_metadata.Service, logger *zap.Logger) *FacilityHandler {
	return &FacilityHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers the facility routes
func (h *FacilityHandler) RegisterRoutes(router *gin.RouterGroup) {
	facilities := router.Group("/facilities")
	{
		facilities.POST("", h.CreateFacility)
		facilities.GET("", h.ListFacilities)
		facilities.GET("/:id", h.GetFacility)
		facilities.PUT("/:id", h.UpdateFacility)
		facilities.DELETE("/:id", h.DeleteFacility)
	}
}

// CreateFacility handles facility creation with icon upload
// @Summary Create a new facility with icon upload
// @Description Create a new facility with support for icon file upload (admin only)
// @Tags admin,facilities
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param facility formData string true "Facility details (JSON string)"
// @Param icon formData file true "Facility icon (single file)"
// @Success 201 {object} models.Facility
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/facilities [post]
func (h *FacilityHandler) CreateFacility(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get facility details from form
	facilityJSON := c.PostForm("facility")
	if facilityJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "facility details are required"})
		return
	}

	// Parse facility details
	var req models.CreateFacilityRequest
	if err := json.Unmarshal([]byte(facilityJSON), &req); err != nil {
		h.logger.Warn("Invalid facility creation request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility details format"})
		return
	}

	// Process icon file upload (required)
	file, err := c.FormFile("icon")
	if err != nil {
		h.logger.Warn("Failed to get icon file",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "facility icon is required"})
		return
	}

	// Log file details for debugging
	h.logger.Info("Icon file details",
		zap.String("filename", file.Filename),
		zap.Int64("size", file.Size),
		zap.String("content_type", file.Header.Get("Content-Type")),
		zap.String("ip", c.ClientIP()),
	)

	// Create facility from request first (before saving file)
	facility := &models.Facility{
		Name:      req.Name,
		IconURL:   "", // Will be set after successful file upload
		Available: req.Available,
		Active:    req.Active,
	}

	// Set defaults if not provided
	if !req.Available {
		facility.Available = true
	}
	if !req.Active {
		facility.Active = true
	}

	h.logger.Info("Processing facility creation request",
		zap.String("name", facility.Name),
		zap.String("ip", c.ClientIP()),
	)

	// Try to create facility in database first (before saving file)
	if err := h.service.CreateFacility(c.Request.Context(), facility); err != nil {
		h.logger.Error("Facility creation failed",
			zap.String("name", facility.Name),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)

		// Handle specific MongoDB duplicate key error
		if strings.Contains(err.Error(), "E11000 duplicate key error") {
			if strings.Contains(err.Error(), "name_1") {
				c.JSON(http.StatusConflict, gin.H{"error": "facility with this name already exists"})
			} else {
				c.JSON(http.StatusConflict, gin.H{"error": "facility already exists"})
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create facility"})
		}
		return
	}

	// Database creation successful, now save the file
	uploadDir := "uploads/facilities"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		h.logger.Error("Failed to create uploads directory",
			zap.String("error", err.Error()),
			zap.String("facility_id", facility.ID.Hex()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete the facility from database since file upload failed
		if deleteErr := h.service.DeleteFacility(c.Request.Context(), facility.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback facility creation",
				zap.String("facility_id", facility.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
		return
	}

	// Generate unique filename with URL-safe characters (same format as property/review APIs)
	originalName := filepath.Base(file.Filename)
	ext := filepath.Ext(originalName)
	nameWithoutExt := strings.TrimSuffix(originalName, ext)
	safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
	safeName = strings.ReplaceAll(safeName, ":", "_")
	safeName = strings.ReplaceAll(safeName, "/", "_")
	safeName = strings.ReplaceAll(safeName, "\\", "_")

	// Generate unique filename using timestamp and random string (consistent with other APIs)
	timestamp := time.Now().UnixNano()
	randomStr := primitive.NewObjectID().Hex()
	filename := fmt.Sprintf("facility_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
	filePath := filepath.Join(uploadDir, filename)

	// Save file
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		h.logger.Error("Failed to save facility icon",
			zap.String("error", err.Error()),
			zap.String("filename", filename),
			zap.String("facility_id", facility.ID.Hex()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete the facility from database since file upload failed
		if deleteErr := h.service.DeleteFacility(c.Request.Context(), facility.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback facility creation",
				zap.String("facility_id", facility.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to save facility icon"})
		return
	}

	// Set icon URL (use forward slashes for URLs)
	iconURL := fmt.Sprintf("/uploads/facilities/%s", filename)

	// Update facility with icon URL
	updateData := bson.M{"icon_url": iconURL}
	if err := h.service.UpdateFacility(c.Request.Context(), facility.ID, updateData); err != nil {
		h.logger.Error("Failed to update facility with icon URL",
			zap.String("facility_id", facility.ID.Hex()),
			zap.String("icon_url", iconURL),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete both file and facility
		if removeErr := os.Remove(filePath); removeErr != nil {
			h.logger.Error("Failed to cleanup uploaded file",
				zap.String("file_path", filePath),
				zap.String("error", removeErr.Error()),
			)
		}
		if deleteErr := h.service.DeleteFacility(c.Request.Context(), facility.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback facility creation",
				zap.String("facility_id", facility.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update facility with icon"})
		return
	}

	// Update the facility object with the icon URL for response
	facility.IconURL = iconURL

	h.logger.Info("Facility created successfully with icon",
		zap.String("facility_id", facility.ID.Hex()),
		zap.String("name", facility.Name),
		zap.String("icon_filename", filename),
		zap.String("icon_url", iconURL),
		zap.String("ip", c.ClientIP()),
	)

	h.logger.Info("Facility created successfully",
		zap.String("id", facility.ID.Hex()),
		zap.String("name", facility.Name),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusCreated, facility)
}

// GetFacility handles getting a facility by ID
// @Summary Get a facility
// @Description Get a facility by ID (admin only)
// @Tags admin,facilities
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Facility ID"
// @Success 200 {object} models.Facility
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Facility not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/facilities/{id} [get]
func (h *FacilityHandler) GetFacility(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid facility ID format",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility id"})
		return
	}

	h.logger.Info("Processing facility retrieval request",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	facility, err := h.service.GetFacility(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Facility retrieval failed",
			zap.String("id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if facility == nil {
		h.logger.Warn("Facility not found",
			zap.String("id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "facility not found"})
		return
	}

	h.logger.Info("Facility retrieved successfully",
		zap.String("id", facility.ID.Hex()),
		zap.String("name", facility.Name),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, facility)
}

// ListFacilities handles listing facilities with pagination and filters
// @Summary List facilities
// @Description Get a list of facilities with pagination and filtering options (admin only)
// @Tags admin,facilities
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param name query string false "Filter by name"
// @Param active query bool false "Filter by active status"
// @Success 200 {object} models.PaginatedResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/facilities [get]
func (h *FacilityHandler) ListFacilities(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	// Build filter
	filter := bson.M{}
	if name := c.Query("name"); name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}
	if active := c.Query("active"); active != "" {
		activeBool, _ := strconv.ParseBool(active)
		filter["active"] = activeBool
	}

	h.logger.Info("Processing facilities list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	facilities, total, err := h.service.ListFacilities(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Facilities listing failed",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(float64(total)/float64(limit) + 0.5)
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Facilities list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(facilities)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: facilities,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateFacility handles facility updates with icon upload
// @Summary Update a facility with icon upload
// @Description Update an existing facility with support for icon file upload (admin only)
// @Tags admin,facilities
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param id path string true "Facility ID"
// @Param facility formData string true "Facility details (JSON string)"
// @Param icon formData file false "Facility icon (single file)"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid input or ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Facility not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/facilities/{id} [put]
func (h *FacilityHandler) UpdateFacility(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid facility ID format for update",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility id"})
		return
	}

	// Get existing facility to check current icon and for comparison
	existingFacility, err := h.service.GetFacility(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get existing facility",
			zap.String("facility_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get facility"})
		return
	}

	if existingFacility == nil {
		h.logger.Warn("Facility not found for update",
			zap.String("facility_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "facility not found"})
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("facility_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get facility details from form
	facilityJSON := c.PostForm("facility")
	if facilityJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "facility details are required"})
		return
	}

	// Parse facility details
	var req models.UpdateFacilityRequest
	if err := json.Unmarshal([]byte(facilityJSON), &req); err != nil {
		h.logger.Warn("Invalid facility update request",
			zap.String("facility_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility details format"})
		return
	}

	// Process icon upload with intelligent handling (similar to Update Profile API)
	var newIconURL string
	file, err := c.FormFile("icon")
	if err == nil {
		// Icon file provided, process it
		// Check if the uploaded icon is the same as the current one
		currentIconName := ""
		if existingFacility.IconURL != "" {
			// Extract filename from current icon URL
			parts := strings.Split(existingFacility.IconURL, "/")
			if len(parts) > 0 {
				currentIconName = parts[len(parts)-1]
			}
		}

		// Generate safe filename for comparison
		originalName := filepath.Base(file.Filename)
		ext := filepath.Ext(originalName)
		nameWithoutExt := strings.TrimSuffix(originalName, ext)
		safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
		safeName = strings.ReplaceAll(safeName, ":", "_")
		safeName = strings.ReplaceAll(safeName, "/", "_")
		safeName = strings.ReplaceAll(safeName, "\\", "_")

		// Check if the same icon is being uploaded (by comparing original filename)
		if currentIconName != "" && strings.Contains(currentIconName, safeName) && strings.HasSuffix(currentIconName, ext) {
			h.logger.Info("Same icon uploaded, skipping processing",
				zap.String("facility_id", id.Hex()),
				zap.String("filename", originalName),
			)
			// Keep the existing icon URL
			newIconURL = existingFacility.IconURL
		} else {
			// Create uploads directory only when needed
			uploadDir := "uploads/facilities"
			if err := os.MkdirAll(uploadDir, 0755); err != nil {
				h.logger.Error("Failed to create uploads directory",
					zap.String("error", err.Error()),
					zap.String("facility_id", id.Hex()),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
				return
			}

			// Generate unique filename using timestamp and random string (consistent with other APIs)
			timestamp := time.Now().UnixNano()
			randomStr := primitive.NewObjectID().Hex()
			filename := fmt.Sprintf("facility_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
			filePath := filepath.Join(uploadDir, filename)

			// Save new file
			if err := c.SaveUploadedFile(file, filePath); err != nil {
				h.logger.Error("Failed to save facility icon",
					zap.String("error", err.Error()),
					zap.String("facility_id", id.Hex()),
					zap.String("filename", filename),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to save facility icon"})
				return
			}

			// Delete previous icon if it exists
			if existingFacility.IconURL != "" {
				// Convert URL path to filesystem path
				oldIconPath := strings.TrimPrefix(existingFacility.IconURL, "/")
				if err := os.Remove(oldIconPath); err != nil {
					// Log warning but don't fail the request
					h.logger.Warn("Failed to delete previous facility icon",
						zap.String("error", err.Error()),
						zap.String("facility_id", id.Hex()),
						zap.String("old_icon_path", oldIconPath),
					)
				} else {
					h.logger.Info("Previous facility icon deleted successfully",
						zap.String("facility_id", id.Hex()),
						zap.String("old_icon_path", oldIconPath),
					)
				}
			}

			// Set new icon URL (use forward slashes for URLs)
			newIconURL = fmt.Sprintf("/uploads/facilities/%s", filename)

			h.logger.Info("New facility icon uploaded successfully",
				zap.String("facility_id", id.Hex()),
				zap.String("filename", filename),
				zap.String("url", newIconURL),
			)
		}
	} else {
		// No icon file provided, keep existing icon
		newIconURL = existingFacility.IconURL
	}

	// Convert request to bson.M for update
	update := bson.M{}

	// Only add fields that are provided in the request
	if req.Name != "" {
		update["name"] = req.Name
	}

	// Always update icon URL with the processed result
	update["icon_url"] = newIconURL

	// Boolean fields need special handling
	update["available"] = req.Available
	update["active"] = req.Active

	h.logger.Info("Processing facility update request",
		zap.String("facility_id", id.Hex()),
		zap.Any("update", update),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.UpdateFacility(c.Request.Context(), id, update); err != nil {
		h.logger.Error("Facility update failed",
			zap.String("facility_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "facility not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("Facility updated successfully",
		zap.String("facility_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "facility updated successfully"})
}

// DeleteFacility handles facility deletion
// @Summary Delete a facility
// @Description Delete a facility by ID (admin only)
// @Tags admin,facilities
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Facility ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Facility not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/facilities/{id} [delete]
func (h *FacilityHandler) DeleteFacility(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid facility ID format for deletion",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid facility id"})
		return
	}

	h.logger.Info("Processing facility deletion request",
		zap.String("facility_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.DeleteFacility(c.Request.Context(), id); err != nil {
		h.logger.Error("Facility deletion failed",
			zap.String("facility_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "facility not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("Facility deleted successfully",
		zap.String("facility_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "facility deleted successfully"})
}
