package admin

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/property_metadata"
)

type PropertyTypeHandler struct {
	service *property_metadata.Service
	logger  *zap.Logger
}

func NewPropertyTypeHandler(service *property_metadata.Service, logger *zap.Logger) *PropertyTypeHandler {
	return &PropertyTypeHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers the property type routes
func (h *PropertyTypeHandler) RegisterRoutes(router *gin.RouterGroup) {
	propertyTypes := router.Group("/property-types")
	{
		propertyTypes.POST("", h.CreatePropertyType)
		propertyTypes.GET("", h.ListPropertyTypes)
		propertyTypes.GET("/:id", h.GetPropertyType)
		propertyTypes.PUT("/:id", h.UpdatePropertyType)
		propertyTypes.DELETE("/:id", h.DeletePropertyType)
	}
}

// CreatePropertyType handles property type creation with image upload
// @Summary Create a new property type with image upload
// @Description Create a new property type with support for image file upload (admin only)
// @Tags admin,property-types
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param property_type formData string true "Property Type details (JSON string)"
// @Param image formData file true "Property Type image (single file)"
// @Success 201 {object} models.PropertyType
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/property-types [post]
func (h *PropertyTypeHandler) CreatePropertyType(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get property type details from form
	propertyTypeJSON := c.PostForm("property_type")
	if propertyTypeJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "property type details are required"})
		return
	}

	// Parse property type details
	var req models.CreatePropertyTypeRequest
	if err := json.Unmarshal([]byte(propertyTypeJSON), &req); err != nil {
		h.logger.Warn("Invalid property type creation request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property type details format"})
		return
	}

	// Process image file upload (required)
	file, err := c.FormFile("image")
	if err != nil {
		h.logger.Warn("Failed to get image file",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "property type image is required"})
		return
	}

	// Log file details for debugging
	h.logger.Info("Image file details",
		zap.String("filename", file.Filename),
		zap.Int64("size", file.Size),
		zap.String("content_type", file.Header.Get("Content-Type")),
		zap.String("ip", c.ClientIP()),
	)

	// Create property type from request first (before saving file)
	propertyType := &models.PropertyType{
		Name:     req.Name,
		Order:    req.Order,
		ImageURL: "",   // Will be set after successful file upload
		Active:   true, // Default to true
	}

	// Set Active field if provided
	if req.Active != nil {
		propertyType.Active = *req.Active
	}

	h.logger.Info("Processing property type creation request",
		zap.String("name", propertyType.Name),
		zap.Int("order", propertyType.Order),
		zap.Bool("active", propertyType.Active),
		zap.String("ip", c.ClientIP()),
	)

	// Try to create property type in database first (before saving file)
	if err := h.service.CreatePropertyType(c.Request.Context(), propertyType); err != nil {
		h.logger.Error("Property type creation failed",
			zap.String("name", propertyType.Name),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)

		// Handle specific MongoDB duplicate key error
		if strings.Contains(err.Error(), "E11000 duplicate key error") {
			if strings.Contains(err.Error(), "name_1") {
				c.JSON(http.StatusConflict, gin.H{"error": "property type with this name already exists"})
			} else {
				c.JSON(http.StatusConflict, gin.H{"error": "property type already exists"})
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create property type"})
		}
		return
	}

	// Database creation successful, now save the file
	uploadDir := "uploads/property-types"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		h.logger.Error("Failed to create uploads directory",
			zap.String("error", err.Error()),
			zap.String("property_type_id", propertyType.ID.Hex()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete the property type from database since file upload failed
		if deleteErr := h.service.DeletePropertyType(c.Request.Context(), propertyType.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback property type creation",
				zap.String("property_type_id", propertyType.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
		return
	}

	// Generate unique filename with URL-safe characters (same format as facility API)
	originalName := filepath.Base(file.Filename)
	ext := filepath.Ext(originalName)
	nameWithoutExt := strings.TrimSuffix(originalName, ext)
	safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
	safeName = strings.ReplaceAll(safeName, ":", "_")
	safeName = strings.ReplaceAll(safeName, "/", "_")
	safeName = strings.ReplaceAll(safeName, "\\", "_")

	// Generate unique filename using timestamp and random string (consistent with other APIs)
	timestamp := time.Now().UnixNano()
	randomStr := primitive.NewObjectID().Hex()
	filename := fmt.Sprintf("property_type_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
	filePath := filepath.Join(uploadDir, filename)

	// Save file
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		h.logger.Error("Failed to save property type image",
			zap.String("error", err.Error()),
			zap.String("filename", filename),
			zap.String("property_type_id", propertyType.ID.Hex()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete the property type from database since file upload failed
		if deleteErr := h.service.DeletePropertyType(c.Request.Context(), propertyType.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback property type creation",
				zap.String("property_type_id", propertyType.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to save property type image"})
		return
	}

	// Set image URL (use forward slashes for URLs)
	imageURL := fmt.Sprintf("/uploads/property-types/%s", filename)

	// Update property type with image URL
	updateData := bson.M{"image_url": imageURL}
	if err := h.service.UpdatePropertyType(c.Request.Context(), propertyType.ID, updateData); err != nil {
		h.logger.Error("Failed to update property type with image URL",
			zap.String("property_type_id", propertyType.ID.Hex()),
			zap.String("image_url", imageURL),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)

		// Rollback: Delete both file and property type
		if removeErr := os.Remove(filePath); removeErr != nil {
			h.logger.Error("Failed to cleanup uploaded file",
				zap.String("file_path", filePath),
				zap.String("error", removeErr.Error()),
			)
		}
		if deleteErr := h.service.DeletePropertyType(c.Request.Context(), propertyType.ID); deleteErr != nil {
			h.logger.Error("Failed to rollback property type creation",
				zap.String("property_type_id", propertyType.ID.Hex()),
				zap.String("error", deleteErr.Error()),
			)
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update property type with image"})
		return
	}

	// Update the property type object with the image URL for response
	propertyType.ImageURL = imageURL

	h.logger.Info("Property type created successfully with image",
		zap.String("property_type_id", propertyType.ID.Hex()),
		zap.String("name", propertyType.Name),
		zap.String("image_filename", filename),
		zap.String("image_url", imageURL),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusCreated, propertyType)
}

// GetPropertyType handles getting a property type by ID
// @Summary Get a property type
// @Description Get a property type by ID (admin only)
// @Tags admin,property-types
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Property Type ID"
// @Success 200 {object} models.PropertyType
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Property type not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/property-types/{id} [get]
func (h *PropertyTypeHandler) GetPropertyType(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property type ID format",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property type id"})
		return
	}

	h.logger.Info("Processing property type retrieval request",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	propertyType, err := h.service.GetPropertyType(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Property type retrieval failed",
			zap.String("id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if propertyType == nil {
		h.logger.Warn("Property type not found",
			zap.String("id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "property type not found"})
		return
	}

	h.logger.Info("Property type retrieved successfully",
		zap.String("id", propertyType.ID.Hex()),
		zap.String("name", propertyType.Name),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, propertyType)
}

// ListPropertyTypes handles listing property types with pagination and filters
// @Summary List property types
// @Description Get a list of property types with pagination and filtering options (admin only)
// @Tags admin,property-types
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param name query string false "Filter by name"
// @Param active query string false "Filter by active status"
// @Success 200 {object} models.PaginatedResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/property-types [get]
func (h *PropertyTypeHandler) ListPropertyTypes(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	// Build filter
	filter := bson.M{}
	if name := c.Query("name"); name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}
	if active := c.Query("active"); active != "" {
		activeBool, err := strconv.ParseBool(active)
		if err == nil {
			filter["active"] = activeBool
		}
	}

	h.logger.Info("Processing property types list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	propertyTypes, total, err := h.service.ListPropertyTypes(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Property types listing failed",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(float64(total)/float64(limit) + 0.5)
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Property types list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(propertyTypes)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: propertyTypes,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdatePropertyType handles property type updates with image upload
// @Summary Update a property type with image upload
// @Description Update an existing property type with support for image file upload (admin only)
// @Tags admin,property-types
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param id path string true "Property Type ID"
// @Param property_type formData string true "Property Type details (JSON string)"
// @Param image formData file false "Property Type image (single file)"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid input or ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Property type not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/property-types/{id} [put]
func (h *PropertyTypeHandler) UpdatePropertyType(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property type ID format for update",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property type id"})
		return
	}

	// Get existing property type to check current image and for comparison
	existingPropertyType, err := h.service.GetPropertyType(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get existing property type",
			zap.String("property_type_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get property type"})
		return
	}

	if existingPropertyType == nil {
		h.logger.Warn("Property type not found for update",
			zap.String("property_type_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "property type not found"})
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("property_type_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get property type details from form
	propertyTypeJSON := c.PostForm("property_type")
	if propertyTypeJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "property type details are required"})
		return
	}

	// Parse property type details
	var req models.UpdatePropertyTypeRequest
	if err := json.Unmarshal([]byte(propertyTypeJSON), &req); err != nil {
		h.logger.Warn("Invalid property type update request",
			zap.String("property_type_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property type details format"})
		return
	}

	// Process image upload with intelligent handling (similar to Update Facility API)
	var newImageURL string
	file, err := c.FormFile("image")
	if err == nil {
		// Image file provided, process it
		// Check if the uploaded image is the same as the current one
		currentImageName := ""
		if existingPropertyType.ImageURL != "" {
			// Extract filename from current image URL
			parts := strings.Split(existingPropertyType.ImageURL, "/")
			if len(parts) > 0 {
				currentImageName = parts[len(parts)-1]
			}
		}

		// Generate safe filename for comparison
		originalName := filepath.Base(file.Filename)
		ext := filepath.Ext(originalName)
		nameWithoutExt := strings.TrimSuffix(originalName, ext)
		safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
		safeName = strings.ReplaceAll(safeName, ":", "_")
		safeName = strings.ReplaceAll(safeName, "/", "_")
		safeName = strings.ReplaceAll(safeName, "\\", "_")

		// Check if the same image is being uploaded (by comparing original filename)
		if currentImageName != "" && strings.Contains(currentImageName, safeName) && strings.HasSuffix(currentImageName, ext) {
			h.logger.Info("Same image uploaded, skipping processing",
				zap.String("property_type_id", id.Hex()),
				zap.String("filename", originalName),
			)
			// Keep the existing image URL
			newImageURL = existingPropertyType.ImageURL
		} else {
			// Create uploads directory only when needed
			uploadDir := "uploads/property-types"
			if err := os.MkdirAll(uploadDir, 0755); err != nil {
				h.logger.Error("Failed to create uploads directory",
					zap.String("error", err.Error()),
					zap.String("property_type_id", id.Hex()),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
				return
			}

			// Generate unique filename using timestamp and random string (consistent with other APIs)
			timestamp := time.Now().UnixNano()
			randomStr := primitive.NewObjectID().Hex()
			filename := fmt.Sprintf("property_type_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
			filePath := filepath.Join(uploadDir, filename)

			// Save new file
			if err := c.SaveUploadedFile(file, filePath); err != nil {
				h.logger.Error("Failed to save property type image",
					zap.String("error", err.Error()),
					zap.String("property_type_id", id.Hex()),
					zap.String("filename", filename),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to save property type image"})
				return
			}

			// Delete previous image if it exists
			if existingPropertyType.ImageURL != "" {
				// Convert URL path to filesystem path
				oldImagePath := strings.TrimPrefix(existingPropertyType.ImageURL, "/")
				if err := os.Remove(oldImagePath); err != nil {
					// Log warning but don't fail the request
					h.logger.Warn("Failed to delete previous property type image",
						zap.String("error", err.Error()),
						zap.String("property_type_id", id.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				} else {
					h.logger.Info("Previous property type image deleted successfully",
						zap.String("property_type_id", id.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				}
			}

			// Set new image URL (use forward slashes for URLs)
			newImageURL = fmt.Sprintf("/uploads/property-types/%s", filename)

			h.logger.Info("New property type image uploaded successfully",
				zap.String("property_type_id", id.Hex()),
				zap.String("filename", filename),
				zap.String("url", newImageURL),
			)
		}
	} else {
		// No image file provided, keep existing image
		newImageURL = existingPropertyType.ImageURL
	}

	// Convert request to bson.M for update
	update := bson.M{}

	// Only add fields that are provided in the request
	if req.Name != "" {
		update["name"] = req.Name
	}
	if req.Order != 0 {
		update["order"] = req.Order
	}

	// Always update image URL with the processed result
	update["image_url"] = newImageURL

	// Handle Active field if provided
	if req.Active != nil {
		update["active"] = *req.Active
	}

	h.logger.Info("Processing property type update request",
		zap.String("property_type_id", id.Hex()),
		zap.Any("update", update),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.UpdatePropertyType(c.Request.Context(), id, update); err != nil {
		h.logger.Error("Property type update failed",
			zap.String("property_type_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "property type not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("Property type updated successfully",
		zap.String("property_type_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "property type updated successfully"})
}

// DeletePropertyType handles property type deletion
// @Summary Delete a property type
// @Description Delete a property type by ID (admin only)
// @Tags admin,property-types
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Property Type ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "Property type not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/property-types/{id} [delete]
func (h *PropertyTypeHandler) DeletePropertyType(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property type ID format for deletion",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property type id"})
		return
	}

	h.logger.Info("Processing property type deletion request",
		zap.String("property_type_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.DeletePropertyType(c.Request.Context(), id); err != nil {
		h.logger.Error("Property type deletion failed",
			zap.String("property_type_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "property type not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("Property type deleted successfully",
		zap.String("property_type_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "property type deleted successfully"})
}
