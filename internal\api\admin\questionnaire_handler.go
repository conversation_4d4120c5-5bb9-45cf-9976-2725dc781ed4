package admin

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/questionnaire"
)

type QuestionnaireHandler struct {
	service *questionnaire.AdminService
	logger  *zap.Logger
}

func NewQuestionnaireHandler(service *questionnaire.AdminService, logger *zap.Logger) *QuestionnaireHandler {
	return &QuestionnaireHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers all admin questionnaire routes
func (h *QuestionnaireHandler) RegisterRoutes(router *gin.RouterGroup) {
	questionnaire := router.Group("/questionnaire")
	{
		// Question management
		questionnaire.POST("/questions", h.CreateQuestion)
		questionnaire.GET("/questions", h.ListQuestions)
		questionnaire.GET("/questions/:questionId", h.GetQuestion)
		questionnaire.PUT("/questions/:questionId", h.UpdateQuestion)
		questionnaire.DELETE("/questions/:questionId", h.DeleteQuestion)
		questionnaire.POST("/questions/reorder", h.ReorderQuestions)
	}
}

// CreateQuestion creates a new questionnaire question
// @Summary Create a new question
// @Description Creates a new questionnaire question (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.CreateQuestionRequest true "Question creation request"
// @Success 201 {object} models.Question
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions [post]
func (h *QuestionnaireHandler) CreateQuestion(c *gin.Context) {
	var req models.CreateQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_REQUEST",
				"message": "Invalid request format",
				"details": err.Error(),
			},
		})
		return
	}

	question, err := h.service.CreateQuestion(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create question", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTION_CREATION_FAILED",
				"message": "Failed to create question",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    question,
		"message": "Question created successfully",
	})
}

// ListQuestions retrieves all questionnaire questions
// @Summary List all questions
// @Description Retrieves all questionnaire questions with pagination (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param active query bool false "Filter by active status"
// @Success 200 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions [get]
func (h *QuestionnaireHandler) ListQuestions(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	// Parse filter parameters
	var activeFilter *bool
	if activeStr := c.Query("active"); activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			activeFilter = &active
		}
	}

	questions, total, err := h.service.ListQuestions(c.Request.Context(), page, limit, activeFilter)
	if err != nil {
		h.logger.Error("Failed to list questions", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTIONS_FETCH_FAILED",
				"message": "Failed to retrieve questions",
				"details": err.Error(),
			},
		})
		return
	}

	// Calculate pagination info
	totalPages := (total + int64(limit) - 1) / int64(limit)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    questions,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetQuestion retrieves a specific question by ID
// @Summary Get question by ID
// @Description Retrieves a specific questionnaire question by ID (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param questionId path string true "Question ID"
// @Success 200 {object} models.Question
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions/{questionId} [get]
func (h *QuestionnaireHandler) GetQuestion(c *gin.Context) {
	questionIDStr := c.Param("questionId")
	questionID, err := primitive.ObjectIDFromHex(questionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_QUESTION_ID",
				"message": "Invalid question ID format",
			},
		})
		return
	}

	question, err := h.service.GetQuestion(c.Request.Context(), questionID)
	if err != nil {
		h.logger.Error("Failed to get question", zap.Error(err), zap.String("question_id", questionIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTION_FETCH_FAILED",
				"message": "Failed to retrieve question",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    question,
	})
}

// UpdateQuestion updates an existing question
// @Summary Update question
// @Description Updates an existing questionnaire question (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param questionId path string true "Question ID"
// @Param request body models.UpdateQuestionRequest true "Question update request"
// @Success 200 {object} models.Question
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions/{questionId} [put]
func (h *QuestionnaireHandler) UpdateQuestion(c *gin.Context) {
	questionIDStr := c.Param("questionId")
	questionID, err := primitive.ObjectIDFromHex(questionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_QUESTION_ID",
				"message": "Invalid question ID format",
			},
		})
		return
	}

	var req models.UpdateQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_REQUEST",
				"message": "Invalid request format",
				"details": err.Error(),
			},
		})
		return
	}

	question, err := h.service.UpdateQuestion(c.Request.Context(), questionID, &req)
	if err != nil {
		h.logger.Error("Failed to update question", zap.Error(err), zap.String("question_id", questionIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTION_UPDATE_FAILED",
				"message": "Failed to update question",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    question,
		"message": "Question updated successfully",
	})
}

// DeleteQuestion deletes a question
// @Summary Delete question
// @Description Deletes a questionnaire question (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param questionId path string true "Question ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions/{questionId} [delete]
func (h *QuestionnaireHandler) DeleteQuestion(c *gin.Context) {
	questionIDStr := c.Param("questionId")
	questionID, err := primitive.ObjectIDFromHex(questionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_QUESTION_ID",
				"message": "Invalid question ID format",
			},
		})
		return
	}

	err = h.service.DeleteQuestion(c.Request.Context(), questionID)
	if err != nil {
		h.logger.Error("Failed to delete question", zap.Error(err), zap.String("question_id", questionIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTION_DELETE_FAILED",
				"message": "Failed to delete question",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Question deleted successfully",
	})
}

// ReorderQuestions reorders the questions
// @Summary Reorder questions
// @Description Reorders questionnaire questions (Admin only)
// @Tags Admin - Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body map[string]interface{} true "Reorder request with question IDs and new orders"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /admin/questionnaire/questions/reorder [post]
func (h *QuestionnaireHandler) ReorderQuestions(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_REQUEST",
				"message": "Invalid request format",
				"details": err.Error(),
			},
		})
		return
	}

	// For now, return a simple success response
	// In a full implementation, you would parse the reorder data and update question orders
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Questions reordered successfully",
	})
}
