package admin

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/notification"
	userService "realestate-platform/internal/services/user"
)

type UserHandler struct {
	service             *userService.Service
	notificationService *notification.Service
	logger              *zap.Logger
}

func NewUserHandler(service *userService.Service, notificationService *notification.Service, logger *zap.Logger) *UserHandler {
	return &UserHandler{
		service:             service,
		notificationService: notificationService,
		logger:              logger,
	}
}

// RegisterRoutes registers the user management routes
func (h *UserHandler) RegisterRoutes(router *gin.RouterGroup) {
	users := router.Group("/users")
	{
		users.GET("", h.ListUsers)
		users.GET("/:id", h.GetUser)
		users.PUT("/:id/block", h.BlockUser)
		users.PUT("/:id/unblock", h.UnblockUser)
	}
}

// ListUsers handles listing users with pagination and filters
// @Summary List users
// @Description Get a list of users with pagination and filtering options (admin only)
// @Tags admin,users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param email query string false "Filter by email"
// @Param role query string false "Filter by role"
// @Param is_blocked query bool false "Filter by blocked status"
// @Success 200 {object} models.PaginatedResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	// Build filter
	filter := bson.M{}
	if email := c.Query("email"); email != "" {
		filter["email"] = bson.M{"$regex": email, "$options": "i"}
	}
	if role := c.Query("role"); role != "" {
		filter["role"] = role
	}
	if isBlocked := c.Query("is_blocked"); isBlocked != "" {
		isBlockedBool, _ := strconv.ParseBool(isBlocked)
		filter["is_blocked"] = isBlockedBool
	}

	h.logger.Info("Processing users list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	users, total, err := h.service.ListUsers(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Users listing failed",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(float64(total)/float64(limit) + 0.5)
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Users list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(users)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: users,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetUser handles getting a user by ID
// @Summary Get a user
// @Description Get a user by ID (admin only)
// @Tags admin,users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} models.User
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/users/{id} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid user ID format",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user id"})
		return
	}

	h.logger.Info("Processing user retrieval request",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	user, err := h.service.GetUser(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("User retrieval failed",
			zap.String("id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("User retrieved successfully",
		zap.String("id", user.ID.Hex()),
		zap.String("email", user.Email),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, user)
}

// BlockUser handles blocking a user
// @Summary Block a user
// @Description Block a user by ID (admin only)
// @Tags admin,users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/users/{id}/block [put]
func (h *UserHandler) BlockUser(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid user ID format for block",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user id"})
		return
	}

	h.logger.Info("Processing user block request",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.UpdateUserBlockStatus(c.Request.Context(), id, true); err != nil {
		h.logger.Error("User block failed",
			zap.String("id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// Notify user about being blocked
	if err := h.notificationService.NotifyUserBlocked(c.Request.Context(), id); err != nil {
		h.logger.Error("Failed to send user block notification",
			zap.String("user_id", id.Hex()),
			zap.String("error", err.Error()),
		)
		// Don't fail the request if notification fails
	}

	h.logger.Info("User blocked successfully",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "user blocked successfully"})
}

// UnblockUser handles unblocking a user
// @Summary Unblock a user
// @Description Unblock a user by ID (admin only)
// @Tags admin,users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "User ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/users/{id}/unblock [put]
func (h *UserHandler) UnblockUser(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid user ID format for unblock",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user id"})
		return
	}

	h.logger.Info("Processing user unblock request",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.UpdateUserBlockStatus(c.Request.Context(), id, false); err != nil {
		h.logger.Error("User unblock failed",
			zap.String("id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	// Notify user about being unblocked
	if err := h.notificationService.NotifyUserUnblocked(c.Request.Context(), id); err != nil {
		h.logger.Error("Failed to send user unblock notification",
			zap.String("user_id", id.Hex()),
			zap.String("error", err.Error()),
		)
		// Don't fail the request if notification fails
	}

	h.logger.Info("User unblocked successfully",
		zap.String("id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "user unblocked successfully"})
}
