package favorite

import (
	"math"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/favorite"
)

type Handler struct {
	service *favorite.Service
	logger  *zap.Logger
}

func NewHandler(service *favorite.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers all favorite routes
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	favorites := router.Group("/favorites")
	{
		favorites.POST("", h.AddFavorite)
		favorites.DELETE("/:propertyId", h.RemoveFavorite)
		favorites.GET("", h.ListFavorites)
	}
}

// AddFavorite handles adding a property to favorites
// @Summary Add a property to favorites
// @Description Add a property to the authenticated user's favorites
// @Tags favorites
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.FavoriteRequest true "Property ID"
// @Success 201 {object} models.FavoriteResponse "Success message"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Property not found"
// @Failure 409 {object} map[string]string "Property already in favorites"
// @Failure 500 {object} map[string]string "Server error"
// @Router /favorites [post]
func (h *Handler) AddFavorite(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	userID, ok := userIDStr.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID format in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID format"})
		return
	}

	// Parse request body
	var req models.FavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid add favorite request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert property ID from string to ObjectID
	propertyID, err := primitive.ObjectIDFromHex(req.PropertyID)
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("property_id", req.PropertyID),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property ID format"})
		return
	}

	// Add to favorites
	err = h.service.AddFavorite(c.Request.Context(), userID, propertyID)
	if err != nil {
		switch err {
		case favorite.ErrPropertyNotFound:
			h.logger.Warn("Property not found",
				zap.String("property_id", propertyID.Hex()),
				zap.String("user_id", userID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "property not found"})
		case favorite.ErrFavoriteExists:
			h.logger.Warn("Property already in favorites",
				zap.String("property_id", propertyID.Hex()),
				zap.String("user_id", userID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusConflict, gin.H{"error": "property already in favorites"})
		default:
			h.logger.Error("Failed to add favorite",
				zap.String("property_id", propertyID.Hex()),
				zap.String("user_id", userID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to add favorite"})
		}
		return
	}

	h.logger.Info("Property added to favorites",
		zap.String("property_id", propertyID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("ip", c.ClientIP()),
	)
	c.JSON(http.StatusCreated, models.FavoriteResponse{
		Success: true,
		Message: "property added to favorites",
	})
}

// RemoveFavorite handles removing a property from favorites
// @Summary Remove a property from favorites
// @Description Remove a property from the authenticated user's favorites
// @Tags favorites
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param propertyId path string true "Property ID"
// @Success 200 {object} models.FavoriteResponse "Success message"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Favorite not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /favorites/{propertyId} [delete]
func (h *Handler) RemoveFavorite(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	userID, ok := userIDStr.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID format in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID format"})
		return
	}

	// Get property ID from URL
	propertyIDStr := c.Param("propertyId")
	propertyID, err := primitive.ObjectIDFromHex(propertyIDStr)
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("property_id", propertyIDStr),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property ID format"})
		return
	}

	// Remove from favorites
	err = h.service.RemoveFavorite(c.Request.Context(), userID, propertyID)
	if err != nil {
		if err == favorite.ErrFavoriteNotFound {
			h.logger.Warn("Favorite not found",
				zap.String("property_id", propertyID.Hex()),
				zap.String("user_id", userID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "favorite not found"})
			return
		}

		h.logger.Error("Failed to remove favorite",
			zap.String("property_id", propertyID.Hex()),
			zap.String("user_id", userID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove favorite"})
		return
	}

	h.logger.Info("Property removed from favorites",
		zap.String("property_id", propertyID.Hex()),
		zap.String("user_id", userID.Hex()),
		zap.String("ip", c.ClientIP()),
	)
	c.JSON(http.StatusOK, models.FavoriteResponse{
		Success: true,
		Message: "property removed from favorites",
	})
}

// ListFavorites handles listing all favorites for a user
// @Summary List user's favorite properties
// @Description Get all favorite properties for the authenticated user with filtering options
// @Tags favorites
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Param type query string false "Filter by property type (apartment, house, villa, commercial, land)"
// @Param status query string false "Filter by property status (rent, sell, rented, sold)"
// @Param min_price query number false "Minimum price"
// @Param max_price query number false "Maximum price"
// @Param bhk query int false "Filter by BHK"
// @Param bedroom query int false "Filter by bedroom count"
// @Param bathroom query int false "Filter by bathroom count"
// @Param furniture query string false "Filter by furniture type (furnished, semi-furnished, unfurnished)"
// @Success 200 {object} models.PaginatedResponse "List of favorite properties"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /favorites [get]
func (h *Handler) ListFavorites(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	userID, ok := userIDStr.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID format in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID format"})
		return
	}

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.ParseInt(pageStr, 10, 64)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	// Build filters from query parameters
	filters := bson.M{}

	// Property type filter
	if propertyType := c.Query("type"); propertyType != "" {
		validTypes := map[string]bool{
			"apartment":  true,
			"house":      true,
			"villa":      true,
			"commercial": true,
			"land":       true,
		}
		if validTypes[propertyType] {
			filters["type"] = propertyType
		}
	}

	// Property status filter
	if status := c.Query("status"); status != "" {
		validStatuses := map[string]bool{
			"rent":   true,
			"sell":   true,
			"rented": true,
			"sold":   true,
		}
		if validStatuses[status] {
			filters["status"] = status
		}
	}

	// Price range filters
	if minPrice := c.Query("min_price"); minPrice != "" {
		if minPriceFloat, err := strconv.ParseFloat(minPrice, 64); err == nil && minPriceFloat > 0 {
			filters["total_price"] = bson.M{"$gte": minPriceFloat}
		}
	}
	if maxPrice := c.Query("max_price"); maxPrice != "" {
		if maxPriceFloat, err := strconv.ParseFloat(maxPrice, 64); err == nil && maxPriceFloat > 0 {
			if _, exists := filters["total_price"]; exists {
				filters["total_price"].(bson.M)["$lte"] = maxPriceFloat
			} else {
				filters["total_price"] = bson.M{"$lte": maxPriceFloat}
			}
		}
	}

	// BHK filter
	if bhk := c.Query("bhk"); bhk != "" {
		if bhkInt, err := strconv.Atoi(bhk); err == nil && bhkInt > 0 {
			filters["bhk"] = bhkInt
		}
	}

	// Bedroom filter
	if bedroom := c.Query("bedroom"); bedroom != "" {
		if bedroomInt, err := strconv.Atoi(bedroom); err == nil && bedroomInt > 0 {
			filters["bedroom"] = bedroomInt
		}
	}

	// Bathroom filter
	if bathroom := c.Query("bathroom"); bathroom != "" {
		if bathroomInt, err := strconv.Atoi(bathroom); err == nil && bathroomInt > 0 {
			filters["bathroom"] = bathroomInt
		}
	}

	// Furniture type filter
	if furniture := c.Query("furniture"); furniture != "" {
		validFurniture := map[string]bool{
			"furnished":      true,
			"semi-furnished": true,
			"unfurnished":    true,
		}
		if validFurniture[furniture] {
			filters["furniture"] = furniture
		}
	}

	// Get favorites with filters
	properties, total, err := h.service.ListFavoritesByUserID(c.Request.Context(), userID, page, limit, filters)
	if err != nil {
		h.logger.Error("Failed to list favorites",
			zap.String("user_id", userID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list favorites"})
		return
	}

	h.logger.Info("Listed favorites",
		zap.String("user_id", userID.Hex()),
		zap.Int("count", len(properties)),
		zap.Int64("total", total),
		zap.Any("filters", filters),
		zap.String("ip", c.ClientIP()),
	)

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	// Create paginated response
	response := models.PaginatedResponse{
		Data: properties,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}
