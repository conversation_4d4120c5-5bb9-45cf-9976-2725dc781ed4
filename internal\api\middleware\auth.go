package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"net/http"
	"realestate-platform/internal/config"
	"strings"
	"time"
)

func AuthMiddleware(cfg *config.Config, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method
		ip := c.ClientIP()

		logger.Info("Processing authentication request",
			zap.String("path", path),
			zap.String("method", method),
			zap.String("ip", ip),
		)

		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logger.Warn("Authentication failed - missing authorization header",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("ip", ip),
			)
			c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "authorization header is required"})
			c.Abort()
			return
		}

		// Extract the token from the Authorization header
		// Format: "Bearer <token>"
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			logger.Warn("Authentication failed - invalid authorization header format",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("ip", ip),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid authorization header format"})
			c.Abort()
			return
		}

		tokenString := parts[1]
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return []byte(cfg.JWT.SecretKey), nil
		})

		if err != nil {
			logger.Warn("Authentication failed - invalid token",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("error", err.Error()),
				zap.String("ip", ip),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			c.Abort()
			return
		}

		if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
			// Check if token is expired
			if exp, ok := claims["exp"].(float64); ok {
				if time.Now().Unix() > int64(exp) {
					logger.Warn("Authentication failed - token expired",
						zap.String("path", path),
						zap.String("method", method),
						zap.String("ip", ip),
					)
					c.JSON(http.StatusUnauthorized, gin.H{"error": "token has expired"})
					c.Abort()
					return
				}
			}

			// Set user ID in context
			if userID, ok := claims["user_id"].(string); ok {
				objectID, err := primitive.ObjectIDFromHex(userID)
				if err != nil {
					logger.Warn("Authentication failed - invalid user ID format",
						zap.String("path", path),
						zap.String("method", method),
						zap.String("user_id", userID),
						zap.String("error", err.Error()),
						zap.String("ip", ip),
					)
					c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid user id"})
					c.Abort()
					return
				}
				c.Set("user_id", objectID)
			}

			// Set user role in context
			if role, ok := claims["role"].(string); ok {
				c.Set("user_role", role)
			}

			// Set first name in context
			if firstName, ok := claims["first_name"].(string); ok {
				c.Set("first_name", firstName)
			}

			// Set last name in context
			if lastName, ok := claims["last_name"].(string); ok {
				c.Set("last_name", lastName)
			}

			// Log successful authentication
			userID, _ := c.Get("user_id")
			userRole, _ := c.Get("user_role")

			logger.Info("Authentication successful",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("user_role", userRole.(string)),
				zap.String("ip", ip),
			)

			c.Next()
		} else {
			logger.Warn("Authentication failed - invalid token claims",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("ip", ip),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token claims"})
			c.Abort()
			return
		}
	}
}

// RoleMiddleware checks if the user has the required role
func RoleMiddleware(requiredRole string, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method
		ip := c.ClientIP()

		userRole, exists := c.Get("user_role")
		if !exists {
			logger.Warn("Role check failed - user role not found",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("required_role", requiredRole),
				zap.String("ip", ip),
			)
			c.JSON(http.StatusUnauthorized, gin.H{"error": "user role not found"})
			c.Abort()
			return
		}

		if userRole.(string) != requiredRole {
			logger.Warn("Role check failed - insufficient permissions",
				zap.String("path", path),
				zap.String("method", method),
				zap.String("user_role", userRole.(string)),
				zap.String("required_role", requiredRole),
				zap.String("ip", ip),
			)
			c.JSON(http.StatusForbidden, gin.H{"error": "insufficient permissions"})
			c.Abort()
			return
		}

		userID, _ := c.Get("user_id")
		logger.Info("Role check successful",
			zap.String("path", path),
			zap.String("method", method),
			zap.String("user_id", userID.(primitive.ObjectID).Hex()),
			zap.String("user_role", userRole.(string)),
			zap.String("required_role", requiredRole),
			zap.String("ip", ip),
		)

		c.Next()
	}
}
