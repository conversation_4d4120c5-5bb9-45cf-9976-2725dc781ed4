package middleware

import (
	"bytes"
	"encoding/json"
	"io"

	"realestate-platform/internal/sanitizer"

	"github.com/gin-gonic/gin"
)

// ContextKey is a type for context keys
type ContextKey string

// SanitizationMessagesKey is the key for storing sanitization messages in context
const SanitizationMessagesKey ContextKey = "sanitization_messages"

// SanitizerMiddleware sanitizes all incoming request data
func SanitizerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Initialize sanitization messages slice
		sanitizationMessages := make([]string, 0)

		// Sanitize URL parameters
		for _, param := range c.Params {
			result := sanitizer.SanitizeFieldWithFeedback("URL parameter '"+param.Key+"'", param.Value)
			if !result.IsValid {
				sanitizationMessages = append(sanitizationMessages, result.Feedback)
			}
			param.Value = result.SanitizedValue
		}

		// Sanitize query parameters
		for key, values := range c.Request.URL.Query() {
			for i, value := range values {
				result := sanitizer.SanitizeFieldWithFeedback("Query parameter '"+key+"'", value)
				if !result.IsValid {
					sanitizationMessages = append(sanitizationMessages, result.Feedback)
				}
				values[i] = result.SanitizedValue
			}
			c.Request.URL.RawQuery = c.Request.URL.Query().Encode()
		}

		// Sanitize headers
		for key, values := range c.Request.Header {
			for i, value := range values {
				result := sanitizer.SanitizeFieldWithFeedback("Header '"+key+"'", value)
				if !result.IsValid {
					sanitizationMessages = append(sanitizationMessages, result.Feedback)
				}
				values[i] = result.SanitizedValue
			}
			c.Request.Header[key] = values
		}

		// Sanitize request body
		if c.Request.Body != nil && c.Request.Method != "GET" {
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err == nil {
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

				// Try to parse as JSON
				var jsonData map[string]interface{}
				if err := json.Unmarshal(bodyBytes, &jsonData); err == nil {
					// Log the original request body for debugging

					// Sanitize string values in the JSON
					sanitizedData, messages := sanitizer.SanitizeMapWithFeedback(jsonData)
					sanitizationMessages = append(sanitizationMessages, messages...)

					// Special handling for email and password fields
					if email, ok := jsonData["email"].(string); ok {
						emailResult := sanitizer.SanitizeEmailWithFeedback(email)
						if !emailResult.IsValid {
							sanitizationMessages = append(sanitizationMessages, emailResult.Feedback)
						}
						sanitizedData["email"] = emailResult.SanitizedValue
					}

					if password, ok := jsonData["password"].(string); ok {
						passwordResult := sanitizer.SanitizePasswordWithFeedback(password)
						if !passwordResult.IsValid {
							sanitizationMessages = append(sanitizationMessages, passwordResult.Feedback)
						}
						sanitizedData["password"] = passwordResult.SanitizedValue
					}

					// Special handling for first_name and last_name fields
					if firstName, ok := jsonData["first_name"].(string); ok {
						// Log the first_name value for debugging
						// log.Printf("First name before sanitization: %s", firstName)

						firstNameResult := sanitizer.SanitizeFieldWithFeedback("First Name", firstName)
						if !firstNameResult.IsValid {
							sanitizationMessages = append(sanitizationMessages, firstNameResult.Feedback)
						}
						sanitizedData["first_name"] = firstNameResult.SanitizedValue

						// Log the sanitization result for debugging
						// log.Printf("First name after sanitization: %s, Feedback: %s", firstNameResult.SanitizedValue, firstNameResult.Feedback)
					}

					if lastName, ok := jsonData["last_name"].(string); ok {
						lastNameResult := sanitizer.SanitizeFieldWithFeedback("Last Name", lastName)
						if !lastNameResult.IsValid {
							sanitizationMessages = append(sanitizationMessages, lastNameResult.Feedback)
						}
						sanitizedData["last_name"] = lastNameResult.SanitizedValue
					}

					// Replace the request body with sanitized data
					sanitizedBytes, err := json.Marshal(sanitizedData)
					if err == nil {
						c.Request.Body = io.NopCloser(bytes.NewBuffer(sanitizedBytes))
					}

					// Log the sanitization messages for debugging
					// log.Printf("Sanitization messages: %v", sanitizationMessages)
				}
			}
		}

		// Store sanitization messages in context
		c.Set(string(SanitizationMessagesKey), sanitizationMessages)

		// Add a response modifier to include sanitization messages
		c.Writer = &responseWriter{
			ResponseWriter: c.Writer,
			context:        c,
		}

		c.Next()
	}
}

// responseWriter is a custom response writer that adds sanitization messages
type responseWriter struct {
	gin.ResponseWriter
	context *gin.Context
}

// WriteJSON overrides the default JSON response to include sanitization messages
func (w *responseWriter) WriteJSON(code int, obj interface{}) error {
	// Get sanitization messages from context
	messages, exists := w.context.Get(string(SanitizationMessagesKey))
	if !exists || len(messages.([]string)) == 0 {
		// If no messages, just write the original response
		w.context.JSON(code, obj)
		return nil
	}

	// Create a new response with sanitization messages
	response := gin.H{
		"data": obj,
		"sanitization": gin.H{
			"messages": messages,
		},
	}

	// Write the modified response
	w.context.JSON(code, response)
	return nil
}

// WriteJSONResponse is a helper function to write JSON responses with sanitization messages
func WriteJSONResponse(c *gin.Context, code int, obj interface{}) {
	// Check if we have a custom response writer
	if writer, ok := c.Writer.(*responseWriter); ok {
		writer.WriteJSON(code, obj)
		return
	}

	// Fall back to standard JSON response
	c.JSON(code, obj)
}
