package notification

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/notification"
)

type Handler struct {
	service *notification.Service
	logger  *zap.Logger
}

// NewHandler creates a new notification handler
func NewHandler(service *notification.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterUserRoutes registers notification routes for normal users
func (h *Handler) RegisterUserRoutes(router *gin.RouterGroup) {
	notifications := router.Group("/notifications")
	{
		notifications.GET("", h.GetUserNotifications)
		notifications.GET("/count", h.GetUserNotificationCount)
		notifications.PUT("/:id/read", h.MarkNotificationAsRead)
		notifications.PUT("/read-all", h.<PERSON>otificationsAsRead)
	}
}

// RegisterAdminRoutes registers notification routes for admin
func (h *Handler) RegisterAdminRoutes(router *gin.RouterGroup) {
	notifications := router.Group("/notifications")
	{
		notifications.GET("", h.GetAdminNotifications)
		notifications.GET("/count", h.GetAdminNotificationCount)
		notifications.PUT("/:id/read", h.MarkNotificationAsRead)
		notifications.PUT("/read-all", h.MarkAllNotificationsAsRead)
	}
}

// GetUserNotifications handles getting notifications for normal users
// @Summary Get user notifications
// @Description Get paginated notifications for the authenticated user
// @Tags notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 20, max: 50)"
// @Param only_unread query bool false "Show only unread notifications"
// @Success 200 {object} models.GetNotificationsResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/notifications [get]
func (h *Handler) GetUserNotifications(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("Unauthorized notification access attempt",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	onlyUnread, _ := strconv.ParseBool(c.DefaultQuery("only_unread", "false"))

	req := &models.GetNotificationsRequest{
		Page:       page,
		Limit:      limit,
		OnlyUnread: onlyUnread,
	}

	h.logger.Info("Processing user notifications request",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.Bool("only_unread", onlyUnread),
		zap.String("ip", c.ClientIP()),
	)

	response, err := h.service.GetNotifications(c.Request.Context(), userID.(primitive.ObjectID), req)
	if err != nil {
		h.logger.Error("Failed to get user notifications",
			zap.String("user_id", userID.(primitive.ObjectID).Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get notifications"})
		return
	}

	h.logger.Info("User notifications retrieved successfully",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.Int64("total_count", response.TotalCount),
		zap.Int64("unread_count", response.UnreadCount),
		zap.Int("returned_count", len(response.Notifications)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, response)
}

// GetAdminNotifications handles getting notifications for admin
// @Summary Get admin notifications
// @Description Get paginated notifications for the admin
// @Tags admin,notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 20, max: 50)"
// @Param only_unread query bool false "Show only unread notifications"
// @Success 200 {object} models.GetNotificationsResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/notifications [get]
func (h *Handler) GetAdminNotifications(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("Unauthorized admin notification access attempt",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	onlyUnread, _ := strconv.ParseBool(c.DefaultQuery("only_unread", "false"))

	req := &models.GetNotificationsRequest{
		Page:       page,
		Limit:      limit,
		OnlyUnread: onlyUnread,
	}

	h.logger.Info("Processing admin notifications request",
		zap.String("admin_id", userID.(primitive.ObjectID).Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.Bool("only_unread", onlyUnread),
		zap.String("ip", c.ClientIP()),
	)

	response, err := h.service.GetNotifications(c.Request.Context(), userID.(primitive.ObjectID), req)
	if err != nil {
		h.logger.Error("Failed to get admin notifications",
			zap.String("admin_id", userID.(primitive.ObjectID).Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get notifications"})
		return
	}

	h.logger.Info("Admin notifications retrieved successfully",
		zap.String("admin_id", userID.(primitive.ObjectID).Hex()),
		zap.Int64("total_count", response.TotalCount),
		zap.Int64("unread_count", response.UnreadCount),
		zap.Int("returned_count", len(response.Notifications)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, response)
}

// GetUserNotificationCount handles getting notification counts for users
// @Summary Get user notification count
// @Description Get total and unread notification counts for the authenticated user
// @Tags notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.NotificationCountResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/notifications/count [get]
func (h *Handler) GetUserNotificationCount(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	response, err := h.service.GetNotificationCounts(c.Request.Context(), userID.(primitive.ObjectID))
	if err != nil {
		h.logger.Error("Failed to get user notification counts",
			zap.String("user_id", userID.(primitive.ObjectID).Hex()),
			zap.String("error", err.Error()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get notification counts"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetAdminNotificationCount handles getting notification counts for admin
// @Summary Get admin notification count
// @Description Get total and unread notification counts for the admin
// @Tags admin,notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.NotificationCountResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 403 {object} map[string]string "Forbidden"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/admin/notifications/count [get]
func (h *Handler) GetAdminNotificationCount(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	response, err := h.service.GetNotificationCounts(c.Request.Context(), userID.(primitive.ObjectID))
	if err != nil {
		h.logger.Error("Failed to get admin notification counts",
			zap.String("admin_id", userID.(primitive.ObjectID).Hex()),
			zap.String("error", err.Error()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get notification counts"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// MarkNotificationAsRead handles marking a specific notification as read
// @Summary Mark notification as read
// @Description Mark a specific notification as read for the authenticated user
// @Tags notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Notification ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid notification ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Notification not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/notifications/{id}/read [put]
// @Router /api/v1/admin/notifications/{id}/read [put]
func (h *Handler) MarkNotificationAsRead(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	notificationIDStr := c.Param("id")
	notificationID, err := primitive.ObjectIDFromHex(notificationIDStr)
	if err != nil {
		h.logger.Warn("Invalid notification ID format",
			zap.String("id", notificationIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid notification id"})
		return
	}

	h.logger.Info("Processing mark notification as read request",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("notification_id", notificationID.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	err = h.service.MarkAsRead(c.Request.Context(), userID.(primitive.ObjectID), notificationID)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			h.logger.Warn("Notification not found or not owned by user",
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("notification_id", notificationID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "notification not found"})
			return
		}

		h.logger.Error("Failed to mark notification as read",
			zap.String("user_id", userID.(primitive.ObjectID).Hex()),
			zap.String("notification_id", notificationID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to mark notification as read"})
		return
	}

	h.logger.Info("Notification marked as read successfully",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("notification_id", notificationID.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "notification marked as read"})
}

// MarkAllNotificationsAsRead handles marking all notifications as read
// @Summary Mark all notifications as read
// @Description Mark all notifications as read for the authenticated user
// @Tags notifications
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} map[string]string "Success message"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/notifications/read-all [put]
// @Router /api/v1/admin/notifications/read-all [put]
func (h *Handler) MarkAllNotificationsAsRead(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	h.logger.Info("Processing mark all notifications as read request",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	err := h.service.MarkAllAsRead(c.Request.Context(), userID.(primitive.ObjectID))
	if err != nil {
		h.logger.Error("Failed to mark all notifications as read",
			zap.String("user_id", userID.(primitive.ObjectID).Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to mark all notifications as read"})
		return
	}

	h.logger.Info("All notifications marked as read successfully",
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "all notifications marked as read"})
}
