package property

import (
	"encoding/json"
	"fmt"
	"math"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/favorite"
	"realestate-platform/internal/services/notification"
	"realestate-platform/internal/services/property"
	property_metadata "realestate-platform/internal/services/property_metadata"
)

type Handler struct {
	service             *property.Service
	favoriteService     *favorite.Service
	metadataService     *property_metadata.Service
	notificationService *notification.Service
	logger              *zap.Logger
}

func NewHandler(service *property.Service, favoriteService *favorite.Service, metadataService *property_metadata.Service, notificationService *notification.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service:             service,
		favoriteService:     favoriteService,
		metadataService:     metadataService,
		notificationService: notificationService,
		logger:              logger,
	}
}

// RegisterPublicRoutes registers public property routes (no authentication required)
func (h *Handler) RegisterPublicRoutes(router *gin.RouterGroup) {
	// Only property detail endpoint is public for sharing
	router.GET("/properties/:id", h.GetProperty)
}

// RegisterRoutes registers protected property routes (authentication required)
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	properties := router.Group("/properties")
	{
		properties.POST("", h.CreateProperty)
		properties.GET("", h.ListProperties)
		properties.GET("/premium", h.ListPremiumProperties)
		properties.POST("/comprehensive-search", h.ComprehensiveSearch)
		// Removed: properties.GET("/:id", h.GetProperty) - now in public routes
		properties.PUT("/:id", h.UpdateProperty)
		properties.DELETE("/:id", h.DeleteProperty)
		properties.GET("/user", h.GetPropertiesByUserID)
	}
}

// CreateProperty handles property creation
// @Summary Create a new property
// @Description Create a new property listing with images and facilities with image URLs
// @Tags properties
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param property formData string true "Property Information (JSON) - facilities should include image_url field"
// @Param images formData file false "Property Images"
// @Success 201 {object} models.Property
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties [post]
func (h *Handler) CreateProperty(c *gin.Context) {
	// Get owner ID from context (set by auth middleware)
	ownerID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("Unauthorized property creation attempt",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get property details from form
	propertyJSON := c.PostForm("property")
	if propertyJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "property details are required"})
		return
	}

	// Parse property details
	var req models.CreatePropertyRequest
	if err := json.Unmarshal([]byte(propertyJSON), &req); err != nil {
		h.logger.Warn("Invalid property creation request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property details format"})
		return
	}

	// Create validation error channel
	validationErrors := make(chan error, 1)

	// Run validations concurrently
	go func() {
		var err error
		// Validate required fields
		if req.Title == "" {
			err = fmt.Errorf("title is required")
		} else if req.Description == "" {
			err = fmt.Errorf("description is required")
		} else if req.Type == "" {
			err = fmt.Errorf("type is required")
		} else if req.Status == "" {
			err = fmt.Errorf("status is required")
		} else if req.OwnerName == "" {
			err = fmt.Errorf("owner_name is required")
		} else if req.Area <= 0 {
			err = fmt.Errorf("area must be greater than 0")
		} else if req.YearBuilt <= 0 {
			err = fmt.Errorf("year_built must be greater than 0")
		} else if req.BHK < 0 {
			err = fmt.Errorf("bhk must be greater than or equal to 0")
		} else if req.Bedroom < 0 {
			err = fmt.Errorf("bedroom must be greater than or equal to 0")
		} else if req.Bathroom < 0 {
			err = fmt.Errorf("bathroom must be greater than or equal to 0")
		} else if req.TotalPrice <= 0 {
			err = fmt.Errorf("total_price must be greater than 0")
		}

		// Validate location
		if req.Location == nil {
			err = fmt.Errorf("location is required")
		} else if req.Location.Address == "" {
			err = fmt.Errorf("location address is required")
		} else if req.Location.City == "" {
			err = fmt.Errorf("location city is required")
		} else if req.Location.State == "" {
			err = fmt.Errorf("location state is required")
		} else if req.Location.Country == "" {
			err = fmt.Errorf("location country is required")
		} else if req.Location.ZipCode == "" {
			err = fmt.Errorf("location zip_code is required")
		}

		// Validate property type
		validTypes := map[string]bool{
			"apartment":  true,
			"house":      true,
			"villa":      true,
			"commercial": true,
			"land":       true,
		}
		if !validTypes[req.Type] {
			err = fmt.Errorf("invalid property type")
		}

		// Validate property status
		validStatuses := map[string]bool{
			"rent":   true,
			"rented": true,
			"sold":   true,
			"sell":   true,
		}
		if !validStatuses[req.Status] {
			err = fmt.Errorf("invalid property status")
		}

		// Validate furniture type if provided
		if req.Furniture != "" {
			validFurniture := map[string]bool{
				"furnished":      true,
				"semi-furnished": true,
				"unfurnished":    true,
			}
			if !validFurniture[req.Furniture] {
				err = fmt.Errorf("invalid furniture type")
			}
		}

		// Validate facility IDs if provided
		if len(req.Facilities) > 0 {
			for i, facility := range req.Facilities {
				if facility.FacilityID.IsZero() {
					err = fmt.Errorf("facility_id is required for facility at index %d", i)
					break
				}
				if facility.Name == "" {
					err = fmt.Errorf("facility name is required for facility at index %d", i)
					break
				}
				if facility.ImageURL == "" {
					err = fmt.Errorf("facility image_url is required for facility at index %d", i)
					break
				}
			}
		}

		validationErrors <- err
	}()

	// Wait for validation results
	if err := <-validationErrors; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate facility IDs against database if facilities are provided
	if len(req.Facilities) > 0 {
		for i, facility := range req.Facilities {
			// Check if facility exists in database
			existingFacility, err := h.metadataService.GetFacility(c.Request.Context(), facility.FacilityID)
			if err != nil {
				h.logger.Error("Failed to validate facility",
					zap.String("facility_id", facility.FacilityID.Hex()),
					zap.String("error", err.Error()),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to validate facility"})
				return
			}
			if existingFacility == nil {
				h.logger.Warn("Invalid facility ID provided",
					zap.String("facility_id", facility.FacilityID.Hex()),
					zap.Int("facility_index", i),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("facility with ID %s not found", facility.FacilityID.Hex())})
				return
			}
			// Check if facility is active
			if !existingFacility.Active {
				h.logger.Warn("Inactive facility ID provided",
					zap.String("facility_id", facility.FacilityID.Hex()),
					zap.String("facility_name", existingFacility.Name),
					zap.Int("facility_index", i),
					zap.String("ip", c.ClientIP()),
				)
				c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("facility '%s' is not active", existingFacility.Name)})
				return
			}
		}
	}

	// Create property from request
	property := &models.Property{
		Title:       req.Title,
		Description: req.Description,
		Type:        req.Type,
		Status:      req.Status,
		Location: models.Location{
			Address:      req.Location.Address,
			City:         req.Location.City,
			Area:         req.Location.Area,
			State:        req.Location.State,
			Country:      req.Location.Country,
			ZipCode:      req.Location.ZipCode,
			Latitude:     req.Location.Latitude,
			Longitude:    req.Location.Longitude,
			Neighborhood: req.Location.Neighborhood,
		},
		Area:               req.Area,
		YearBuilt:          req.YearBuilt,
		BHK:                req.BHK,
		Bedroom:            req.Bedroom,
		Bathroom:           req.Bathroom,
		NoOfParking:        req.NoOfParking,
		Rating:             0,
		OwnerID:            ownerID.(primitive.ObjectID),
		OwnerName:          req.OwnerName,
		Facilities:         req.Facilities,
		Facing:             req.Facing,
		Furniture:          req.Furniture,
		TotalPrice:         req.TotalPrice,
		TotalFloor:         req.TotalFloor,
		IsPropertyVerified: false,
		IsPropertyActive:   true,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}

	// Create uploads directory if it doesn't exist
	uploadDir := "uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		h.logger.Error("Failed to create uploads directory",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
		return
	}

	// Process uploaded images concurrently
	form, err := c.MultipartForm()
	if err != nil {
		h.logger.Warn("Failed to get multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to get form data"})
		return
	}

	files := form.File["images"]
	var imageURLs []string
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make(chan error, len(files))

	// Process each image concurrently
	for _, file := range files {
		wg.Add(1)
		go func(file *multipart.FileHeader) {
			defer wg.Done()

			// Generate unique filename with URL-safe characters
			originalName := filepath.Base(file.Filename)
			ext := filepath.Ext(originalName)
			nameWithoutExt := strings.TrimSuffix(originalName, ext)
			safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
			safeName = strings.ReplaceAll(safeName, ":", "_")
			safeName = strings.ReplaceAll(safeName, "/", "_")
			safeName = strings.ReplaceAll(safeName, "\\", "_")

			// Generate unique filename using timestamp and random string
			timestamp := time.Now().UnixNano()
			randomStr := primitive.NewObjectID().Hex()
			filename := fmt.Sprintf("%s_%d_%s%s", randomStr, timestamp, safeName, ext)
			filepath := filepath.Join(uploadDir, filename)

			// Save file
			if err := c.SaveUploadedFile(file, filepath); err != nil {
				errors <- fmt.Errorf("failed to save file %s: %v", file.Filename, err)
				return
			}

			// Add file URL to list (use forward slashes for URLs)
			mu.Lock()
			imageURLs = append(imageURLs, fmt.Sprintf("/uploads/%s", filename))
			mu.Unlock()
		}(file)
	}

	// Wait for all image processing to complete
	wg.Wait()
	close(errors)

	// Check for any errors during image processing
	for err := range errors {
		h.logger.Error("Image processing error",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Add image URLs to property
	property.Images = imageURLs

	h.logger.Info("Processing property creation request",
		zap.String("owner_id", property.OwnerID.Hex()),
		zap.String("title", property.Title),
		zap.Int("facility_count", len(property.Facilities)),
		zap.Int("image_count", len(property.Images)),
		zap.String("ip", c.ClientIP()),
	)

	// Create property in database
	if err := h.service.CreateProperty(c.Request.Context(), property); err != nil {
		h.logger.Error("Property creation failed",
			zap.String("owner_id", property.OwnerID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Property created successfully",
		zap.String("property_id", property.ID.Hex()),
		zap.String("owner_id", property.OwnerID.Hex()),
		zap.String("title", property.Title),
		zap.String("ip", c.ClientIP()),
	)

	// Notify admin about new property creation
	firstName, _ := c.Get("first_name")
	lastName, _ := c.Get("last_name")
	userName := fmt.Sprintf("%s %s", firstName, lastName)

	if err := h.notificationService.NotifyPropertyCreated(c.Request.Context(), property.OwnerID, property.ID, property.Title, userName); err != nil {
		h.logger.Error("Failed to send property creation notification",
			zap.String("property_id", property.ID.Hex()),
			zap.String("owner_id", property.OwnerID.Hex()),
			zap.String("error", err.Error()),
		)
		// Don't fail the request if notification fails
	}

	c.JSON(http.StatusCreated, property)
}

// GetProperty handles retrieving a single property with latest review
// @Summary Get a property by ID with latest review (Public - No Auth Required)
// @Description Get detailed information about a property by its ID including the latest review. Authentication is optional - authenticated users get favorite status and review like status.
// @Tags properties
// @Accept json
// @Produce json
// @Param id path string true "Property ID"
// @Success 200 {object} models.PropertyDetailResponse
// @Failure 400 {object} map[string]string "Invalid ID format"
// @Failure 404 {object} map[string]string "Property not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/{id} [get]
func (h *Handler) GetProperty(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property id"})
		return
	}

	// Get current user ID and role for review filtering and like status
	var currentUserID *primitive.ObjectID
	isAdmin := false

	// Get user ID if available (user might not be logged in)
	if userIDValue, exists := c.Get("user_id"); exists {
		if userID, ok := userIDValue.(primitive.ObjectID); ok {
			currentUserID = &userID
		}
	}

	// Check if user is admin
	if userRole, exists := c.Get("user_role"); exists && userRole.(string) == "admin" {
		isAdmin = true
	}

	h.logger.Info("Processing property retrieval request",
		zap.String("property_id", id.Hex()),
		zap.Bool("is_admin", isAdmin),
		zap.Bool("user_logged_in", currentUserID != nil),
		zap.String("ip", c.ClientIP()),
	)

	propertyDetail, err := h.service.GetPropertyWithLatestReview(c.Request.Context(), id, currentUserID, isAdmin)
	if err != nil {
		h.logger.Error("Property retrieval failed",
			zap.String("property_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if propertyDetail == nil {
		h.logger.Warn("Property not found",
			zap.String("property_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "property not found"})
		return
	}

	// Log whether a review was found
	hasReview := propertyDetail.Review != nil
	h.logger.Info("Property retrieved successfully",
		zap.String("property_id", id.Hex()),
		zap.Bool("has_review", hasReview),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, propertyDetail)
}

// ListProperties handles property listing with pagination and filters
// @Summary List properties
// @Description Get a list of properties with pagination and filtering options
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param city query string false "Filter by city"
// @Param area query string false "Filter by area (e.g., Kamrej, Varacha)"
// @Param type query string false "Filter by property type"
// @Param status query string false "Filter by status"
// @Param min_price query number false "Minimum price"
// @Param max_price query number false "Maximum price"
// @Param bhk query int false "Filter by BHK"
// @Param bedroom query int false "Filter by bedroom count"
// @Param bathroom query int false "Filter by bathroom count"
// @Param furniture query string false "Filter by furniture type"
// @Param facilities query string false "Filter by facilities (comma-separated)"
// @Param sort_by query string false "Sort field" default("created_at")
// @Param sort_order query string false "Sort order" default("desc")
// @Success 200 {object} models.PaginatedResponse "Properties list with pagination metadata"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties [get]
func (h *Handler) ListProperties(c *gin.Context) {
	// Create search request from query parameters
	var req models.PropertySearchRequest

	// Parse pagination parameters
	req.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	req.Limit, _ = strconv.Atoi(c.DefaultQuery("limit", "10"))
	req.SortBy = c.DefaultQuery("sort_by", "created_at")
	req.SortOrder = c.DefaultQuery("sort_order", "desc")

	// Parse filter parameters
	req.City = c.Query("city")
	req.Area = c.Query("area") // Now handling area as a string
	req.Type = c.Query("type")
	req.Status = c.Query("status")
	req.Furniture = c.Query("furniture")
	req.Facilities = c.Query("facilities")

	// Parse numeric parameters
	if minPrice := c.Query("min_price"); minPrice != "" {
		req.MinPrice, _ = strconv.ParseFloat(minPrice, 64)
	}
	if maxPrice := c.Query("max_price"); maxPrice != "" {
		req.MaxPrice, _ = strconv.ParseFloat(maxPrice, 64)
	}
	if bhk := c.Query("bhk"); bhk != "" {
		req.BHK, _ = strconv.Atoi(bhk)
	}
	if bedroom := c.Query("bedroom"); bedroom != "" {
		req.Bedroom, _ = strconv.Atoi(bedroom)
	}
	if bathroom := c.Query("bathroom"); bathroom != "" {
		req.Bathroom, _ = strconv.Atoi(bathroom)
	}

	// Build filter from search request
	filter := bson.M{}

	if req.City != "" {
		filter["location.city"] = req.City
	}
	if req.Area != "" {
		filter["location.area"] = req.Area // Now using location.area for area name
	}
	if req.Type != "" {
		filter["type"] = req.Type
	}
	if req.Status != "" {
		filter["status"] = req.Status
	}
	if req.MinPrice > 0 || req.MaxPrice > 0 {
		priceFilter := bson.M{}
		if req.MinPrice > 0 {
			priceFilter["$gte"] = req.MinPrice
		}
		if req.MaxPrice > 0 {
			priceFilter["$lte"] = req.MaxPrice
		}
		filter["total_price"] = priceFilter
	}
	if req.BHK > 0 {
		filter["bhk"] = req.BHK
	}
	if req.Bedroom > 0 {
		filter["bedroom"] = req.Bedroom
	}
	if req.Bathroom > 0 {
		filter["bathroom"] = req.Bathroom
	}
	if req.Furniture != "" {
		filter["furniture"] = req.Furniture
	}
	if req.Facilities != "" {
		facilities := strings.Split(req.Facilities, ",")
		// Search by facility names in the new structure
		facilityFilters := make([]bson.M, len(facilities))
		for i, facility := range facilities {
			facilityFilters[i] = bson.M{"facilities.name": strings.TrimSpace(facility)}
		}
		filter["$and"] = facilityFilters
	}

	h.logger.Info("Processing property list request",
		zap.Int("page", req.Page),
		zap.Int("limit", req.Limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	properties, total, err := h.service.ListProperties(c.Request.Context(), filter, int64(req.Page), int64(req.Limit))
	if err != nil {
		h.logger.Error("Property listing failed",
			zap.Int("page", req.Page),
			zap.Int("limit", req.Limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(req.Limit)))
	hasNext := req.Page < totalPages
	hasPrevious := req.Page > 1

	h.logger.Info("Property list retrieved successfully",
		zap.Int64("total", total),
		zap.Int("page", req.Page),
		zap.Int("limit", req.Limit),
		zap.Int("count", len(properties)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: properties,
		Pagination: models.Pagination{
			Total:       total,
			Page:        req.Page,
			Limit:       req.Limit,
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// UpdateProperty handles property updates
// @Summary Update a property
// @Description Update an existing property by ID
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Property ID"
// @Param property body models.UpdatePropertyRequest true "Property fields to update"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid input or ID"
// @Failure 404 {object} map[string]string "Property not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/{id} [put]
func (h *Handler) UpdateProperty(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property ID format for update",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property id"})
		return
	}

	var req models.UpdatePropertyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid property update request",
			zap.String("property_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Convert request to bson.M for update
	update := bson.M{}

	// Only add fields that are provided in the request
	if req.Title != "" {
		update["title"] = req.Title
	}
	if req.Description != "" {
		update["description"] = req.Description
	}
	if req.Type != "" {
		update["type"] = req.Type
	}
	if req.Status != "" {
		update["status"] = req.Status
	}
	if req.Location != nil {
		location := models.Location{
			Address:      req.Location.Address,
			City:         req.Location.City,
			Area:         req.Location.Area,
			State:        req.Location.State,
			Country:      req.Location.Country,
			ZipCode:      req.Location.ZipCode,
			Latitude:     req.Location.Latitude,
			Longitude:    req.Location.Longitude,
			Neighborhood: req.Location.Neighborhood,
		}
		update["location"] = location
	}
	if req.Images != nil {
		update["images"] = req.Images
	}
	if req.Area > 0 {
		update["area"] = req.Area
	}
	if req.YearBuilt > 0 {
		update["year_built"] = req.YearBuilt
	}
	if req.BHK > 0 {
		update["bhk"] = req.BHK
	}
	if req.Bedroom > 0 {
		update["bedroom"] = req.Bedroom
	}
	if req.Bathroom > 0 {
		update["bathroom"] = req.Bathroom
	}
	if req.NoOfParking > 0 {
		update["no_of_parking"] = req.NoOfParking
	}
	if req.OwnerName != "" {
		update["owner_name"] = req.OwnerName
	}
	if req.Facilities != nil {
		update["facilities"] = req.Facilities
	}
	if req.Facing != "" {
		update["facing_direction"] = req.Facing
	}
	if req.Furniture != "" {
		update["furniture"] = req.Furniture
	}
	if req.TotalPrice > 0 {
		update["total_price"] = req.TotalPrice
	}
	if req.TotalFloor > 0 {
		update["total_floor"] = req.TotalFloor
	}
	if req.Rating > 0 {
		update["rating"] = req.Rating
	}

	// Handle boolean fields explicitly
	update["is_property_verified"] = req.IsPropertyVerified
	update["is_property_active"] = req.IsPropertyActive

	// Add updated timestamp
	update["updated_at"] = time.Now()

	h.logger.Info("Processing property update request",
		zap.String("property_id", id.Hex()),
		zap.Any("update", update),
		zap.String("ip", c.ClientIP()),
	)

	// Get the current property to check verification status change
	currentProperty, err := h.service.GetProperty(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get current property for notification check",
			zap.String("property_id", id.Hex()),
			zap.String("error", err.Error()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get property"})
		return
	}

	if err := h.service.UpdateProperty(c.Request.Context(), id, update); err != nil {
		h.logger.Error("Property update failed",
			zap.String("property_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Check if verification status changed and send notification
	if currentProperty != nil && currentProperty.IsPropertyVerified != req.IsPropertyVerified {
		if req.IsPropertyVerified {
			// Property was verified
			if err := h.notificationService.NotifyPropertyVerified(c.Request.Context(), currentProperty.OwnerID, id, currentProperty.Title); err != nil {
				h.logger.Error("Failed to send property verification notification",
					zap.String("property_id", id.Hex()),
					zap.String("owner_id", currentProperty.OwnerID.Hex()),
					zap.String("error", err.Error()),
				)
			}
		} else {
			// Property was rejected
			if err := h.notificationService.NotifyPropertyRejected(c.Request.Context(), currentProperty.OwnerID, id, currentProperty.Title); err != nil {
				h.logger.Error("Failed to send property rejection notification",
					zap.String("property_id", id.Hex()),
					zap.String("owner_id", currentProperty.OwnerID.Hex()),
					zap.String("error", err.Error()),
				)
			}
		}
	}

	h.logger.Info("Property updated successfully",
		zap.String("property_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "property updated successfully"})
}

// DeleteProperty handles property deletion
// @Summary Delete a property
// @Description Delete a property by ID
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Property ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID format"
// @Failure 404 {object} map[string]string "Property not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/{id} [delete]
func (h *Handler) DeleteProperty(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid property ID format for deletion",
			zap.String("id", c.Param("id")),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property id"})
		return
	}

	h.logger.Info("Processing property deletion request",
		zap.String("property_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	if err := h.service.DeleteProperty(c.Request.Context(), id); err != nil {
		h.logger.Error("Property deletion failed",
			zap.String("property_id", id.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Property deleted successfully",
		zap.String("property_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "property deleted successfully"})
}

// ListPremiumProperties handles the request to list premium properties (added by subscribed users)
// @Summary List premium properties
// @Description Get a list of premium properties (added by subscribed users)
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} models.PaginatedResponse "List of premium properties"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/premium [get]
func (h *Handler) ListPremiumProperties(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	_, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.ParseInt(pageStr, 10, 64)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil || limit < 1 {
		limit = 10
	}

	h.logger.Info("Processing premium properties list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.String("ip", c.ClientIP()),
	)

	// Get premium properties from service
	properties, total, err := h.service.ListPremiumProperties(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.Error("Failed to retrieve premium properties",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Premium properties list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(properties)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: properties,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// ComprehensiveSearch handles comprehensive property search with trending areas, properties, top agents, and premium listings
// @Summary Comprehensive property search
// @Description Get comprehensive property search results including trending areas, properties, top agents, and premium listings
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ComprehensiveSearchRequest true "Search parameters"
// @Success 200 {object} models.ComprehensiveSearchResponse "Comprehensive search results"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/comprehensive-search [post]
func (h *Handler) ComprehensiveSearch(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	_, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	var req models.ComprehensiveSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid comprehensive search request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing comprehensive search request",
		zap.String("property_status", req.PropertyStatus),
		zap.String("property_type", req.PropertyType),
		zap.String("city", req.City),
		zap.String("area", req.Area),
		zap.String("trending_area", req.TrendingArea),
		zap.String("ip", c.ClientIP()),
	)

	// Use optimized concurrent search for better performance
	response, err := h.service.ComprehensiveSearchOptimized(
		c.Request.Context(),
		req.City,
		req.Area,
		req.PropertyType,
		req.PropertyStatus,
		req.TrendingArea,
	)
	if err != nil {
		h.logger.Error("Comprehensive search failed",
			zap.String("city", req.City),
			zap.String("area", req.Area),
			zap.String("type", req.PropertyType),
			zap.String("status", req.PropertyStatus),
			zap.String("trending_area", req.TrendingArea),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "comprehensive search failed"})
		return
	}

	// Determine search area for logging
	searchArea := req.Area
	if req.TrendingArea != "" {
		searchArea = req.TrendingArea
	}

	h.logger.Info("Comprehensive search completed successfully",
		zap.String("city", req.City),
		zap.String("area", searchArea),
		zap.String("type", req.PropertyType),
		zap.String("status", req.PropertyStatus),
		zap.Int("trending_areas_count", len(response.TrendingAreas)),
		zap.Int("properties_count", len(response.Properties)),
		zap.Int("top_agents_count", len(response.TopAgents)),
		zap.Int("premium_listings_count", len(response.PremiumListings)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, response)
}

// GetPropertiesByUserID handles retrieving properties owned by the authenticated user
// @Summary Get properties by user ID with filtering
// @Description Get all properties owned by the authenticated user with pagination and filtering options
// @Tags properties
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page (max 100)" default(10)
// @Param type query string false "Filter by property type (apartment, house, villa, commercial, land)"
// @Param status query string false "Filter by property status (available, sold, rented)"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param bhk query int false "Filter by BHK count"
// @Param bedroom query int false "Filter by bedroom count"
// @Param bathroom query int false "Filter by bathroom count"
// @Param furniture query string false "Filter by furniture type (furnished, semi-furnished, unfurnished)"
// @Param city query string false "Filter by city"
// @Param area query string false "Filter by area"
// @Param sort_by query string false "Sort field (created_at, updated_at, total_price, area)" default("created_at")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} models.PaginatedResponse "List of user's properties with favorite status"
// @Failure 400 {object} map[string]string "Invalid parameters"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /properties/user [get]
func (h *Handler) GetPropertiesByUserID(c *gin.Context) {
	// Get user ID from authentication context
	userIDValue, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	userID, ok := userIDValue.(primitive.ObjectID)
	if !ok {
		h.logger.Warn("Invalid user ID format in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "invalid user ID format"})
		return
	}

	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.ParseInt(pageStr, 10, 64)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	// Parse filtering parameters
	filters := bson.M{"owner_id": userID}

	if propertyType := c.Query("type"); propertyType != "" {
		filters["type"] = propertyType
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if city := c.Query("city"); city != "" {
		filters["location.city"] = city
	}
	if area := c.Query("area"); area != "" {
		filters["location.area"] = area
	}
	if furniture := c.Query("furniture"); furniture != "" {
		filters["furniture"] = furniture
	}

	// Parse numeric filters
	if minPrice := c.Query("min_price"); minPrice != "" {
		if price, err := strconv.ParseFloat(minPrice, 64); err == nil && price > 0 {
			if filters["total_price"] == nil {
				filters["total_price"] = bson.M{}
			}
			filters["total_price"].(bson.M)["$gte"] = price
		}
	}
	if maxPrice := c.Query("max_price"); maxPrice != "" {
		if price, err := strconv.ParseFloat(maxPrice, 64); err == nil && price > 0 {
			if filters["total_price"] == nil {
				filters["total_price"] = bson.M{}
			}
			filters["total_price"].(bson.M)["$lte"] = price
		}
	}
	if bhk := c.Query("bhk"); bhk != "" {
		if bhkCount, err := strconv.Atoi(bhk); err == nil && bhkCount > 0 {
			filters["bhk"] = bhkCount
		}
	}
	if bedroom := c.Query("bedroom"); bedroom != "" {
		if bedroomCount, err := strconv.Atoi(bedroom); err == nil && bedroomCount >= 0 {
			filters["bedroom"] = bedroomCount
		}
	}
	if bathroom := c.Query("bathroom"); bathroom != "" {
		if bathroomCount, err := strconv.Atoi(bathroom); err == nil && bathroomCount >= 0 {
			filters["bathroom"] = bathroomCount
		}
	}

	// Parse sorting parameters
	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortOrder := c.DefaultQuery("sort_order", "desc")

	// Validate sort fields
	validSortFields := map[string]bool{
		"created_at":  true,
		"updated_at":  true,
		"total_price": true,
		"area":        true,
	}
	if !validSortFields[sortBy] {
		sortBy = "created_at"
	}

	sortDirection := -1 // desc
	if sortOrder == "asc" {
		sortDirection = 1
	}

	h.logger.Info("Processing properties by user ID request",
		zap.String("user_id", userID.Hex()),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filters),
		zap.String("sort_by", sortBy),
		zap.String("sort_order", sortOrder),
		zap.String("ip", c.ClientIP()),
	)

	// Get properties from service with filters
	properties, total, err := h.service.GetPropertiesByUserIDWithFilters(c.Request.Context(), userID, page, limit, filters, sortBy, sortDirection)
	if err != nil {
		h.logger.Error("Failed to retrieve properties by user ID",
			zap.String("user_id", userID.Hex()),
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filters),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Convert properties to PropertyWithFavorite and check favorite status efficiently
	propertiesWithFavorites := make([]*models.PropertyWithFavorite, len(properties))

	// Early return if no properties
	if len(properties) == 0 {
		// Return empty response immediately
		c.JSON(http.StatusOK, models.PaginatedResponse{
			Data: propertiesWithFavorites,
			Pagination: models.Pagination{
				Total:       0,
				Page:        int(page),
				Limit:       int(limit),
				TotalPages:  1,
				HasNext:     false,
				HasPrevious: false,
			},
		})
		return
	}

	// Extract all property IDs for batch favorite checking
	propertyIDs := make([]primitive.ObjectID, len(properties))
	for i, property := range properties {
		propertyIDs[i] = property.ID
	}

	// Batch check favorites for all properties in a single database call
	favoriteMap, err := h.favoriteService.CheckMultipleFavorites(c.Request.Context(), userID, propertyIDs)
	if err != nil {
		h.logger.Warn("Failed to batch check favorite status, falling back to individual checks",
			zap.String("user_id", userID.Hex()),
			zap.Int("property_count", len(propertyIDs)),
			zap.String("error", err.Error()),
		)

		// Fallback: Initialize all as non-favorites if batch check fails
		favoriteMap = make(map[primitive.ObjectID]bool)
		for _, propertyID := range propertyIDs {
			favoriteMap[propertyID] = false
		}
	}

	// Use goroutines for concurrent processing of properties (for CPU-intensive operations)
	var wg sync.WaitGroup
	var mu sync.Mutex

	// Process properties in batches to avoid overwhelming the system
	batchSize := 50 // Process 50 properties at a time
	for i := 0; i < len(properties); i += batchSize {
		end := i + batchSize
		if end > len(properties) {
			end = len(properties)
		}

		wg.Add(1)
		go func(start, end int) {
			defer wg.Done()

			// Process batch of properties
			for j := start; j < end; j++ {
				property := properties[j]

				// Get favorite status from the pre-fetched map
				isFavorite, exists := favoriteMap[property.ID]
				if !exists {
					isFavorite = false // Default to false if not found
				}

				propertyWithFavorite := &models.PropertyWithFavorite{
					Property:   *property,
					IsFavorite: isFavorite,
				}

				// Thread-safe assignment
				mu.Lock()
				propertiesWithFavorites[j] = propertyWithFavorite
				mu.Unlock()
			}
		}(i, end)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Properties by user ID retrieved successfully",
		zap.String("user_id", userID.Hex()),
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(propertiesWithFavorites)),
		zap.Any("filters", filters),
		zap.String("ip", c.ClientIP()),
	)

	// Convert int64 values to int for Pagination struct
	pageInt := int(page)
	limitInt := int(limit)

	// Create paginated response
	c.JSON(http.StatusOK, models.PaginatedResponse{
		Data: propertiesWithFavorites,
		Pagination: models.Pagination{
			Total:       total,
			Page:        pageInt,
			Limit:       limitInt,
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	})
}
