package property_metadata

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/property_metadata"
)

type Handler struct {
	service *property_metadata.Service
	logger  *zap.Logger
}

func NewHandler(service *property_metadata.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers the property metadata routes for authenticated users
func (h *<PERSON><PERSON>) RegisterRoutes(router *gin.RouterGroup) {
	metadata := router.Group("/metadata")
	{
		metadata.GET("/property-types", h.ListPropertyTypes)
		metadata.GET("/facilities", h.ListFacilities)
	}
}

// ListPropertyTypes handles listing property types for authenticated users
// @Summary List property types for authenticated users
// @Description Get a list of property types with pagination and filtering options (requires authentication)
// @Tags metadata
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param name query string false "Filter by name"
// @Param active query string false "Filter by active status"
// @Success 200 {object} models.PaginatedResponse
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/metadata/property-types [get]
func (h *Handler) ListPropertyTypes(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	// Build filter - for authenticated users, allow filtering by active status
	filter := bson.M{}

	// Allow name filtering
	if name := c.Query("name"); name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}

	// Allow active filtering
	if active := c.Query("active"); active != "" {
		activeBool, err := strconv.ParseBool(active)
		if err == nil {
			filter["active"] = activeBool
		}
	} else {
		// Default to active=true if not specified
		filter["active"] = true
	}

	h.logger.Info("Processing property types list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	propertyTypes, total, err := h.service.ListPropertyTypes(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Property types listing failed",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(float64(total)/float64(limit) + 0.5)
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Property types list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(propertyTypes)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: propertyTypes,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// ListFacilities handles listing facilities for authenticated users
// @Summary List facilities for authenticated users
// @Description Get a simplified list of facilities (id, name, icon_url) with pagination and filtering options (requires authentication)
// @Tags metadata
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Param name query string false "Filter by name"
// @Param active query string false "Filter by active status"
// @Success 200 {object} models.PaginatedResponse{data=[]models.FacilitiesResponse}
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/metadata/facilities [get]
func (h *Handler) ListFacilities(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	// Build filter - for authenticated users, allow filtering by active status
	filter := bson.M{}

	// Allow name filtering
	if name := c.Query("name"); name != "" {
		filter["name"] = bson.M{"$regex": name, "$options": "i"}
	}

	// Allow active filtering
	if active := c.Query("active"); active != "" {
		activeBool, err := strconv.ParseBool(active)
		if err == nil {
			filter["active"] = activeBool
		}
	} else {
		// Default to active=true if not specified
		filter["active"] = true
	}

	h.logger.Info("Processing facilities list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Any("filters", filter),
		zap.String("ip", c.ClientIP()),
	)

	facilities, total, err := h.service.ListFacilities(c.Request.Context(), filter, page, limit)
	if err != nil {
		h.logger.Error("Facilities listing failed",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.Any("filters", filter),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Transform facilities to simplified response format
	facilitiesResponse := make([]models.FacilitiesResponse, len(facilities))
	for i, facility := range facilities {
		facilitiesResponse[i] = models.FacilitiesResponse{
			ID:      facility.ID,
			Name:    facility.Name,
			IconURL: facility.IconURL,
		}
	}

	// Calculate pagination info
	totalPages := int(float64(total)/float64(limit) + 0.5)
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Facilities list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(facilities)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response with simplified facility data
	response := models.PaginatedResponse{
		Data: facilitiesResponse,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}
