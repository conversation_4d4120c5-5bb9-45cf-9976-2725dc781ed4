package questionnaire

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/questionnaire"
)

type Handler struct {
	service               *questionnaire.Service
	recommendationService *questionnaire.RecommendationService
	logger                *zap.Logger
}

func NewHandler(service *questionnaire.Service, recommendationService *questionnaire.RecommendationService, logger *zap.Logger) *Handler {
	return &Handler{
		service:               service,
		recommendationService: recommendationService,
		logger:                logger,
	}
}

// RegisterRoutes registers all questionnaire routes
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	questionnaire := router.Group("/questionnaire")
	{
		// Session management
		questionnaire.POST("/start", h.StartQuestionnaire)
		questionnaire.GET("/session/:sessionId", h.GetSessionStatus)
		questionnaire.GET("/session/:sessionId/current-question", h.GetCurrentQuestion)
		questionnaire.POST("/session/:sessionId/answer", h.SubmitAnswer)
		questionnaire.POST("/session/:sessionId/complete", h.CompleteQuestionnaire)
		questionnaire.DELETE("/session/:sessionId", h.TerminateSession)

		// Recommendations
		questionnaire.GET("/session/:sessionId/recommendations", h.GetRecommendations)
		questionnaire.GET("/recommendations/:recommendationId", h.GetRecommendationByID)
	}
}

// StartQuestionnaire creates a new questionnaire session
// @Summary Start a new questionnaire session
// @Description Creates a new questionnaire session for the authenticated user
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.StartQuestionnaireResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 401 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/start [post]
func (h *Handler) StartQuestionnaire(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "UNAUTHORIZED",
				"message": "User not authenticated",
			},
		})
		return
	}

	userID, ok := userIDInterface.(primitive.ObjectID)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_USER_ID",
				"message": "Invalid user ID format",
			},
		})
		return
	}

	// Start questionnaire session
	response, err := h.service.StartSession(c.Request.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to start questionnaire session", zap.Error(err), zap.String("user_id", userID.Hex()))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "SESSION_START_FAILED",
				"message": "Failed to start questionnaire session",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "Questionnaire session started successfully",
	})
}

// GetCurrentQuestion retrieves the current question for a session
// @Summary Get current question
// @Description Retrieves the current question for the specified session
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Success 200 {object} models.CurrentQuestionResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId}/current-question [get]
func (h *Handler) GetCurrentQuestion(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	response, err := h.service.GetCurrentQuestion(c.Request.Context(), sessionID)
	if err != nil {
		h.logger.Error("Failed to get current question", zap.Error(err), zap.String("session_id", sessionIDStr))

		// Check for specific error types
		if err.Error() == "session has expired" {
			c.JSON(http.StatusGone, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "SESSION_EXPIRED",
					"message": "Your questionnaire session has expired",
					"details": "Sessions expire after 30 minutes of inactivity. Please start a new session.",
				},
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "QUESTION_FETCH_FAILED",
				"message": "Failed to get current question",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// SubmitAnswer submits an answer for the current question
// @Summary Submit answer
// @Description Submits an answer for the current question in the session
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Param request body models.SubmitAnswerRequest true "Answer submission request"
// @Success 200 {object} models.SubmitAnswerResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 403 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId}/answer [post]
func (h *Handler) SubmitAnswer(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	var req models.SubmitAnswerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_REQUEST",
				"message": "Invalid request format",
				"details": err.Error(),
			},
		})
		return
	}

	response, err := h.service.SubmitAnswer(c.Request.Context(), sessionID, req.QuestionID, req.Answer)
	if err != nil {
		h.logger.Error("Failed to submit answer", zap.Error(err), zap.String("session_id", sessionIDStr))

		// Check for inappropriate answer termination
		if err.Error() == "inappropriate answer: inappropriate_content" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "INAPPROPRIATE_ANSWER",
					"message": "Your response contains inappropriate content. The questionnaire session has been terminated.",
					"details": "Please provide relevant and appropriate answers related to property preferences.",
				},
				"data": gin.H{
					"session_status":     "terminated",
					"termination_reason": "inappropriate_content",
				},
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "ANSWER_SUBMISSION_FAILED",
				"message": "Failed to submit answer",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "Answer submitted successfully",
	})
}

// CompleteQuestionnaire marks the questionnaire as completed
// @Summary Complete questionnaire
// @Description Marks the questionnaire as completed and generates recommendations
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Success 200 {object} models.CompleteQuestionnaireResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId}/complete [post]
func (h *Handler) CompleteQuestionnaire(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	response, err := h.service.CompleteQuestionnaire(c.Request.Context(), sessionID)
	if err != nil {
		h.logger.Error("Failed to complete questionnaire", zap.Error(err), zap.String("session_id", sessionIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "COMPLETION_FAILED",
				"message": "Failed to complete questionnaire",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "Questionnaire completed successfully. Generating recommendations...",
	})
}

// GetSessionStatus retrieves the current status of a session
// @Summary Get session status
// @Description Retrieves the current status and progress of a questionnaire session
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Success 200 {object} models.SessionStatusResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId} [get]
func (h *Handler) GetSessionStatus(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	response, err := h.service.GetSessionStatus(c.Request.Context(), sessionID)
	if err != nil {
		h.logger.Error("Failed to get session status", zap.Error(err), zap.String("session_id", sessionIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "STATUS_FETCH_FAILED",
				"message": "Failed to get session status",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetRecommendations retrieves property recommendations for a completed session
// @Summary Get property recommendations
// @Description Retrieves property recommendations for a completed questionnaire session
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Success 200 {object} models.RecommendationsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId}/recommendations [get]
func (h *Handler) GetRecommendations(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	response, err := h.recommendationService.GetRecommendations(c.Request.Context(), sessionID)
	if err != nil {
		h.logger.Error("Failed to get recommendations", zap.Error(err), zap.String("session_id", sessionIDStr))

		if err.Error() == "session is not completed: active" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "SESSION_NOT_COMPLETED",
					"message": "Questionnaire session is not completed yet",
					"details": "Please complete all questions before requesting recommendations.",
				},
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "RECOMMENDATIONS_FETCH_FAILED",
				"message": "Failed to get recommendations",
				"details": err.Error(),
			},
		})
		return
	}

	message := "Found properties matching your preferences"
	if response.TotalMatches == 0 {
		message = "No properties found matching your exact preferences. Try adjusting your criteria."
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": message,
	})
}

// GetRecommendationByID retrieves a specific recommendation by ID
// @Summary Get recommendation by ID
// @Description Retrieves a specific property recommendation by recommendation ID
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param recommendationId path string true "Recommendation ID"
// @Success 200 {object} models.RecommendationsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/recommendations/{recommendationId} [get]
func (h *Handler) GetRecommendationByID(c *gin.Context) {
	recommendationIDStr := c.Param("recommendationId")
	recommendationID, err := primitive.ObjectIDFromHex(recommendationIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_RECOMMENDATION_ID",
				"message": "Invalid recommendation ID format",
			},
		})
		return
	}

	response, err := h.recommendationService.GetRecommendationByID(c.Request.Context(), recommendationID)
	if err != nil {
		h.logger.Error("Failed to get recommendation by ID", zap.Error(err), zap.String("recommendation_id", recommendationIDStr))
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "RECOMMENDATION_FETCH_FAILED",
				"message": "Failed to get recommendation",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// TerminateSession terminates an active questionnaire session
// @Summary Terminate session
// @Description Terminates an active questionnaire session
// @Tags Questionnaire
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param sessionId path string true "Session ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /questionnaire/session/{sessionId} [delete]
func (h *Handler) TerminateSession(c *gin.Context) {
	sessionIDStr := c.Param("sessionId")
	sessionID, err := primitive.ObjectIDFromHex(sessionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "INVALID_SESSION_ID",
				"message": "Invalid session ID format",
			},
		})
		return
	}

	// Terminate the session using the service
	err = h.service.TerminateSession(c.Request.Context(), sessionID)
	if err != nil {
		h.logger.Error("Failed to terminate session", zap.Error(err), zap.String("session_id", sessionIDStr))

		// Check for specific error types
		if err.Error() == "session is already terminated" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "SESSION_ALREADY_TERMINATED",
					"message": "Session is already terminated",
				},
			})
			return
		}

		if err.Error() == "cannot terminate a completed session" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error": gin.H{
					"code":    "SESSION_ALREADY_COMPLETED",
					"message": "Cannot terminate a completed session",
				},
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error": gin.H{
				"code":    "TERMINATION_FAILED",
				"message": "Failed to terminate session",
				"details": err.Error(),
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Session terminated successfully",
		"data": gin.H{
			"session_id": sessionID,
			"status":     "terminated",
		},
	})
}
