package review

import (
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/review"
	review_service "realestate-platform/internal/services/review"
)

type Handler struct {
	service *review.Service
	logger  *zap.Logger
}

func NewHandler(service *review.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers all review routes
func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
	reviews := router.Group("/reviews")
	{
		// Review CRUD operations
		reviews.POST("", h.CreateReview)
		reviews.GET("/property/:propertyId", h.ListReviewsByProperty)
		reviews.GET("/my-reviews/property/:propertyId", h.<PERSON>ReviewsByUserForProperty) // New route for user's reviews on specific property
		reviews.PUT("/:reviewId", h.UpdateReview)
		reviews.DELETE("/:reviewId", h.DeleteReview)

		// Like/Dislike operations
		reviews.POST("/:reviewId/like", h.LikeReview)
		reviews.POST("/:reviewId/dislike", h.DislikeReview)
		reviews.DELETE("/:reviewId/like", h.RemoveLike)
		reviews.DELETE("/:reviewId/dislike", h.RemoveDislike)

		// Admin operations
		reviews.PUT("/:reviewId/toggle-active", h.ToggleReviewActive)
	}
}

// CreateReview handles creating a new review with image uploads
// @Summary Create a new review with image uploads
// @Description Create a new review for a property with support for image uploads
// @Tags reviews
// @Accept multipart/form-data
// @Produce json
// @Param review formData string true "Review details (JSON string)"
// @Param images formData file false "Review images (multiple files allowed)"
// @Security ApiKeyAuth
// @Success 201 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Property not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews [post]
func (h *Handler) CreateReview(c *gin.Context) {
	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get review details from form
	reviewJSON := c.PostForm("review")
	if reviewJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "review details are required"})
		return
	}

	// Parse review details
	var req models.CreateReviewRequest
	if err := json.Unmarshal([]byte(reviewJSON), &req); err != nil {
		h.logger.Warn("Invalid review creation request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review details format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	// Get user name from context
	firstName, exists := c.Get("first_name")
	if !exists {
		firstName = ""
	}
	lastName, exists := c.Get("last_name")
	if !exists {
		lastName = ""
	}
	userName := firstName.(string) + " " + lastName.(string)

	// Convert property ID from string to ObjectID
	propertyID, err := primitive.ObjectIDFromHex(req.PropertyID)
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("property_id", req.PropertyID),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property ID format"})
		return
	}

	// Create uploads directory if it doesn't exist
	uploadDir := "uploads/reviews"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		h.logger.Error("Failed to create uploads directory",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
		return
	}

	// Process uploaded images concurrently (similar to property creation)
	form, err := c.MultipartForm()
	if err != nil {
		h.logger.Warn("Failed to get multipart form",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to get form data"})
		return
	}

	files := form.File["images"]
	var imageURLs []string
	var wg sync.WaitGroup
	var mu sync.Mutex
	errors := make(chan error, len(files))

	// Process each image concurrently
	for _, file := range files {
		wg.Add(1)
		go func(file *multipart.FileHeader) {
			defer wg.Done()

			// Generate unique filename with URL-safe characters
			originalName := filepath.Base(file.Filename)
			ext := filepath.Ext(originalName)
			nameWithoutExt := strings.TrimSuffix(originalName, ext)
			safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
			safeName = strings.ReplaceAll(safeName, ":", "_")
			safeName = strings.ReplaceAll(safeName, "/", "_")
			safeName = strings.ReplaceAll(safeName, "\\", "_")

			// Generate unique filename using timestamp and random string
			timestamp := time.Now().UnixNano()
			randomStr := primitive.NewObjectID().Hex()
			filename := fmt.Sprintf("review_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
			filepath := filepath.Join(uploadDir, filename)

			// Save file
			if err := c.SaveUploadedFile(file, filepath); err != nil {
				errors <- fmt.Errorf("failed to save file %s: %v", file.Filename, err)
				return
			}

			// Add file URL to list (use forward slashes for URLs)
			mu.Lock()
			imageURLs = append(imageURLs, fmt.Sprintf("/uploads/reviews/%s", filename))
			mu.Unlock()
		}(file)
	}

	// Wait for all image processing to complete
	wg.Wait()
	close(errors)

	// Check for any errors during image processing
	for err := range errors {
		h.logger.Error("Image processing error",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Create review object
	review := &models.Review{
		UserID:     userID.(primitive.ObjectID),
		PropertyID: propertyID,
		UserName:   userName,
		Rating:     req.Rating,
		Comment:    req.Comment,
		Photos:     imageURLs, // Use uploaded image URLs instead of req.Photos
	}

	h.logger.Info("Processing review creation request",
		zap.String("user_id", review.UserID.Hex()),
		zap.String("property_id", review.PropertyID.Hex()),
		zap.Float64("rating", review.Rating),
		zap.String("ip", c.ClientIP()),
	)

	// Create the review
	err = h.service.CreateReview(c.Request.Context(), review)
	if err != nil {
		switch err {
		case review_service.ErrPropertyNotFound:
			h.logger.Warn("Property not found",
				zap.String("property_id", review.PropertyID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "property not found"})
		case review_service.ErrUserNotFound:
			h.logger.Warn("User not found",
				zap.String("user_id", review.UserID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "user not found"})
		default:
			h.logger.Error("Review creation failed",
				zap.String("user_id", review.UserID.Hex()),
				zap.String("property_id", review.PropertyID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create review"})
		}
		return
	}

	h.logger.Info("Review created successfully",
		zap.String("review_id", review.ID.Hex()),
		zap.String("user_id", review.UserID.Hex()),
		zap.String("property_id", review.PropertyID.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusCreated, models.ReviewResponse{
		Success: true,
		Message: "review created successfully",
		Review:  *review,
	})
}

// ListReviewsByProperty handles listing reviews for a property with like status
// @Summary List reviews for a property with like status
// @Description Get all reviews for a specific property with pagination and like status for logged-in user
// @Tags reviews
// @Accept json
// @Produce json
// @Param propertyId path string true "Property ID"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewListWithLikeStatusResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/property/{propertyId} [get]
func (h *Handler) ListReviewsByProperty(c *gin.Context) {
	propertyIDStr := c.Param("propertyId")

	// Convert property ID from string to ObjectID
	propertyID, err := primitive.ObjectIDFromHex(propertyIDStr)
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("property_id", propertyIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property ID format"})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	h.logger.Info("Processing review list request",
		zap.String("property_id", propertyID.Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.String("ip", c.ClientIP()),
	)

	// Check if user is admin
	isAdmin := false
	userRole, exists := c.Get("user_role")
	if exists && userRole.(string) == "admin" {
		isAdmin = true
	}

	// Get user ID for like status (optional - user might not be logged in)
	var userID *primitive.ObjectID
	if userIDValue, exists := c.Get("user_id"); exists {
		if uid, ok := userIDValue.(primitive.ObjectID); ok {
			userID = &uid
		}
	}

	// Get reviews with optimized like status checking
	reviewsWithLikeStatus, total, err := h.service.ListReviewsByPropertyID(c.Request.Context(), propertyID, int64(page), int64(limit), isAdmin, userID)
	if err != nil {
		h.logger.Error("Failed to list reviews",
			zap.String("property_id", propertyID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list reviews"})
		return
	}

	// Convert to response format
	var reviewsData []models.ReviewWithLikeStatus
	for _, r := range reviewsWithLikeStatus {
		reviewsData = append(reviewsData, *r)
	}

	// Calculate pagination info
	totalPages := (int(total) + limit - 1) / limit
	hasNext := page < totalPages
	hasPrevious := page > 1

	h.logger.Info("Reviews retrieved successfully with like status",
		zap.String("property_id", propertyID.Hex()),
		zap.Int64("total", total),
		zap.Int("count", len(reviewsData)),
		zap.Bool("user_logged_in", userID != nil),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewListWithLikeStatusResponse{
		Data: reviewsData,
		Pagination: models.Pagination{
			Total:       total,
			Page:        page,
			Limit:       limit,
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	})
}

// UpdateReview handles updating an existing review with image uploads
// @Summary Update a review with image uploads
// @Description Update an existing review with support for image uploads
// @Tags reviews
// @Accept multipart/form-data
// @Produce json
// @Param reviewId path string true "Review ID"
// @Param review formData string true "Review details (JSON string)"
// @Param images formData file false "Review images (multiple files allowed)"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 403 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Review not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId} [put]
func (h *Handler) UpdateReview(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	// Get the review to check ownership
	existingReview, err := h.service.GetReview(c.Request.Context(), reviewID)
	if err != nil {
		if err == review.ErrReviewNotFound {
			h.logger.Warn("Review not found",
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "review not found"})
		} else {
			h.logger.Error("Failed to get review",
				zap.String("review_id", reviewID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get review"})
		}
		return
	}

	// Check if the user is the owner of the review
	if existingReview.UserID != userID.(primitive.ObjectID) {
		// Check if user is admin
		userRole, exists := c.Get("user_role")
		if !exists || userRole.(string) != "admin" {
			h.logger.Warn("Unauthorized review update attempt",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("review_owner_id", existingReview.UserID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusForbidden, gin.H{"error": "you can only update your own reviews"})
			return
		}
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("review_id", reviewID.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get review details from form
	reviewJSON := c.PostForm("review")
	if reviewJSON == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "review details are required"})
		return
	}

	// Parse review details
	var req models.UpdateReviewRequest
	if err := json.Unmarshal([]byte(reviewJSON), &req); err != nil {
		h.logger.Warn("Invalid review update request",
			zap.String("error", err.Error()),
			zap.String("review_id", reviewID.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review details format"})
		return
	}

	// Process image uploads with intelligent handling (similar to Update Profile API)
	var newImageURLs []string
	form, err := c.MultipartForm()
	if err != nil {
		h.logger.Warn("Failed to get multipart form",
			zap.String("error", err.Error()),
			zap.String("review_id", reviewID.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to get form data"})
		return
	}

	files := form.File["images"]

	// Check if user wants to remove all images (no files uploaded and explicit removal)
	removeAllImages := len(files) == 0

	if len(files) > 0 {
		// Check if uploaded images are the same as existing ones
		existingImageNames := make(map[string]bool)
		for _, existingURL := range existingReview.Photos {
			// Extract filename from URL
			parts := strings.Split(existingURL, "/")
			if len(parts) > 0 {
				existingImageNames[parts[len(parts)-1]] = true
			}
		}

		// Create uploads directory if needed
		uploadDir := "uploads/reviews"
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			h.logger.Error("Failed to create uploads directory",
				zap.String("error", err.Error()),
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
			return
		}

		var wg sync.WaitGroup
		var mu sync.Mutex
		errors := make(chan error, len(files))

		// Process each image
		for _, file := range files {
			wg.Add(1)
			go func(file *multipart.FileHeader) {
				defer wg.Done()

				// Generate safe filename for comparison
				originalName := filepath.Base(file.Filename)
				ext := filepath.Ext(originalName)
				nameWithoutExt := strings.TrimSuffix(originalName, ext)
				safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
				safeName = strings.ReplaceAll(safeName, ":", "_")
				safeName = strings.ReplaceAll(safeName, "/", "_")
				safeName = strings.ReplaceAll(safeName, "\\", "_")

				// Check if this image already exists
				isDuplicate := false
				for existingName := range existingImageNames {
					if strings.Contains(existingName, safeName) && strings.HasSuffix(existingName, ext) {
						isDuplicate = true
						// Add existing URL to new list
						mu.Lock()
						for _, existingURL := range existingReview.Photos {
							if strings.Contains(existingURL, existingName) {
								newImageURLs = append(newImageURLs, existingURL)
								break
							}
						}
						mu.Unlock()
						break
					}
				}

				if !isDuplicate {
					// Generate unique filename using timestamp and random string
					timestamp := time.Now().UnixNano()
					randomStr := primitive.NewObjectID().Hex()
					filename := fmt.Sprintf("review_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
					filepath := filepath.Join(uploadDir, filename)

					// Save file
					if err := c.SaveUploadedFile(file, filepath); err != nil {
						errors <- fmt.Errorf("failed to save file %s: %v", file.Filename, err)
						return
					}

					// Add file URL to list
					mu.Lock()
					newImageURLs = append(newImageURLs, fmt.Sprintf("/uploads/reviews/%s", filename))
					mu.Unlock()
				}
			}(file)
		}

		// Wait for all image processing to complete
		wg.Wait()
		close(errors)

		// Check for any errors during image processing
		for err := range errors {
			h.logger.Error("Image processing error",
				zap.String("error", err.Error()),
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// Delete old images that are not in the new list
		for _, oldURL := range existingReview.Photos {
			isStillUsed := false
			for _, newURL := range newImageURLs {
				if oldURL == newURL {
					isStillUsed = true
					break
				}
			}
			if !isStillUsed {
				// Delete old image file
				oldImagePath := strings.TrimPrefix(oldURL, "/")
				if err := os.Remove(oldImagePath); err != nil {
					h.logger.Warn("Failed to delete old review image",
						zap.String("error", err.Error()),
						zap.String("review_id", reviewID.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				} else {
					h.logger.Info("Old review image deleted successfully",
						zap.String("review_id", reviewID.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				}
			}
		}
	} else if removeAllImages {
		// No new images uploaded - user wants to remove all images
		// Delete all existing images from filesystem
		for _, oldURL := range existingReview.Photos {
			oldImagePath := strings.TrimPrefix(oldURL, "/")
			if err := os.Remove(oldImagePath); err != nil {
				h.logger.Warn("Failed to delete review image during removal",
					zap.String("error", err.Error()),
					zap.String("review_id", reviewID.Hex()),
					zap.String("old_image_path", oldImagePath),
				)
			} else {
				h.logger.Info("Review image deleted successfully during removal",
					zap.String("review_id", reviewID.Hex()),
					zap.String("old_image_path", oldImagePath),
				)
			}
		}
		// Clear the image URLs array
		newImageURLs = []string{}
	} else {
		// No new images uploaded, keep existing ones (this case should not happen with current logic)
		newImageURLs = existingReview.Photos
	}

	// Create update document
	update := bson.M{}
	if req.Rating != 0 {
		update["rating"] = req.Rating
	}
	if req.Comment != "" {
		update["comment"] = req.Comment
	}
	// Always update photos with the processed image URLs
	update["photos"] = newImageURLs

	h.logger.Info("Processing review update request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.Any("update", update),
		zap.String("ip", c.ClientIP()),
	)

	// Update the review
	err = h.service.UpdateReview(c.Request.Context(), reviewID, update)
	if err != nil {
		h.logger.Error("Review update failed",
			zap.String("review_id", reviewID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update review"})
		return
	}

	// Get the updated review
	updatedReview, err := h.service.GetReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.Error("Failed to get updated review",
			zap.String("review_id", reviewID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "review updated but failed to retrieve updated data"})
		return
	}

	h.logger.Info("Review updated successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "review updated successfully",
		Review:  *updatedReview,
	})
}

// DeleteReview handles deleting a review
// @Summary Delete a review
// @Description Delete an existing review
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 403 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Review not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId} [delete]
func (h *Handler) DeleteReview(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	// Get the review to check ownership
	existingReview, err := h.service.GetReview(c.Request.Context(), reviewID)
	if err != nil {
		if err == review.ErrReviewNotFound {
			h.logger.Warn("Review not found",
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "review not found"})
		} else {
			h.logger.Error("Failed to get review",
				zap.String("review_id", reviewID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get review"})
		}
		return
	}

	// Check if the user is the owner of the review
	if existingReview.UserID != userID.(primitive.ObjectID) {
		// Check if user is admin
		userRole, exists := c.Get("user_role")
		if !exists || userRole.(string) != "admin" {
			h.logger.Warn("Unauthorized review deletion attempt",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("review_owner_id", existingReview.UserID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusForbidden, gin.H{"error": "you can only delete your own reviews"})
			return
		}
	}

	h.logger.Info("Processing review deletion request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Delete the review
	err = h.service.DeleteReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.Error("Review deletion failed",
			zap.String("review_id", reviewID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete review"})
		return
	}

	h.logger.Info("Review deleted successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "review deleted successfully",
	})
}

// LikeReview handles liking a review
// @Summary Like a review
// @Description Add a like to a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Review not found"
// @Failure 409 {object} map[string]string "Already liked"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId}/like [post]
func (h *Handler) LikeReview(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	h.logger.Info("Processing review like request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Like the review
	err = h.service.LikeReview(c.Request.Context(), reviewID, userID.(primitive.ObjectID))
	if err != nil {
		switch err {
		case review.ErrReviewNotFound:
			h.logger.Warn("Review not found",
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "review not found"})
		case review.ErrAlreadyLiked:
			h.logger.Warn("Review already liked",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusConflict, gin.H{"error": "already liked this review"})
		default:
			h.logger.Error("Failed to like review",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to like review"})
		}
		return
	}

	h.logger.Info("Review liked successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "review liked successfully",
	})
}

// DislikeReview handles disliking a review
// @Summary Dislike a review
// @Description Add a dislike to a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Review not found"
// @Failure 409 {object} map[string]string "Already disliked"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId}/dislike [post]
func (h *Handler) DislikeReview(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	h.logger.Info("Processing review dislike request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Dislike the review
	err = h.service.DislikeReview(c.Request.Context(), reviewID, userID.(primitive.ObjectID))
	if err != nil {
		switch err {
		case review.ErrReviewNotFound:
			h.logger.Warn("Review not found",
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "review not found"})
		case review.ErrAlreadyDisliked:
			h.logger.Warn("Review already disliked",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusConflict, gin.H{"error": "already disliked this review"})
		default:
			h.logger.Error("Failed to dislike review",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to dislike review"})
		}
		return
	}

	h.logger.Info("Review disliked successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "review disliked successfully",
	})
}

// RemoveLike handles removing a like from a review
// @Summary Remove like from a review
// @Description Remove a like from a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Review not found or not liked"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId}/like [delete]
func (h *Handler) RemoveLike(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	h.logger.Info("Processing remove like request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Remove like
	err = h.service.RemoveLike(c.Request.Context(), reviewID, userID.(primitive.ObjectID))
	if err != nil {
		switch err {
		case review.ErrNotLiked:
			h.logger.Warn("Review not liked",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "you have not liked this review"})
		default:
			h.logger.Error("Failed to remove like",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove like"})
		}
		return
	}

	h.logger.Info("Like removed successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "like removed successfully",
	})
}

// RemoveDislike handles removing a dislike from a review
// @Summary Remove dislike from a review
// @Description Remove a dislike from a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Review not found or not disliked"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId}/dislike [delete]
func (h *Handler) RemoveDislike(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	h.logger.Info("Processing remove dislike request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Remove dislike
	err = h.service.RemoveDislike(c.Request.Context(), reviewID, userID.(primitive.ObjectID))
	if err != nil {
		switch err {
		case review.ErrNotDisliked:
			h.logger.Warn("Review not disliked",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "you have not disliked this review"})
		default:
			h.logger.Error("Failed to remove dislike",
				zap.String("review_id", reviewID.Hex()),
				zap.String("user_id", userID.(primitive.ObjectID).Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove dislike"})
		}
		return
	}

	h.logger.Info("Dislike removed successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.String("user_id", userID.(primitive.ObjectID).Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "dislike removed successfully",
	})
}

// ToggleReviewActive handles toggling the active status of a review
// @Summary Toggle review active status
// @Description Toggle the active status of a review (admin only)
// @Tags reviews
// @Accept json
// @Produce json
// @Param reviewId path string true "Review ID"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 403 {object} map[string]string "Unauthorized"
// @Failure 404 {object} map[string]string "Review not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/{reviewId}/toggle-active [put]
func (h *Handler) ToggleReviewActive(c *gin.Context) {
	reviewIDStr := c.Param("reviewId")

	// Convert review ID from string to ObjectID
	reviewID, err := primitive.ObjectIDFromHex(reviewIDStr)
	if err != nil {
		h.logger.Warn("Invalid review ID format",
			zap.String("review_id", reviewIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid review ID format"})
		return
	}

	// Check if user is admin
	userRole, exists := c.Get("user_role")
	if !exists || userRole.(string) != "admin" {
		h.logger.Warn("Unauthorized attempt to toggle review active status",
			zap.String("review_id", reviewID.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusForbidden, gin.H{"error": "admin access required"})
		return
	}

	h.logger.Info("Processing toggle review active status request",
		zap.String("review_id", reviewID.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Toggle the review active status
	err = h.service.ToggleReviewActive(c.Request.Context(), reviewID)
	if err != nil {
		if err == review.ErrReviewNotFound {
			h.logger.Warn("Review not found",
				zap.String("review_id", reviewID.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "review not found"})
		} else {
			h.logger.Error("Failed to toggle review active status",
				zap.String("review_id", reviewID.Hex()),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to toggle review active status"})
		}
		return
	}

	// Get the updated review
	updatedReview, err := h.service.GetReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.Error("Failed to get updated review",
			zap.String("review_id", reviewID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "review status toggled but failed to retrieve updated data"})
		return
	}

	h.logger.Info("Review active status toggled successfully",
		zap.String("review_id", reviewID.Hex()),
		zap.Bool("is_active", updatedReview.IsActive),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewResponse{
		Success: true,
		Message: "review active status toggled successfully",
		Review:  *updatedReview,
	})
}

// ListReviewsByUserForProperty handles listing reviews written by the current logged-in user for a specific property
// @Summary List reviews by current user for a specific property
// @Description Get all reviews written by the current logged-in user for a specific property with pagination and like status
// @Tags reviews
// @Accept json
// @Produce json
// @Param propertyId path string true "Property ID"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Security ApiKeyAuth
// @Success 200 {object} models.ReviewListWithLikeStatusResponse
// @Failure 400 {object} map[string]string "Invalid property ID"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /reviews/my-reviews/property/{propertyId} [get]
func (h *Handler) ListReviewsByUserForProperty(c *gin.Context) {
	// Get property ID from URL path
	propertyIDStr := c.Param("propertyId")

	// Convert property ID from string to ObjectID
	propertyID, err := primitive.ObjectIDFromHex(propertyIDStr)
	if err != nil {
		h.logger.Warn("Invalid property ID format",
			zap.String("property_id", propertyIDStr),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid property ID format"})
		return
	}

	// Get user ID from authentication context (required)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("property_id", propertyID.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "user not authenticated"})
		return
	}

	reviewAuthorID := userID.(primitive.ObjectID)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	h.logger.Info("Processing user reviews for property request",
		zap.String("user_id", reviewAuthorID.Hex()),
		zap.String("property_id", propertyID.Hex()),
		zap.Int("page", page),
		zap.Int("limit", limit),
		zap.String("ip", c.ClientIP()),
	)

	// Check if user is admin
	isAdmin := false
	userRole, exists := c.Get("user_role")
	if exists && userRole.(string) == "admin" {
		isAdmin = true
	}

	// Get reviews written by this user for this specific property
	reviewsWithLikeStatus, total, err := h.service.ListReviewsByUserIDAndPropertyID(c.Request.Context(), reviewAuthorID, propertyID, int64(page), int64(limit), isAdmin, &reviewAuthorID)
	if err != nil {
		h.logger.Error("Failed to list user reviews for property",
			zap.String("user_id", reviewAuthorID.Hex()),
			zap.String("property_id", propertyID.Hex()),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list user reviews for property"})
		return
	}

	// Convert to response format
	var reviewsData []models.ReviewWithLikeStatus
	for _, r := range reviewsWithLikeStatus {
		reviewsData = append(reviewsData, *r)
	}

	// Calculate pagination info
	totalPages := int((total + int64(limit) - 1) / int64(limit))
	hasNext := int64(page) < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("User reviews for property retrieved successfully",
		zap.String("user_id", reviewAuthorID.Hex()),
		zap.String("property_id", propertyID.Hex()),
		zap.Int64("total", total),
		zap.Int("count", len(reviewsData)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.ReviewListWithLikeStatusResponse{
		Data: reviewsData,
		Pagination: models.Pagination{
			Total:       total,
			Page:        page,
			Limit:       limit,
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	})
}
