package api

import (
	"realestate-platform/internal/api/admin"
	favoriteHandler "realestate-platform/internal/api/favorite"
	"realestate-platform/internal/api/middleware"
	propertyHandler "realestate-platform/internal/api/property"
	propertyMetadataHandler "realestate-platform/internal/api/property_metadata"
	reviewHandler "realestate-platform/internal/api/review"
	subscriptionHandler "realestate-platform/internal/api/subscription"
	userHandler "realestate-platform/internal/api/user"
	"realestate-platform/internal/config"
	"realestate-platform/internal/repository/mongodb"
	favoriteService "realestate-platform/internal/services/favorite"
	propertyService "realestate-platform/internal/services/property"
	propertyMetadataService "realestate-platform/internal/services/property_metadata"
	reviewService "realestate-platform/internal/services/review"
	subscriptionService "realestate-platform/internal/services/subscription"
	userService "realestate-platform/internal/services/user"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"
)

func NewRouter(cfg *config.Config, db *mongodb.MongoDBClient, logger *zap.Logger) *gin.Engine {
	// Set Gin mode
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create router
	router := gin.New()

	// Use middleware
	router.Use(gin.Recovery())
	router.Use(middleware.Logger(logger))
	router.Use(middleware.CORS())
	router.Use(middleware.SanitizerMiddleware())

	// Serve static files from uploads directory with custom handler
	router.GET("/uploads/*filename", func(c *gin.Context) {
		filename := c.Param("filename")
		// Remove leading slash if present
		if len(filename) > 0 && filename[0] == '/' {
			filename = filename[1:]
		}
		c.File("./uploads/" + filename)
	})

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":      "ok",
			"environment": cfg.Server.Environment,
		})
	})

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API routes
	api := router.Group("/api/v1")
	{
		// Create user service once for all user-related routes
		userSvc := userService.NewService(db.GetCollection("users").Database(), cfg.JWT.SecretKey)
		userHdl := userHandler.NewHandler(userSvc, logger)

		// Create subscription service and handler
		subscriptionSvc := subscriptionService.NewService(db)
		subscriptionHdl := subscriptionHandler.NewHandler(subscriptionSvc, logger)

		// Public routes
		public := api.Group("")
		{
			// Register only auth routes (public)
			userHdl.RegisterAuthRoutes(public)

			// Property service for public routes
			propertyService := propertyService.NewService(db)
			favService := favoriteService.NewService(db)
			metadataService := propertyMetadataService.NewService(db)
			propertyHandler := propertyHandler.NewHandler(propertyService, favService, metadataService, logger)

			// Register public property routes (no auth required)
			propertyHandler.RegisterPublicRoutes(public)
		}

		// Protected routes
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware(cfg, logger))
		{
			// Register subscription routes
			subscriptionHdl.RegisterSubscriptionRoutes(protected)

			// Register user profile routes (protected with auth middleware)
			userHdl.RegisterUserRoutes(protected)

			// Property routes
			service := propertyService.NewService(db)
			favService := favoriteService.NewService(db)
			metadataService := propertyMetadataService.NewService(db)
			handler := propertyHandler.NewHandler(service, favService, metadataService, logger)
			handler.RegisterRoutes(protected)

			// Favorite routes
			favHandler := favoriteHandler.NewHandler(favService, logger)
			favHandler.RegisterRoutes(protected)

			// Review routes
			reviewSvc := reviewService.NewService(db)
			reviewHdl := reviewHandler.NewHandler(reviewSvc, logger)
			reviewHdl.RegisterRoutes(protected)

			// Property metadata routes for authenticated users
			metadataHandler := propertyMetadataHandler.NewHandler(metadataService, logger)
			metadataHandler.RegisterRoutes(protected)
		}

		// Admin routes
		adminGroup := api.Group("/admin")
		adminGroup.Use(middleware.AuthMiddleware(cfg, logger))
		adminGroup.Use(middleware.RoleMiddleware("admin", logger))
		{
			// Property metadata service for admin routes
			metadataService := propertyMetadataService.NewService(db)

			// Property Type routes
			propertyTypeHandler := admin.NewPropertyTypeHandler(metadataService, logger)
			propertyTypeHandler.RegisterRoutes(adminGroup)

			// Facility routes
			facilityHandler := admin.NewFacilityHandler(metadataService, logger)
			facilityHandler.RegisterRoutes(adminGroup)

			// User management routes
			userSvc := userService.NewService(db.GetCollection("users").Database(), cfg.JWT.SecretKey)
			userHandler := admin.NewUserHandler(userSvc, logger)
			userHandler.RegisterRoutes(adminGroup)
		}
	}

	return router
}
