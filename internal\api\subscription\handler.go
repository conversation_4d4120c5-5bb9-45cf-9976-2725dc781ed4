package subscription

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/services/subscription"
)

type Handler struct {
	service *subscription.Service
	logger  *zap.Logger
}

func NewHandler(service *subscription.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterSubscriptionRoutes registers all subscription package routes
func (h *Handler) RegisterSubscriptionRoutes(router *gin.RouterGroup) {
	subscription := router.Group("/subscription")
	{
		subscription.POST("/packages", h.CreatePackage)
		subscription.GET("/packages", h.ListPackages)
		subscription.GET("/packages/:id", h.GetPackage)
		subscription.PUT("/packages/:id", h.UpdatePackage)
		subscription.DELETE("/packages/:id", h.DeletePackage)
		subscription.GET("/packages/popular", h.GetPopularPackages)
	}
}

// CreatePackage handles the creation of a new subscription package
// @Summary Create a new subscription package
// @Description Create a new subscription package with the provided details
// @Tags subscription
// @Accept json
// @Produce json
// @Param package body models.CreatePackageRequest true "Package details"
// @Success 201 {object} models.Package
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages [post]
func (h *Handler) CreatePackage(c *gin.Context) {
	var req models.CreatePackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid package creation request",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing package creation request",
		zap.Any("package_type", req.PackageType),
		zap.String("ip", c.ClientIP()),
	)

	package_, err := h.service.CreateSubscriptionPackage(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Package creation failed",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Package created successfully",
		zap.String("package_id", package_.ID.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusCreated, package_)
}

// ListPackages handles listing all subscription packages with pagination
// @Summary List subscription packages
// @Description Get a paginated list of subscription packages
// @Tags subscription
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} models.PaginatedResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages [get]
func (h *Handler) ListPackages(c *gin.Context) {
	page, _ := strconv.ParseInt(c.DefaultQuery("page", "1"), 10, 64)
	limit, _ := strconv.ParseInt(c.DefaultQuery("limit", "10"), 10, 64)

	h.logger.Info("Processing package list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.String("ip", c.ClientIP()),
	)

	packages, total, err := h.service.ListSubscriptionPackages(c.Request.Context(), bson.M{}, page, limit)
	if err != nil {
		h.logger.Error("Package listing failed",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	totalPages := (total + limit - 1) / limit
	hasNext := page < totalPages
	hasPrevious := page > 1

	h.logger.Info("Package list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(packages)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, models.PaginatedResponse{
		Data: packages,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  int(totalPages),
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	})
}

// GetPackage handles retrieving a specific subscription package
// @Summary Get a subscription package
// @Description Get a subscription package by ID
// @Tags subscription
// @Produce json
// @Param id path string true "Package ID"
// @Success 200 {object} models.Package
// @Failure 400 {object} map[string]string "Invalid ID format"
// @Failure 404 {object} map[string]string "Package not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages/{id} [get]
func (h *Handler) GetPackage(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid package ID format",
			zap.String("id", c.Param("id")),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid package id"})
		return
	}

	h.logger.Info("Processing package retrieval request",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	package_, err := h.service.GetSubscriptionPackage(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Package retrieval failed",
			zap.String("package_id", id.Hex()),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if package_ == nil {
		h.logger.Warn("Package not found",
			zap.String("package_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "package not found"})
		return
	}

	h.logger.Info("Package retrieved successfully",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, package_)
}

// UpdatePackage handles updating a subscription package
// @Summary Update a subscription package
// @Description Update an existing subscription package
// @Tags subscription
// @Accept json
// @Produce json
// @Param id path string true "Package ID"
// @Param package body models.UpdatePackageRequest true "Package details"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "Package not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages/{id} [put]
func (h *Handler) UpdatePackage(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid package ID format",
			zap.String("id", c.Param("id")),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid package id"})
		return
	}

	var req models.UpdatePackageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid package update request",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing package update request",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	err = h.service.UpdateSubscriptionPackage(c.Request.Context(), id, &req)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			h.logger.Warn("Package not found for update",
				zap.String("package_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "package not found"})
			return
		}
		h.logger.Error("Package update failed",
			zap.String("package_id", id.Hex()),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Package updated successfully",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "package updated successfully"})
}

// DeletePackage handles deleting a subscription package
// @Summary Delete a subscription package
// @Description Delete an existing subscription package
// @Tags subscription
// @Produce json
// @Param id path string true "Package ID"
// @Success 200 {object} map[string]string "Success message"
// @Failure 400 {object} map[string]string "Invalid ID format"
// @Failure 404 {object} map[string]string "Package not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages/{id} [delete]
func (h *Handler) DeletePackage(c *gin.Context) {
	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		h.logger.Warn("Invalid package ID format",
			zap.String("id", c.Param("id")),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid package id"})
		return
	}

	h.logger.Info("Processing package deletion request",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	err = h.service.DeleteSubscriptionPackage(c.Request.Context(), id)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			h.logger.Warn("Package not found for deletion",
				zap.String("package_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			c.JSON(http.StatusNotFound, gin.H{"error": "package not found"})
			return
		}
		h.logger.Error("Package deletion failed",
			zap.String("package_id", id.Hex()),
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Package deleted successfully",
		zap.String("package_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, gin.H{"message": "package deleted successfully"})
}

// GetPopularPackages handles retrieving popular subscription packages
// @Summary Get popular packages
// @Description Get a list of popular subscription packages
// @Tags subscription
// @Produce json
// @Success 200 {array} models.Package
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/subscription/packages/popular [get]
func (h *Handler) GetPopularPackages(c *gin.Context) {
	h.logger.Info("Processing popular packages request",
		zap.String("ip", c.ClientIP()),
	)

	packages, err := h.service.GetPopularPackages(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to retrieve popular packages",
			zap.Error(err),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Popular packages retrieved successfully",
		zap.Int("count", len(packages)),
		zap.String("ip", c.ClientIP()),
	)

	c.JSON(http.StatusOK, packages)
}
