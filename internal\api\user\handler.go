package user

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/api/middleware"
	"realestate-platform/internal/models"
	userService "realestate-platform/internal/services/user"
	"realestate-platform/internal/utils"
	customValidator "realestate-platform/internal/validator"
)

type Handler struct {
	service *userService.Service
	logger  *zap.Logger
}

func NewHandler(service *userService.Service, logger *zap.Logger) *Handler {
	return &Handler{
		service: service,
		logger:  logger,
	}
}

// RegisterAuthRoutes registers public authentication routes
func (h *Handler) RegisterAuthRoutes(router *gin.RouterGroup) {
	auth := router.Group("/auth")
	{
		auth.POST("/register", h.Register)
		auth.POST("/login", h.Login)
		auth.POST("/send-otp", h.SendOTP)
		auth.POST("/verify-otp", h.VerifyOTP)
		auth.POST("/resend-otp", h.ResendOTP)
		auth.POST("/forgot-password", h.ForgotPassword)
		auth.POST("/reset-password", h.ResetPassword)
	}
}

// RegisterUserRoutes registers protected user profile routes
func (h *Handler) RegisterUserRoutes(router *gin.RouterGroup) {
	user := router.Group("/user")
	{
		user.POST("/complete-profile", h.CompleteProfile)
		user.PUT("/profile", h.UpdateProfile)
		user.POST("/change-password", h.ChangePassword)
		user.GET("/top-agents", h.ListTopPerformingAgents)
		user.GET("/details", h.GetUserDetails)
	}
}

// RegisterRoutes is kept for backward compatibility
// It's recommended to use RegisterAuthRoutes and RegisterUserRoutes instead
// func (h *Handler) RegisterRoutes(router *gin.RouterGroup) {
// 	h.RegisterAuthRoutes(router)
// 	// Note: This will register user routes without authentication
// 	// For proper authentication, use RegisterUserRoutes with a protected group
// 	h.RegisterUserRoutes(router)
// }

// Register handles user registration
// @Summary Register a new user
// @Description Register a new user with the provided information
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.RegisterRequest true "User Registration Info"
// @Success 201 {object} models.User
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 409 {object} map[string]string "User already exists"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/register [post]
func (h *Handler) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email),
						zap.String("error", e.Error()),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}

				// Handle password validation errors
				if e.Field() == "Password" && e.Tag() == "password" {
					// Get specific password validation error
					passwordError := customValidator.GetPasswordValidationError(req.Password)
					h.logger.Warn("Password validation failed",
						zap.String("error", passwordError),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": passwordError})
					return
				}
			}
		}

		h.logger.Warn("Invalid registration request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing registration request",
		zap.String("email", req.Email),
		zap.String("ip", c.ClientIP()),
	)

	user, err := h.service.Register(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case userService.ErrUserExists:
			h.logger.Warn("Registration failed - user already exists",
				zap.String("email", req.Email),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusConflict, gin.H{"error": "user already exists"})
		default:
			h.logger.Error("Registration failed - internal error",
				zap.String("email", req.Email),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("User registration successful",
		zap.String("user_id", user.ID.Hex()),
		zap.String("email", user.Email),
		zap.String("ip", c.ClientIP()),
	)

	middleware.WriteJSONResponse(c, http.StatusCreated, gin.H{
		"user": user,
	})
}

// Login handles user authentication
// @Summary Login a user
// @Description Authenticate a user and return a JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param credentials body models.LoginRequest true "Login Credentials"
// @Success 200 {object} models.AuthResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Invalid credentials"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/login [post]
func (h *Handler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email),
						zap.String("error", e.Error()),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}

				// Handle password validation errors
				if e.Field() == "Password" && e.Tag() == "password" {
					// Get specific password validation error
					passwordError := customValidator.GetPasswordValidationError(req.Password)
					h.logger.Warn("Password validation failed",
						zap.String("error", passwordError),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": passwordError})
					return
				}
			}
		}

		h.logger.Warn("Invalid login request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing login request",
		zap.String("email", req.Email),
		zap.String("ip", c.ClientIP()),
	)

	response, err := h.service.Login(c.Request.Context(), &req)
	if err != nil {
		switch err {
		case userService.ErrInvalidCredentials:
			h.logger.Warn("Login failed - invalid credentials",
				zap.String("email", req.Email),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "invalid credentials"})
		case userService.ErrEmailNotVerified:
			h.logger.Warn("Login failed - email not verified",
				zap.String("email", req.Email),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusAccepted, gin.H{"error": "Please verify your email before logging in. Check your inbox for the verification code."})
		default:
			h.logger.Error("Login failed - internal error",
				zap.String("email", req.Email),
				zap.String("error", err.Error()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	h.logger.Info("User login successful",
		zap.String("user_id", response.User.ID.Hex()),
		zap.String("email", response.User.Email),
		zap.String("ip", c.ClientIP()),
	)

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"response": response,
	})
}

// SendOTP handles sending OTP for user registration
// @Summary Send OTP for registration
// @Description Send OTP to user's email for verification
// @Tags auth
// @Accept json
// @Produce json
// @Param user body models.SendOTPRequest true "User Registration Info with Email"
// @Success 200 {object} models.OTPResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 409 {object} map[string]string "User already exists"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/send-otp [post]
func (h *Handler) SendOTP(c *gin.Context) {
	var req models.SendOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email),
						zap.String("error", e.Error()),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}

				// Handle password validation errors
				if e.Field() == "Password" && e.Tag() == "password" {
					// Get specific password validation error
					passwordError := customValidator.GetPasswordValidationError(req.Password)
					h.logger.Warn("Password validation failed",
						zap.String("error", passwordError),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": passwordError})
					return
				}
			}
		}

		h.logger.Warn("Invalid send OTP request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing send OTP request",
		zap.String("email", req.Email),
		zap.String("ip", c.ClientIP()),
	)

	// Validate phone number using Google's libphonenumber
	if err := customValidator.ValidatePhoneNumber(req.PhoneNumber); err != nil {
		h.logger.Warn("Send OTP failed - invalid phone number",
			zap.String("email", req.Email),
			zap.String("phone", req.PhoneNumber),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create EmailJS config
	emailJSConfig := utils.EmailJSConfig{
		ServiceID:  os.Getenv("EMAILJS_SERVICE_ID"),
		TemplateID: os.Getenv("EMAILJS_OTP_VERIFICATION_TEMPLATE_ID"),
		UserID:     os.Getenv("EMAILJS_USER_ID"),
	}

	response, err := h.service.SendOTP(c.Request.Context(), &req, emailJSConfig)
	if err != nil {
		errMsg := err.Error()

		// Handle specific error cases with appropriate status codes
		switch {
		case errMsg == "a user with this email already exists" || errMsg == "a user with this full name already exists":
			h.logger.Warn("Send OTP failed - user already exists",
				zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusConflict, gin.H{"error": errMsg})

		case errMsg == "invalid birth date format. Use YYYY-MM-DD":
			h.logger.Warn("Send OTP failed - invalid input",
				zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": errMsg})

		case errMsg == "missing required EmailJS configuration":
			h.logger.Error("Send OTP failed - server configuration error")
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Server configuration error. Please contact support."})

		case errMsg == "failed to send email: authentication failed":
			h.logger.Error("Send OTP failed - email service authentication error")
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Unable to send verification email. Please try again later."})

		case errMsg == "failed to send email: rate limit exceeded":
			h.logger.Error("Send OTP failed - email rate limit exceeded")
			middleware.WriteJSONResponse(c, http.StatusTooManyRequests,
				gin.H{"error": "Too many verification attempts. Please try again later."})

		default:
			// Generic error handling for other cases
			h.logger.Error("Send OTP failed", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to send verification code. Please try again later."})
		}
		return
	}

	h.logger.Info("OTP sent successfully",
		zap.String("email", req.Email),
		zap.String("ip", c.ClientIP()),
	)

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"success": response.Success,
		"message": response.Message,
	})
}

// VerifyOTP handles OTP verification and user registration
// @Summary Verify OTP and register user
// @Description Verify OTP and complete user registration with JWT token
// @Tags auth
// @Accept json
// @Produce json
// @Param verification body models.VerifyOTPRequest true "OTP Verification Info"
// @Success 200 {object} models.VerifyOTPResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Invalid OTP"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/verify-otp [post]
func (h *Handler) VerifyOTP(c *gin.Context) {
	var req models.VerifyOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email),
						zap.String("error", e.Error()),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}
			}
		}

		h.logger.Warn("Invalid verify OTP request",
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info("Processing verify OTP request",
		zap.String("email", req.Email),
		zap.String("ip", c.ClientIP()),
	)

	response, err := h.service.VerifyOTP(c.Request.Context(), &req)
	if err != nil {
		switch {
		case err == userService.ErrInvalidOTP:
			h.logger.Warn("Verify OTP failed - invalid OTP",
				zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusUnauthorized,
				gin.H{"error": "Invalid verification code. Please try again."})

		case err.Error() == "user not found":
			h.logger.Warn("Verify OTP failed - user not found",
				zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusNotFound,
				gin.H{"error": "No verification request found for this email."})

		default:
			h.logger.Error("Verify OTP failed", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to verify your account. Please try again later."})
		}
		return
	}

	h.logger.Info("OTP verification successful, user registered",
		zap.String("user_id", response.User.ID.Hex()),
		zap.String("email", response.User.Email),
		zap.String("ip", c.ClientIP()),
	)

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"response": response,
	})
}

// ResendOTP handles resending OTP for user registration
// @Summary Resend OTP for registration
// @Description Resend OTP to user's email for verification
// @Tags auth
// @Accept json
// @Produce json
// @Param verification body models.ResendOTPRequest true "Email for OTP Resend"
// @Success 200 {object} models.OTPResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 409 {object} map[string]string "User already verified"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/resend-otp [post]
func (h *Handler) ResendOTP(c *gin.Context) {
	var req models.ResendOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email))
					middleware.WriteJSONResponse(c, http.StatusBadRequest,
						gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}
			}
		}

		h.logger.Warn("Invalid resend OTP request", zap.Error(err))
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info("Processing resend OTP request", zap.String("email", req.Email))

	// Create EmailJS config
	emailJSConfig := utils.EmailJSConfig{
		ServiceID:  os.Getenv("EMAILJS_SERVICE_ID"),
		TemplateID: os.Getenv("EMAILJS_OTP_VERIFICATION_TEMPLATE_ID"),
		UserID:     os.Getenv("EMAILJS_USER_ID"),
	}

	response, err := h.service.ResendOTP(c.Request.Context(), req.Email, emailJSConfig)
	if err != nil {
		errMsg := err.Error()

		switch {
		case errMsg == "user not found":
			h.logger.Warn("Resend OTP failed - user not found", zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusNotFound,
				gin.H{"error": "No verification request found for this email."})

		case errMsg == "user is already verified":
			h.logger.Warn("Resend OTP failed - user already verified", zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusConflict,
				gin.H{"error": "This email is already verified. Please login instead."})

		case strings.Contains(errMsg, "failed to send OTP email"):
			h.logger.Error("Resend OTP failed - email sending error", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to send verification code. Please try again later."})

		default:
			h.logger.Error("Resend OTP failed", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to resend verification code. Please try again later."})
		}
		return
	}

	h.logger.Info("OTP resent successfully", zap.String("email", req.Email))

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"success": response.Success,
		"message": response.Message,
	})
}

// ForgotPassword handles password reset requests
// @Summary Request password reset
// @Description Send a password reset link to the user's email
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.ForgotPasswordRequest true "Email for password reset"
// @Success 200 {object} models.ForgotPasswordResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/forgot-password [post]
func (h *Handler) ForgotPassword(c *gin.Context) {
	var req models.ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email))
					middleware.WriteJSONResponse(c, http.StatusBadRequest,
						gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}
			}
		}

		h.logger.Warn("Invalid forgot password request", zap.Error(err))
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info("Processing forgot password request", zap.String("email", req.Email))

	// Get frontend URL from environment or use default
	frontendURL := os.Getenv("FRONTEND_URL")
	if frontendURL == "" {
		frontendURL = "http://localhost:3000" // Default frontend URL
	}

	// Create EmailJS config
	emailJSConfig := utils.EmailJSConfig{
		ServiceID:  os.Getenv("EMAILJS_SERVICE_ID"),
		TemplateID: os.Getenv("EMAILJS_RESET_PASSWORD_TEMPLATE_ID"),
		UserID:     os.Getenv("EMAILJS_USER_ID"),
	}

	response, err := h.service.ForgotPassword(c.Request.Context(), req.Email, frontendURL, emailJSConfig)
	if err != nil {
		switch {
		case err == userService.ErrUserNotFound:
			// For security reasons, don't reveal if the email exists or not
			h.logger.Info("Forgot password requested for non-existent email",
				zap.String("email", req.Email))
			// Return success even if user doesn't exist to prevent email enumeration
			middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
				"success": true,
				"message": "If your email is registered, you will receive a password reset link shortly.",
			})
			return

		case strings.Contains(err.Error(), "failed to send password reset email"):
			h.logger.Error("Forgot password failed - email sending error", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to send password reset email. Please try again later."})

		default:
			h.logger.Error("Forgot password failed", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to process your request. Please try again later."})
		}
		return
	}

	h.logger.Info("Password reset link sent successfully", zap.String("email", req.Email))

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"success": response.Success,
		"message": "you will receive a password reset link shortly.",
	})
}

// ResetPassword handles password reset with token
// @Summary Reset password with token
// @Description Reset user password using the token received via email
// @Tags auth
// @Accept json
// @Produce json
// @Param request body models.ResetPasswordRequest true "Password Reset Info"
// @Success 200 {object} models.ResetPasswordResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Invalid token"
// @Failure 404 {object} map[string]string "User not found"
// @Failure 500 {object} map[string]string "Server error"
// @Router /auth/reset-password [post]
func (h *Handler) ResetPassword(c *gin.Context) {
	var req models.ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle email validation errors
				if e.Field() == "Email" && e.Tag() == "email" {
					h.logger.Warn("Invalid email format in validation",
						zap.String("email", req.Email))
					middleware.WriteJSONResponse(c, http.StatusBadRequest,
						gin.H{"error": "Invalid email format. Please provide a valid email address."})
					return
				}

				// Handle password validation errors
				if e.Field() == "NewPassword" && e.Tag() == "password" {
					// Get specific password validation error
					passwordError := customValidator.GetPasswordValidationError(req.NewPassword)
					h.logger.Warn("Password validation failed",
						zap.String("error", passwordError))
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": passwordError})
					return
				}
			}
		}

		h.logger.Warn("Invalid reset password request", zap.Error(err))
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info("Processing reset password request", zap.String("email", req.Email))

	response, err := h.service.ResetPassword(c.Request.Context(), &req)
	if err != nil {
		switch {
		case err == userService.ErrUserNotFound:
			h.logger.Warn("Reset password failed - user not found", zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusNotFound,
				gin.H{"error": "No account found with this email address."})

		case err == userService.ErrInvalidToken:
			h.logger.Warn("Reset password failed - invalid or expired token", zap.String("email", req.Email))
			middleware.WriteJSONResponse(c, http.StatusUnauthorized,
				gin.H{"error": "Invalid or expired reset link. Please request a new password reset."})

		default:
			h.logger.Error("Reset password failed", zap.Error(err))
			middleware.WriteJSONResponse(c, http.StatusInternalServerError,
				gin.H{"error": "Failed to reset your password. Please try again later."})
		}
		return
	}

	h.logger.Info("Password reset successful", zap.String("email", req.Email))

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"success": response.Success,
		"message": response.Message,
	})
}

// CompleteProfile handles the profile completion request with profile image upload
// @Summary Complete user profile with image upload
// @Description Complete user profile with address, contact information, and profile image upload
// @Tags user
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param profile formData string true "Profile completion details (JSON string)"
// @Param profile_image formData file false "Profile image (single file)"
// @Success 200 {object} models.User "User profile data"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/complete-profile [post]
func (h *Handler) CompleteProfile(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context")
		middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Use the ObjectID directly
	id := userID.(primitive.ObjectID)

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get profile details from form
	profileJSON := c.PostForm("profile")
	if profileJSON == "" {
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "profile details are required"})
		return
	}

	// Parse profile details
	var req models.CompleteProfileRequest
	if err := json.Unmarshal([]byte(profileJSON), &req); err != nil {
		h.logger.Warn("Invalid profile completion request",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "invalid profile details format"})
		return
	}

	h.logger.Info("Processing complete profile request", zap.String("user_id", id.Hex()))

	// Process profile image upload (single file) - optimized
	var profileImageURL string
	file, err := c.FormFile("profile_image")
	if err != nil && err != http.ErrMissingFile {
		h.logger.Warn("Failed to get profile image file",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "failed to get profile image"})
		return
	}

	// If profile image is provided, process it
	if file != nil {
		// Create uploads directory only when needed
		uploadDir := "uploads/profiles"
		if err := os.MkdirAll(uploadDir, 0755); err != nil {
			h.logger.Error("Failed to create uploads directory",
				zap.String("error", err.Error()),
				zap.String("user_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
			return
		}

		// Generate unique filename with URL-safe characters (same format as property/review APIs)
		originalName := filepath.Base(file.Filename)
		ext := filepath.Ext(originalName)
		nameWithoutExt := strings.TrimSuffix(originalName, ext)
		safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
		safeName = strings.ReplaceAll(safeName, ":", "_")
		safeName = strings.ReplaceAll(safeName, "/", "_")
		safeName = strings.ReplaceAll(safeName, "\\", "_")

		// Generate unique filename using timestamp and random string (consistent with property/review APIs)
		timestamp := time.Now().UnixNano()
		randomStr := primitive.NewObjectID().Hex()
		filename := fmt.Sprintf("profile_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
		filePath := filepath.Join(uploadDir, filename)

		// Save file
		if err := c.SaveUploadedFile(file, filePath); err != nil {
			h.logger.Error("Failed to save profile image",
				zap.String("error", err.Error()),
				zap.String("user_id", id.Hex()),
				zap.String("filename", filename),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "failed to save profile image"})
			return
		}

		// Set profile image URL (use forward slashes for URLs)
		profileImageURL = fmt.Sprintf("/uploads/profiles/%s", filename)

		h.logger.Info("Profile image uploaded successfully",
			zap.String("user_id", id.Hex()),
			zap.String("filename", filename),
			zap.String("url", profileImageURL),
		)
	}

	// Create location from request fields
	location := &models.LocationRequest{
		Address: req.Address,
		City:    req.City,
		State:   req.State,
		Country: req.Country,
		ZipCode: req.ZipCode,
	}

	// Create update request
	updateReq := &models.UpdateUserRequest{
		PhoneNumber:       req.PhoneNumber,
		Location:          location,
		ProfilePhoto:      profileImageURL, // Set profile image URL
		IsProfileComplete: true,
	}

	// Update user profile
	user, err := h.service.UpdateUserProfile(c.Request.Context(), id, updateReq)
	if err != nil {
		h.logger.Error("Complete profile failed",
			zap.Error(err),
			zap.String("user_id", id.Hex()))
		middleware.WriteJSONResponse(c, http.StatusInternalServerError,
			gin.H{"error": "Failed to update your profile. Please try again later."})
		return
	}

	h.logger.Info("Profile completion successful", zap.String("user_id", id.Hex()))

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"user": user,
	})
}

// UpdateProfile handles the profile update request with profile image upload
// @Summary Update user profile with image upload
// @Description Update user profile information with optional profile image upload
// @Tags user
// @Accept multipart/form-data
// @Produce json
// @Security BearerAuth
// @Param profile formData string true "Profile update details (JSON string)"
// @Param profile_image formData file false "Profile image (single file)"
// @Success 200 {object} models.User "User profile data"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/profile [put]
func (h *Handler) UpdateProfile(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context")
		middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Use the ObjectID directly
	id := userID.(primitive.ObjectID)

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(32 << 20); err != nil { // 32MB max
		h.logger.Warn("Failed to parse multipart form",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Get profile details from form
	profileJSON := c.PostForm("profile")
	if profileJSON == "" {
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "profile details are required"})
		return
	}

	// Parse profile details
	var req models.UpdateUserRequest
	if err := json.Unmarshal([]byte(profileJSON), &req); err != nil {
		h.logger.Warn("Invalid update profile request",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "invalid profile details format"})
		return
	}

	h.logger.Info("Processing update profile request", zap.String("user_id", id.Hex()))

	// Get current user data to check existing profile photo
	currentUser, err := h.service.GetUser(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get current user data",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
		)
		middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "failed to get user data"})
		return
	}

	// Process profile image upload (single file) if provided
	file, err := c.FormFile("profile_image")
	if err != nil && err != http.ErrMissingFile {
		h.logger.Warn("Failed to get profile image file",
			zap.String("error", err.Error()),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "failed to get profile image"})
		return
	}

	// If profile image is provided, process it
	if file != nil {
		// Check if the uploaded image is the same as the current one
		currentImageName := ""
		if currentUser.ProfilePhoto != "" {
			// Extract filename from current profile photo URL
			// e.g., "/uploads/profiles/profile_abc123_1703123456789_image.jpg" -> "profile_abc123_1703123456789_image.jpg"
			parts := strings.Split(currentUser.ProfilePhoto, "/")
			if len(parts) > 0 {
				currentImageName = parts[len(parts)-1]
			}
		}

		// Generate safe filename for comparison
		originalName := filepath.Base(file.Filename)
		ext := filepath.Ext(originalName)
		nameWithoutExt := strings.TrimSuffix(originalName, ext)
		safeName := strings.ReplaceAll(nameWithoutExt, " ", "_")
		safeName = strings.ReplaceAll(safeName, ":", "_")
		safeName = strings.ReplaceAll(safeName, "/", "_")
		safeName = strings.ReplaceAll(safeName, "\\", "_")

		// Check if the same image is being uploaded (by comparing original filename)
		if currentImageName != "" && strings.Contains(currentImageName, safeName) && strings.HasSuffix(currentImageName, ext) {
			h.logger.Info("Same image uploaded, skipping processing",
				zap.String("user_id", id.Hex()),
				zap.String("filename", originalName),
			)
			// Skip processing and don't update ProfilePhoto field
			// The existing image URL will remain unchanged
		} else {
			// Create uploads directory only when needed
			uploadDir := "uploads/profiles"
			if err := os.MkdirAll(uploadDir, 0755); err != nil {
				h.logger.Error("Failed to create uploads directory",
					zap.String("error", err.Error()),
					zap.String("user_id", id.Hex()),
					zap.String("ip", c.ClientIP()),
				)
				middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "failed to create uploads directory"})
				return
			}

			// Generate unique filename using timestamp and random string (consistent with property/review APIs)
			timestamp := time.Now().UnixNano()
			randomStr := primitive.NewObjectID().Hex()
			filename := fmt.Sprintf("profile_%s_%d_%s%s", randomStr, timestamp, safeName, ext)
			filePath := filepath.Join(uploadDir, filename)

			// Save new file
			if err := c.SaveUploadedFile(file, filePath); err != nil {
				h.logger.Error("Failed to save profile image",
					zap.String("error", err.Error()),
					zap.String("user_id", id.Hex()),
					zap.String("filename", filename),
					zap.String("ip", c.ClientIP()),
				)
				middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "failed to save profile image"})
				return
			}

			// Delete previous profile image if it exists
			if currentUser.ProfilePhoto != "" {
				// Convert URL path to filesystem path
				// e.g., "/uploads/profiles/image.jpg" -> "uploads/profiles/image.jpg"
				oldImagePath := strings.TrimPrefix(currentUser.ProfilePhoto, "/")
				if err := os.Remove(oldImagePath); err != nil {
					// Log warning but don't fail the request
					h.logger.Warn("Failed to delete previous profile image",
						zap.String("error", err.Error()),
						zap.String("user_id", id.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				} else {
					h.logger.Info("Previous profile image deleted successfully",
						zap.String("user_id", id.Hex()),
						zap.String("old_image_path", oldImagePath),
					)
				}
			}

			// Set new profile image URL (use forward slashes for URLs)
			profileImageURL := fmt.Sprintf("/uploads/profiles/%s", filename)
			req.ProfilePhoto = profileImageURL

			h.logger.Info("New profile image uploaded successfully",
				zap.String("user_id", id.Hex()),
				zap.String("filename", filename),
				zap.String("url", profileImageURL),
			)
		}
	}

	// Update user profile
	user, err := h.service.UpdateUserProfile(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error("Update profile failed",
			zap.Error(err),
			zap.String("user_id", id.Hex()))
		middleware.WriteJSONResponse(c, http.StatusInternalServerError,
			gin.H{"error": "Failed to update your profile. Please try again later."})
		return
	}

	h.logger.Info("Profile update successful", zap.String("user_id", id.Hex()))

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"user": user,
	})
}

// ChangePassword handles the password change request
// @Summary Change user password
// @Description Change the authenticated user's password
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.ChangePasswordRequest true "Password Change Info"
// @Success 200 {object} models.ChangePasswordResponse
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized or invalid current password"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/change-password [post]
func (h *Handler) ChangePassword(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context",
			zap.String("path", c.Request.URL.Path),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "unauthorized"})
		return
	}

	// Use the ObjectID directly
	id := userID.(primitive.ObjectID)

	// Parse request body
	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Check if it's a validation error
		var validationErrors validator.ValidationErrors
		if errors.As(err, &validationErrors) {
			for _, e := range validationErrors {
				// Handle password validation errors
				if e.Field() == "NewPassword" && e.Tag() == "password" {
					// Get specific password validation error
					passwordError := customValidator.GetPasswordValidationError(req.NewPassword)
					h.logger.Warn("Password validation failed",
						zap.String("error", passwordError),
						zap.String("user_id", id.Hex()),
						zap.String("ip", c.ClientIP()),
					)
					middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": passwordError})
					return
				}
			}
		}

		h.logger.Warn("Invalid change password request",
			zap.Error(err),
			zap.String("user_id", id.Hex()),
			zap.String("ip", c.ClientIP()),
		)
		middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info("Processing change password request",
		zap.String("user_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	// Change password
	response, err := h.service.ChangePassword(c.Request.Context(), id, &req)
	if err != nil {
		switch err {
		case userService.ErrInvalidCredentials:
			h.logger.Warn("Change password failed - invalid current password",
				zap.String("user_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "Current password is incorrect"})
		case userService.ErrSamePassword:
			h.logger.Warn("Change password failed - new password same as current",
				zap.String("user_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusBadRequest, gin.H{"error": "New password cannot be the same as the current password"})
		default:
			h.logger.Error("Change password failed",
				zap.Error(err),
				zap.String("user_id", id.Hex()),
				zap.String("ip", c.ClientIP()),
			)
			middleware.WriteJSONResponse(c, http.StatusInternalServerError, gin.H{"error": "Failed to change your password. Please try again later."})
		}
		return
	}

	h.logger.Info("Password change successful",
		zap.String("user_id", id.Hex()),
		zap.String("ip", c.ClientIP()),
	)

	middleware.WriteJSONResponse(c, http.StatusOK, gin.H{
		"success": response.Success,
		"message": response.Message,
	})
}

// ListTopPerformingAgents handles the request to list top performing agents
// @Summary List top performing agents
// @Description Get a list of top performing agents (subscribed users with at least one property listed, ordered by total properties in descending order)
// @Tags User
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} models.PaginatedResponse "List of top performing agents"
// @Failure 400 {object} map[string]string "Invalid input"
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/top-agents [get]
func (h *Handler) ListTopPerformingAgents(c *gin.Context) {
	// Parse pagination parameters
	pageStr := c.DefaultQuery("page", "1")
	limitStr := c.DefaultQuery("limit", "10")

	page, err := strconv.ParseInt(pageStr, 10, 64)
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil || limit < 1 {
		limit = 10
	}

	h.logger.Info("Processing top performing agents list request",
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.String("ip", c.ClientIP()),
	)

	// Get top performing agents from service
	agents, total, err := h.service.ListTopPerformingAgents(c.Request.Context(), page, limit)
	if err != nil {
		h.logger.Error("Failed to retrieve top performing agents",
			zap.Int64("page", page),
			zap.Int64("limit", limit),
			zap.String("error", err.Error()),
			zap.String("ip", c.ClientIP()),
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	if totalPages == 0 {
		totalPages = 1
	}
	hasNext := page < int64(totalPages)
	hasPrevious := page > 1

	h.logger.Info("Top performing agents list retrieved successfully",
		zap.Int64("total", total),
		zap.Int64("page", page),
		zap.Int64("limit", limit),
		zap.Int("count", len(agents)),
		zap.String("ip", c.ClientIP()),
	)

	// Create paginated response
	response := models.PaginatedResponse{
		Data: agents,
		Pagination: models.Pagination{
			Total:       total,
			Page:        int(page),
			Limit:       int(limit),
			TotalPages:  totalPages,
			HasNext:     hasNext,
			HasPrevious: hasPrevious,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetUserDetails handles the user details request
// @Summary Get user details
// @Description Get complete user details including all profile information, location, and status fields
// @Tags user
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.User
// @Failure 401 {object} map[string]string "Unauthorized"
// @Failure 500 {object} map[string]string "Server error"
// @Router /api/v1/user/details [get]
func (h *Handler) GetUserDetails(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warn("User ID not found in context")
		middleware.WriteJSONResponse(c, http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	// Use the ObjectID directly
	id := userID.(primitive.ObjectID)

	h.logger.Info("Processing user details request", zap.String("user_id", id.Hex()))

	// Get user details
	details, err := h.service.GetUserDetails(c.Request.Context(), id)
	if err != nil {
		h.logger.Error("Failed to get user details",
			zap.Error(err),
			zap.String("user_id", id.Hex()))
		middleware.WriteJSONResponse(c, http.StatusInternalServerError,
			gin.H{"error": "Failed to retrieve user details. Please try again later."})
		return
	}

	h.logger.Info("User details retrieved successfully", zap.String("user_id", id.Hex()))

	middleware.WriteJSONResponse(c, http.StatusOK, details)
}
