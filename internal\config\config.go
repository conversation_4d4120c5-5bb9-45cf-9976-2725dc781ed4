package config

import (
	"sync"

	"github.com/spf13/viper"
)

type Config struct {
	Server  ServerConfig
	MongoDB MongoDBConfig
	JWT     JWTConfig
	Redis   RedisConfig
}

type ServerConfig struct {
	Port         string
	Environment  string
	ReadTimeout  int
	WriteTimeout int
	IdleTimeout  int
}

type MongoDBConfig struct {
	URI      string
	Database string
}

type JWTConfig struct {
	SecretKey string
	Duration  int // in minutes
}

type RedisConfig struct {
	Address  string
	Password string
	DB       int
}

// Singleton instance
var (
	config *Config
	once   sync.Once
)

// GetConfig returns the singleton instance of Config
func GetConfig() *Config {
	once.Do(func() {
		var err error
		config, err = LoadConfig()
		if err != nil {
			panic(err)
		}
	})
	return config
}

func LoadConfig() (*Config, error) {
	// Load environment variables from .env file
	if err := LoadEnv(); err != nil {
		return nil, err
	}

	// Set up Viper
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Enable environment variable overrides
	viper.AutomaticEnv()

	// Map environment variables to config keys
	viper.BindEnv("server.port", "SERVER_PORT")
	viper.BindEnv("server.environment", "SERVER_ENVIRONMENT")
	viper.BindEnv("mongodb.uri", "MONGODB_URI")
	viper.BindEnv("mongodb.database", "MONGODB_DATABASE")
	viper.BindEnv("jwt.secret_key", "JWT_SECRET_KEY")
	viper.BindEnv("jwt.duration", "JWT_DURATION")
	viper.BindEnv("redis.address", "REDIS_ADDRESS")
	viper.BindEnv("redis.password", "REDIS_PASSWORD")
	viper.BindEnv("redis.db", "REDIS_DB")

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	// Unmarshal config
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}
