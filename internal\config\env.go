package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/joho/godotenv"
)

// LoadEnv loads environment variables from the appropriate .env file
func LoadEnv() error {
	// Get the current environment, default to development
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "development"
	}

	// Determine which .env file to load
	envFile := fmt.Sprintf(".env.%s", env)

	// Check if the file exists
	if _, err := os.Stat(envFile); os.IsNotExist(err) {
		// Try to find the file in the project root
		projectRoot, err := findProjectRoot()
		if err != nil {
			return fmt.Errorf("could not find project root: %w", err)
		}

		envFile = filepath.Join(projectRoot, envFile)

		// Check if the file exists in the project root
		if _, err := os.Stat(envFile); os.IsNotExist(err) {
			return fmt.Errorf("environment file %s not found", envFile)
		}
	}

	// Load the environment file
	if err := godotenv.Load(envFile); err != nil {
		return fmt.Errorf("error loading %s: %w", envFile, err)
	}

	return nil
}

// findProjectRoot attempts to find the project root directory
func findProjectRoot() (string, error) {
	// Start from the current directory
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// Look for go.mod file
	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}

		// Move up one directory
		parent := filepath.Dir(dir)
		if parent == dir {
			// We've reached the root of the filesystem
			return "", fmt.Errorf("could not find go.mod file")
		}
		dir = parent
	}
}
