package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Favorite represents a user's favorite property
type Favorite struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID     primitive.ObjectID `json:"userId" bson:"userId"`
	PropertyID primitive.ObjectID `json:"propertyId" bson:"propertyId"`
	CreatedAt  time.Time          `json:"createdAt" bson:"createdAt"`
}

// FavoriteRequest is used to add a property to favorites
type FavoriteRequest struct {
	PropertyID string `json:"propertyId" binding:"required"`
}

// FavoriteResponse is returned when a property is added to favorites
type FavoriteResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}
