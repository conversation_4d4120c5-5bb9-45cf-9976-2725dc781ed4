package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Notification represents a notification in the system
type Notification struct {
	ID          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	RecipientID primitive.ObjectID `json:"recipient_id" bson:"recipient_id"`
	SenderID    primitive.ObjectID `json:"sender_id,omitempty" bson:"sender_id,omitempty"`
	Type        string             `json:"type" bson:"type"` // property_created, property_verified, property_rejected, user_blocked, user_unblocked
	Title       string             `json:"title" bson:"title"`
	Message     string             `json:"message" bson:"message"`
	RelatedID   primitive.ObjectID `json:"related_id,omitempty" bson:"related_id,omitempty"` // Property ID or User ID
	IsRead      bool               `json:"is_read" bson:"is_read"`
	CreatedAt   time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt   time.Time          `json:"updated_at" bson:"updated_at"`
}

// NotificationResponse represents the API response for notifications
type NotificationResponse struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"`
	Title     string    `json:"title"`
	Message   string    `json:"message"`
	IsRead    bool      `json:"is_read"`
	CreatedAt time.Time `json:"created_at"`
	RelatedID string    `json:"related_id,omitempty"`
}

// GetNotificationsRequest represents the request for getting notifications
type GetNotificationsRequest struct {
	Page       int  `json:"page" form:"page"`
	Limit      int  `json:"limit" form:"limit"`
	OnlyUnread bool `json:"only_unread" form:"only_unread"`
}

// GetNotificationsResponse represents the response for getting notifications
type GetNotificationsResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	TotalCount    int64                  `json:"total_count"`
	UnreadCount   int64                  `json:"unread_count"`
	CurrentPage   int                    `json:"current_page"`
	TotalPages    int                    `json:"total_pages"`
	HasNext       bool                   `json:"has_next"`
	HasPrevious   bool                   `json:"has_previous"`
}

// MarkAsReadRequest represents the request for marking notifications as read
type MarkAsReadRequest struct {
	NotificationIDs []string `json:"notification_ids,omitempty"`
}

// NotificationCountResponse represents the response for notification counts
type NotificationCountResponse struct {
	TotalCount  int64 `json:"total_count"`
	UnreadCount int64 `json:"unread_count"`
}

// Notification type constants
const (
	NotificationTypePropertyCreated   = "property_created"
	NotificationTypePropertyVerified  = "property_verified"
	NotificationTypePropertyRejected  = "property_rejected"
	NotificationTypeUserBlocked       = "user_blocked"
	NotificationTypeUserUnblocked     = "user_unblocked"
)
