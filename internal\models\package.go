package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Package represents a subscription package in the system
type Package struct {
	ID             primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	PackageId      int                `json:"package_id" bson:"package_id"`
	Email          string             `json:"email" bson:"email"`
	UserID         primitive.ObjectID `json:"user_id" bson:"user_id"`
	PlanDuration   string             `json:"plan_duration" bson:"plan_duration"` // e.g., "1 month", "3 months", "1 year"
	Description    string             `json:"description" bson:"description"`
	PerWeekPrice   string             `json:"per_week_price" bson:"per_week_price"`
	TotalPrice     string             `json:"total_price" bson:"total_price"`
	Discount       float64            `json:"discount" bson:"discount"`
	IsOffer        bool               `json:"is_offer" bson:"is_offer"`
	IsPopular      bool               `json:"is_popular" bson:"is_popular"`
	PackageType    interface{}        `json:"package_type" binding:"omitempty"`
	ProductPackage interface{}        `json:"product_package" bson:"product_package"` // Can store any type of data
	CreatedAt      time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt      time.Time          `json:"updated_at" bson:"updated_at"`
}

// CreatePackageRequest represents the request to create a new package
type CreatePackageRequest struct {
	PackageId      int                `json:"package_id" binding:"required"`
	Email          string             `json:"email" binding:"required,email"`
	UserID         primitive.ObjectID `json:"user_id" binding:"required"`
	PlanDuration   string             `json:"plan_duration" binding:"required"`
	Description    string             `json:"description" binding:"required"`
	PerWeekPrice   string             `json:"per_week_price" binding:"required"`
	TotalPrice     string             `json:"total_price" binding:"required"`
	Discount       float64            `json:"discount" binding:"omitempty"`
	IsOffer        bool               `json:"is_offer" binding:"omitempty"`
	IsPopular      bool               `json:"is_popular" binding:"omitempty"`
	PackageType    interface{}        `json:"package_type" binding:"omitempty"`
	ProductPackage interface{}        `json:"product_package" binding:"omitempty"`
}

// UpdatePackageRequest represents the request to update an existing package
type UpdatePackageRequest struct {
	PlanDuration string      `json:"plan_duration" binding:"omitempty"`
	Description  string      `json:"description" binding:"omitempty"`
	PerWeekPrice string      `json:"per_week_price" binding:"omitempty"`
	TotalPrice   string      `json:"total_price" binding:"omitempty"`
	Discount     float64     `json:"discount" binding:"omitempty"`
	IsOffer      bool        `json:"is_offer" binding:"omitempty"`
	IsPopular    bool        `json:"is_popular" binding:"omitempty"`
	PackageType  interface{} `json:"package_type" binding:"omitempty"`
	Package      interface{} `json:"product_package" binding:"omitempty"`
}
