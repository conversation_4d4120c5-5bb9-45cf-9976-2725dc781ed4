package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// PropertyFacility represents a facility with ID, name and image URL for properties
type PropertyFacility struct {
	FacilityID primitive.ObjectID `json:"facility_id" bson:"facility_id"`
	Name       string             `json:"name" bson:"name"`
	ImageURL   string             `json:"image_url" bson:"image_url"`
}

// LocationRequest is imported from user.go

type Property struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Title              string             `json:"title" bson:"title"`
	Description        string             `json:"description" bson:"description"`
	Type               string             `json:"type" bson:"type"`     // apartment, house, etc.
	Status             string             `json:"status" bson:"status"` // available, sold, rented
	Location           Location           `json:"location" bson:"location"`
	Images             []string           `json:"images" bson:"images"`
	Area               float64            `json:"area" bson:"area"`
	YearBuilt          int                `json:"year_built" bson:"year_built"`
	BHK                int                `json:"bhk" bson:"bhk"`
	Bedroom            int                `json:"bedroom" bson:"bedroom"`
	Bathroom           int                `json:"bathroom" bson:"bathroom"`
	NoOfParking        int                `json:"no_of_parking" bson:"no_of_parking"`
	Rating             float64            `json:"rating" bson:"rating"`
	OwnerID            primitive.ObjectID `json:"owner_id" bson:"owner_id"`
	OwnerName          string             `json:"owner_name" bson:"owner_name"`
	OwnerEmail         string             `json:"owner_email" bson:"owner_email"`
	OwnerPhoneNumber   string             `json:"owner_phone_number" bson:"owner_phone_number"`
	OwnerProfilePhoto  string             `json:"owner_profile_photo" bson:"owner_profile_photo"`
	Facilities         []PropertyFacility `json:"facilities" bson:"facilities"`
	Facing             string             `json:"facing_direction" bson:"facing_direction"`
	Furniture          string             `json:"furniture" bson:"furniture"`
	TotalPrice         float64            `json:"total_price" bson:"total_price"`
	TotalFloor         float64            `json:"total_floor" bson:"total_floor"`
	IsPropertyVerified bool               `json:"is_property_verified" bson:"is_property_verified"`
	IsPropertyActive   bool               `json:"is_property_active" bson:"is_property_active"`
	CreatedAt          time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt          time.Time          `json:"updated_at" bson:"updated_at"`
}

type CreatePropertyRequest struct {
	Title       string             `json:"title" binding:"required"`
	Description string             `json:"description" binding:"required"`
	Type        string             `json:"type" binding:"required,oneof=apartment house villa commercial land"`
	Status      string             `json:"status" binding:"required,oneof=rent rented sold sell"`
	Location    *LocationRequest   `json:"location" binding:"required"`
	Images      []string           `json:"images" binding:"omitempty"`
	Area        float64            `json:"area" binding:"required,gt=0"`
	YearBuilt   int                `json:"year_built" binding:"required"`
	BHK         int                `json:"bhk" binding:"required,gte=0"`
	Bedroom     int                `json:"bedroom" binding:"required,gte=0"`
	Bathroom    int                `json:"bathroom" binding:"required,gte=0"`
	NoOfParking int                `json:"no_of_parking" binding:"omitempty,gte=0"`
	OwnerName   string             `json:"owner_name" binding:"required"`
	Facilities  []PropertyFacility `json:"facilities" binding:"omitempty"`
	Facing      string             `json:"facing_direction" binding:"omitempty"`
	Furniture   string             `json:"furniture" binding:"omitempty,oneof=furnished semi-furnished unfurnished"`
	TotalPrice  float64            `json:"total_price" binding:"required,gt=0"`
	TotalFloor  float64            `json:"total_floor" binding:"omitempty,gte=0"`
}

type UpdatePropertyRequest struct {
	Title              string             `json:"title" binding:"omitempty"`
	Description        string             `json:"description" binding:"omitempty"`
	Type               string             `json:"type" binding:"omitempty,oneof=apartment house villa commercial land"`
	Status             string             `json:"status" binding:"omitempty,oneof=rent rented sold sell"`
	Location           *LocationRequest   `json:"location" binding:"omitempty"`
	Images             []string           `json:"images" binding:"omitempty"`
	Area               float64            `json:"area" binding:"omitempty,gt=0"`
	YearBuilt          int                `json:"year_built" binding:"omitempty"`
	BHK                int                `json:"bhk" binding:"omitempty,gte=0"`
	Bedroom            int                `json:"bedroom" binding:"omitempty,gte=0"`
	Bathroom           int                `json:"bathroom" binding:"omitempty,gte=0"`
	NoOfParking        int                `json:"no_of_parking" binding:"omitempty,gte=0"`
	OwnerName          string             `json:"owner_name" binding:"omitempty"`
	Facilities         []PropertyFacility `json:"facilities" binding:"omitempty"`
	Facing             string             `json:"facing_direction" binding:"omitempty"`
	Furniture          string             `json:"furniture" binding:"omitempty,oneof=furnished semi-furnished unfurnished"`
	TotalPrice         float64            `json:"total_price" binding:"omitempty,gt=0"`
	TotalFloor         float64            `json:"total_floor" binding:"omitempty,gte=0"`
	Rating             float64            `json:"rating" binding:"omitempty,gte=0,lte=5"`
	IsPropertyVerified bool               `json:"is_property_verified" binding:"omitempty"`
	IsPropertyActive   bool               `json:"is_property_active" binding:"omitempty"`
}

type PropertySearchRequest struct {
	City       string  `json:"city" form:"city"`
	Area       string  `json:"area" form:"area"`
	Type       string  `json:"type" form:"type"`
	Status     string  `json:"status" form:"status"`
	MinPrice   float64 `json:"min_price" form:"min_price"`
	MaxPrice   float64 `json:"max_price" form:"max_price"`
	BHK        int     `json:"bhk" form:"bhk"`
	Bedroom    int     `json:"bedroom" form:"bedroom"`
	Bathroom   int     `json:"bathroom" form:"bathroom"`
	Furniture  string  `json:"furniture" form:"furniture"`
	Facilities string  `json:"facilities" form:"facilities"` // Comma-separated list
	Page       int     `json:"page" form:"page" default:"1"`
	Limit      int     `json:"limit" form:"limit" default:"10"`
	SortBy     string  `json:"sort_by" form:"sort_by" default:"created_at"`
	SortOrder  string  `json:"sort_order" form:"sort_order" default:"desc"`
}

type PaginatedResponse struct {
	Data       interface{} `json:"data"`
	Pagination Pagination  `json:"pagination"`
}

type Pagination struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	Limit       int   `json:"limit"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}

// ComprehensiveSearchRequest represents the request for comprehensive property search
type ComprehensiveSearchRequest struct {
	PropertyStatus string `json:"property_status" binding:"required,oneof=rent sell rented sold"`
	PropertyType   string `json:"property_type" binding:"required"`
	City           string `json:"city" binding:"required"`
	Area           string `json:"area" binding:"required"`
	TrendingArea   string `json:"trending_area" binding:"omitempty"`
}

// TrendingArea represents area information for a city
type TrendingArea struct {
	Area string `json:"area" bson:"area"`
}

// RelatedProperty represents a simplified property response for related properties
type RelatedProperty struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Title      string             `json:"title" bson:"title"`
	Status     string             `json:"status" bson:"status"`
	Type       string             `json:"type" bson:"type"` // apartment, house, etc.
	Image      string             `json:"image" bson:"image"`
	Area       float64            `json:"area" bson:"area"`
	YearBuilt  int                `json:"year_built" bson:"year_built"`
	BHK        int                `json:"bhk" bson:"bhk"`
	Bedroom    int                `json:"bedroom" bson:"bedroom"`
	Bathroom   int                `json:"bathroom" bson:"bathroom"`
	Location   Location           `json:"location" bson:"location"`
	IsFavorite bool               `json:"is_favorite" bson:"-"`
	TotalPrice float64            `json:"total_price" bson:"total_price"` // Total price of the property
}

// PropertyDetailResponse represents the response for property detail with latest review
type PropertyDetailResponse struct {
	Property        Property              `json:"property"`
	Review          *ReviewWithLikeStatus `json:"review,omitempty"`           // Single latest review with like status, omitempty if no reviews
	IsFavorite      bool                  `json:"is_favorite"`                // Whether the property is in user's favorites
	RelatedProperty *RelatedProperty      `json:"related_property,omitempty"` // Related property from same area/city
}

// PropertyWithFavorite represents a property with favorite status
type PropertyWithFavorite struct {
	Property
	IsFavorite bool `json:"is_favorite" bson:"-"`
}

// ComprehensiveSearchResponse represents the response for comprehensive property search
type ComprehensiveSearchResponse struct {
	TrendingAreas   []string    `json:"trending_areas"`
	Properties      []*Property `json:"properties"`
	TopAgents       []User      `json:"top_agents"`
	PremiumListings []*Property `json:"premium_listings"`
}
