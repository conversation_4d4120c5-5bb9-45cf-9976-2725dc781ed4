package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Facility represents amenities or facilities available in properties
type Facility struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name      string             `json:"name" bson:"name"`
	IconURL   string             `json:"icon_url" bson:"icon_url"`
	Available bool               `json:"available" bson:"available"`
	Active    bool               `json:"active" bson:"active"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

// CreateFacilityRequest is used to create a new facility (icon handled separately via multipart upload)
type CreateFacilityRequest struct {
	Name      string `json:"name" binding:"required"`
	Available bool   `json:"available" binding:"omitempty"`
	Active    bool   `json:"active" binding:"omitempty"`
}

// UpdateFacilityRequest is used to update an existing facility (icon handled separately via multipart upload)
type UpdateFacilityRequest struct {
	Name      string `json:"name" binding:"omitempty"`
	Available bool   `json:"available" binding:"omitempty"`
	Active    bool   `json:"active" binding:"omitempty"`
}

// FacilitiesResponse represents a simplified facility response with only essential fields
type FacilitiesResponse struct {
	ID      primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name    string             `json:"name" bson:"name"`
	IconURL string             `json:"icon_url" bson:"icon_url"`
}

// PropertyType represents different types of properties
type PropertyType struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name      string             `json:"name" bson:"name"`
	Order     int                `json:"order" bson:"order"`
	ImageURL  string             `json:"image_url" bson:"image_url"`
	Active    bool               `json:"active" bson:"active"`
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt time.Time          `json:"updated_at" bson:"updated_at"`
}

// CreatePropertyTypeRequest is used to create a new property type (image handled separately via multipart upload)
type CreatePropertyTypeRequest struct {
	Name   string `json:"name" binding:"required"`
	Order  int    `json:"order" binding:"required"`
	Active *bool  `json:"active" binding:"omitempty"`
}

// UpdatePropertyTypeRequest is used to update an existing property type (image handled separately via multipart upload)
type UpdatePropertyTypeRequest struct {
	Name   string `json:"name" binding:"omitempty"`
	Order  int    `json:"order" binding:"omitempty"`
	Active *bool  `json:"active" binding:"omitempty"`
}
