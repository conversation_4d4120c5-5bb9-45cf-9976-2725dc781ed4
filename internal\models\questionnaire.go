package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ValidationRules for answer validation
type ValidationRules struct {
	MinValue       *float64 `json:"min_value,omitempty" bson:"min_value,omitempty"`
	MaxValue       *float64 `json:"max_value,omitempty" bson:"max_value,omitempty"`
	MinLength      *int     `json:"min_length,omitempty" bson:"min_length,omitempty"`
	MaxLength      *int     `json:"max_length,omitempty" bson:"max_length,omitempty"`
	Pattern        string   `json:"pattern,omitempty" bson:"pattern,omitempty"`
	ForbiddenWords []string `json:"forbidden_words,omitempty" bson:"forbidden_words,omitempty"`
}

// QuestionOption represents answer options for questions
type QuestionOption struct {
	ID    string `json:"id" bson:"id"`
	Text  string `json:"text" bson:"text"`
	Value string `json:"value" bson:"value"`
}

// Question represents a questionnaire question
type Question struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Text       string             `json:"text" bson:"text"`
	Type       string             `json:"type" bson:"type"` // single_choice, multiple_choice, range, text
	Order      int                `json:"order" bson:"order"`
	IsRequired bool               `json:"is_required" bson:"is_required"`
	Options    []QuestionOption   `json:"options" bson:"options"`
	Validation ValidationRules    `json:"validation" bson:"validation"`
	IsActive   bool               `json:"is_active" bson:"is_active"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt  time.Time          `json:"updated_at" bson:"updated_at"`
}

// UserResponse represents a user's answer to a question
type UserResponse struct {
	QuestionID primitive.ObjectID `json:"question_id" bson:"question_id"`
	Answer     interface{}        `json:"answer" bson:"answer"`
	AnsweredAt time.Time          `json:"answered_at" bson:"answered_at"`
}

// QuestionnaireSession represents a user's questionnaire session
type QuestionnaireSession struct {
	ID                primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID            primitive.ObjectID `json:"user_id" bson:"user_id"`
	CurrentQuestion   int                `json:"current_question" bson:"current_question"`
	Status            string             `json:"status" bson:"status"` // active, completed, terminated
	Responses         []UserResponse     `json:"responses" bson:"responses"`
	StartedAt         time.Time          `json:"started_at" bson:"started_at"`
	CompletedAt       *time.Time         `json:"completed_at,omitempty" bson:"completed_at,omitempty"`
	TerminatedAt      *time.Time         `json:"terminated_at,omitempty" bson:"terminated_at,omitempty"`
	TerminationReason string             `json:"termination_reason,omitempty" bson:"termination_reason,omitempty"`
	LastActivity      time.Time          `json:"last_activity" bson:"last_activity"`
}

// RecommendationResult stores generated property recommendations
type RecommendationResult struct {
	ID         primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	SessionID  primitive.ObjectID     `json:"session_id" bson:"session_id"`
	UserID     primitive.ObjectID     `json:"user_id" bson:"user_id"`
	Properties []primitive.ObjectID   `json:"properties" bson:"properties"`
	Score      float64                `json:"score" bson:"score"`
	Criteria   map[string]interface{} `json:"criteria" bson:"criteria"`
	CreatedAt  time.Time              `json:"created_at" bson:"created_at"`
}

// Request/Response DTOs

// StartQuestionnaireRequest represents the request to start a questionnaire
type StartQuestionnaireRequest struct {
	// No fields needed - user ID comes from JWT token
}

// StartQuestionnaireResponse represents the response when starting a questionnaire
type StartQuestionnaireResponse struct {
	SessionID       primitive.ObjectID `json:"session_id"`
	Status          string             `json:"status"`
	CurrentQuestion int                `json:"current_question"`
	TotalQuestions  int                `json:"total_questions"`
	StartedAt       time.Time          `json:"started_at"`
}

// QuestionProgress represents progress information
type QuestionProgress struct {
	Current        int     `json:"current"`
	Total          int     `json:"total"`
	Percentage     float64 `json:"percentage"`
	IsLastQuestion bool    `json:"is_last_question"`
	NextAction     string  `json:"next_action"` // "answer_current_question" or "complete_questionnaire"
}

// SessionInfo represents session information
type SessionInfo struct {
	ID              primitive.ObjectID `json:"id"`
	Status          string             `json:"status"`
	CurrentQuestion int                `json:"current_question"`
	TotalQuestions  int                `json:"total_questions"`
	ResponsesCount  int                `json:"responses_count"`
}

// CurrentQuestionResponse represents the response for getting current question
type CurrentQuestionResponse struct {
	Question Question         `json:"question"`
	Session  SessionInfo      `json:"session"`
	Progress QuestionProgress `json:"progress"`
}

// SubmitAnswerRequest represents the request to submit an answer
type SubmitAnswerRequest struct {
	QuestionID primitive.ObjectID `json:"question_id" binding:"required"`
	Answer     interface{}        `json:"answer" binding:"required"`
}

// SubmitAnswerResponse represents the response after submitting an answer
type SubmitAnswerResponse struct {
	AnswerAccepted         bool   `json:"answer_accepted"`
	NextQuestionAvailable  bool   `json:"next_question_available"`
	QuestionnaireCompleted bool   `json:"questionnaire_completed"`
	CurrentQuestion        int    `json:"current_question"`
	TotalQuestions         int    `json:"total_questions"`
	CompletionStatus       string `json:"completion_status,omitempty"`
}

// CompleteQuestionnaireResponse represents the response when completing questionnaire
type CompleteQuestionnaireResponse struct {
	SessionID        primitive.ObjectID `json:"session_id"`
	Status           string             `json:"status"`
	CompletedAt      time.Time          `json:"completed_at"`
	RecommendationID primitive.ObjectID `json:"recommendation_id"`
	TotalResponses   int                `json:"total_responses"`
}

// UserPreferences represents analyzed user preferences
type UserPreferences struct {
	BudgetRange  string   `json:"budget_range"`
	PropertyType string   `json:"property_type"`
	City         string   `json:"city"`
	Area         string   `json:"area"`
	BHK          string   `json:"bhk"`
	Amenities    []string `json:"amenities"`
	Status       string   `json:"status"`
	Furnishing   string   `json:"furnishing"`
}

// PropertyRecommendation represents a recommended property with match score
type PropertyRecommendation struct {
	Property
	MatchScore int `json:"match_score"`
}

// RecommendationsResponse represents the response for property recommendations
type RecommendationsResponse struct {
	RecommendationID primitive.ObjectID       `json:"recommendation_id"`
	UserPreferences  UserPreferences          `json:"user_preferences"`
	Properties       []PropertyRecommendation `json:"properties"`
	TotalMatches     int                      `json:"total_matches"`
	Showing          int                      `json:"showing"`
	FiltersApplied   map[string]string        `json:"filters_applied"`
	GeneratedAt      time.Time                `json:"generated_at"`
}

// CreateQuestionRequest represents the request to create a new question (Admin)
type CreateQuestionRequest struct {
	Text       string           `json:"text" binding:"required"`
	Type       string           `json:"type" binding:"required,oneof=single_choice multiple_choice range text"`
	Order      int              `json:"order" binding:"required,min=1"`
	IsRequired bool             `json:"is_required"`
	Options    []QuestionOption `json:"options" binding:"omitempty"`
	Validation ValidationRules  `json:"validation" binding:"omitempty"`
}

// UpdateQuestionRequest represents the request to update a question (Admin)
type UpdateQuestionRequest struct {
	Text       string           `json:"text" binding:"omitempty"`
	Type       string           `json:"type" binding:"omitempty,oneof=single_choice multiple_choice range text"`
	Order      int              `json:"order" binding:"omitempty,min=1"`
	IsRequired *bool            `json:"is_required" binding:"omitempty"`
	Options    []QuestionOption `json:"options" binding:"omitempty"`
	Validation ValidationRules  `json:"validation" binding:"omitempty"`
	IsActive   *bool            `json:"is_active" binding:"omitempty"`
}

// SessionStatusResponse represents the response for session status check
type SessionStatusResponse struct {
	SessionID          primitive.ObjectID `json:"session_id"`
	Status             string             `json:"status"`
	CurrentQuestion    int                `json:"current_question"`
	TotalQuestions     int                `json:"total_questions"`
	ResponsesCount     int                `json:"responses_count"`
	RemainingQuestions int                `json:"remaining_questions"`
	IsComplete         bool               `json:"is_complete"`
	NextAction         string             `json:"next_action"`
	LastActivity       time.Time          `json:"last_activity"`
}
