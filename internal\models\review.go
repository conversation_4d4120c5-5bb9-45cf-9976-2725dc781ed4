package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Review struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID     primitive.ObjectID `json:"user_id" bson:"user_id"`
	PropertyID primitive.ObjectID `json:"property_id" bson:"property_id"`
	UserName   string             `json:"user_name" bson:"user_name"`
	Rating     float64            `json:"rating" bson:"rating"`
	Comment    string             `json:"comment" bson:"comment"`
	Likes      int                `json:"likes" bson:"likes"`
	DisLikes   int                `json:"dislikes" bson:"dislikes"`
	Photos     []string           `json:"photos" bson:"photos"`
	IsActive   bool               `json:"is_active" bson:"is_active"`
	CreatedAt  time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt  time.Time          `json:"updated_at" bson:"updated_at"`
}

// ReviewLike represents a like or dislike on a review
type ReviewLike struct {
	ID        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	ReviewID  primitive.ObjectID `json:"review_id" bson:"review_id"`
	UserID    primitive.ObjectID `json:"user_id" bson:"user_id"`
	Type      string             `json:"type" bson:"type"` // "like" or "dislike"
	CreatedAt time.Time          `json:"created_at" bson:"created_at"`
}

// CreateReviewRequest is used to create a new review (images handled separately via multipart upload)
type CreateReviewRequest struct {
	PropertyID string  `json:"property_id" binding:"required"`
	Rating     float64 `json:"rating" binding:"required,min=1,max=5"`
	Comment    string  `json:"comment" binding:"required"`
}

// UpdateReviewRequest is used to update an existing review (images handled separately via multipart upload)
type UpdateReviewRequest struct {
	Rating  float64 `json:"rating" binding:"omitempty,min=1,max=5"`
	Comment string  `json:"comment" binding:"omitempty"`
}

// ReviewResponse is returned for review operations
type ReviewResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Review  Review `json:"review,omitempty"`
}

// ReviewWithLikeStatus represents a review with user's like status
type ReviewWithLikeStatus struct {
	ID               primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	UserID           primitive.ObjectID `json:"user_id" bson:"user_id"`
	PropertyID       primitive.ObjectID `json:"property_id" bson:"property_id"`
	UserName         string             `json:"user_name" bson:"user_name"`
	Rating           float64            `json:"rating" bson:"rating"`
	Comment          string             `json:"comment" bson:"comment"`
	Likes            int                `json:"likes" bson:"likes"`
	DisLikes         int                `json:"dislikes" bson:"dislikes"`
	Photos           []string           `json:"photos" bson:"photos"`
	IsActive         bool               `json:"is_active" bson:"is_active"`
	CreatedAt        time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt        time.Time          `json:"updated_at" bson:"updated_at"`
	IsLiked          bool               `json:"is_liked" bson:"is_liked"`
	IsDisliked       bool               `json:"is_disliked" bson:"is_disliked"`
	UserProfilePhoto string             `json:"user_profile_photo" bson:"user_profile_photo"`
}

// ReviewListResponse is returned for listing reviews
type ReviewListResponse struct {
	Data       []Review   `json:"data"`
	Pagination Pagination `json:"pagination"`
}

// ReviewListWithLikeStatusResponse is returned for listing reviews with like status
type ReviewListWithLikeStatusResponse struct {
	Data       []ReviewWithLikeStatus `json:"data"`
	Pagination Pagination             `json:"pagination"`
}
