package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TopLocation represents popular locations in the system
type TopLocation struct {
	ID            primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Area          string             `json:"area" bson:"area"`
	City          string             `json:"city" bson:"city"`
	State         string             `json:"state" bson:"state"`
	PropertyCount int                `json:"property_count" bson:"property_count"`
	CreatedAt     time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at" bson:"updated_at"`
}

// CreateTopLocationRequest is used to create a new top location
type CreateTopLocationRequest struct {
	Area  string `json:"area" binding:"required"`
	City  string `json:"city" binding:"required"`
	State string `json:"state" binding:"required"`
}

// UpdateTopLocationRequest is used to update an existing top location
type UpdateTopLocationRequest struct {
	Area          string `json:"area" binding:"omitempty"`
	City          string `json:"city" binding:"omitempty"`
	State         string `json:"state" binding:"omitempty"`
	PropertyCount int    `json:"property_count" binding:"omitempty"`
}
