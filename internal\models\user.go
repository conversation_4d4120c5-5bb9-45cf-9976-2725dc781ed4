package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Location struct {
	Address      string  `json:"address" bson:"address"`
	City         string  `json:"city" bson:"city"`
	Area         string  `json:"area" bson:"area"`
	State        string  `json:"state" bson:"state"`
	Country      string  `json:"country" bson:"country"`
	ZipCode      string  `json:"zip_code" bson:"zip_code"`
	Latitude     float64 `json:"latitude" bson:"latitude"`
	Longitude    float64 `json:"longitude" bson:"longitude"`
	Neighborhood string  `json:"neighborhood" bson:"neighborhood"`
}

type User struct {
	ID                     primitive.ObjectID     `json:"id" bson:"_id,omitempty"`
	Email                  string                 `json:"email" bson:"email"`
	FirstName              string                 `json:"first_name" bson:"first_name"`
	LastName               string                 `json:"last_name" bson:"last_name"`
	Password               string                 `json:"-" bson:"password"` // "-" means this field won't be included in JSON
	Role                   string                 `json:"role" bson:"role"`  // admin, agent, user
	PhoneNumber            string                 `json:"phn_number" bson:"phn_number"`
	BirthDate              time.Time              `json:"birth_date" bson:"birth_date"`
	Location               Location               `json:"location" bson:"location"`
	SignupVerificationCode string                 `json:"signup_verification_code" bson:"signup_verification_code"`
	PasswordResetToken     string                 `json:"password_reset_token,omitempty" bson:"password_reset_token"`
	PasswordResetExpiry    time.Time              `json:"password_reset_expiry,omitempty" bson:"password_reset_expiry"`
	ProfilePhoto           string                 `json:"profile_photo" bson:"profile_photo"`
	Metadata               map[string]interface{} `json:"metadata" bson:"metadata"`
	IsProfileComplete      bool                   `json:"is_profile_complete" bson:"is_profile_complete"`
	IsSubscribedUser       bool                   `json:"is_subscribed_user" bson:"is_subscribed_user"`
	IsBlocked              bool                   `json:"is_blocked" bson:"is_blocked"`
	TotalListedProperties  int                    `json:"total_listed_properties" bson:"total_listed_properties"`
	IsDocumentVerified     bool                   `json:"is_document_verified" bson:"is_document_verified"`
	CreatedAt              time.Time              `json:"created_at" bson:"created_at"`
	UpdatedAt              time.Time              `json:"updated_at" bson:"updated_at"`
}

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,password"`
}

type RegisterRequest struct {
	FirstName   string `json:"first_name" binding:"required"`
	LastName    string `json:"last_name" binding:"required"`
	Email       string `json:"email" binding:"required,email"`
	BirthDate   string `json:"birth_date" binding:"required"`
	PhoneNumber string `json:"phn_number" binding:"required"`
	Password    string `json:"password" binding:"required,password"`
	Role        string `json:"role" binding:"omitempty"`
	// Location     *LocationRequest       `json:"location" binding:"omitempty"`
	// ProfilePhoto string                 `json:"profile_photo" binding:"omitempty"`
	// Metadata     map[string]interface{} `json:"metadata" binding:"omitempty"`
}

type LocationRequest struct {
	Address      string  `json:"address" binding:"omitempty"`
	City         string  `json:"city" binding:"omitempty"`
	Area         string  `json:"area" binding:"omitempty"`
	State        string  `json:"state" binding:"omitempty"`
	Country      string  `json:"country" binding:"omitempty"`
	ZipCode      string  `json:"zip_code" binding:"omitempty"`
	Latitude     float64 `json:"latitude" binding:"omitempty"`
	Longitude    float64 `json:"longitude" binding:"omitempty"`
	Neighborhood string  `json:"neighborhood" binding:"omitempty"`
}

type UpdateUserRequest struct {
	FirstName         string                 `json:"first_name" binding:"omitempty"`
	LastName          string                 `json:"last_name" binding:"omitempty"`
	PhoneNumber       string                 `json:"phn_number" binding:"omitempty"`
	BirthDate         string                 `json:"birth_date" binding:"omitempty"`
	Location          *LocationRequest       `json:"location" binding:"omitempty"`
	ProfilePhoto      string                 `json:"profile_photo" binding:"omitempty"`
	Metadata          map[string]interface{} `json:"metadata" binding:"omitempty"`
	IsProfileComplete bool                   `json:"is_profile_complete" binding:"omitempty"`
}

type AuthResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}

type SendOTPRequest struct {
	Email       string `json:"email" binding:"required,email"`
	FirstName   string `json:"first_name" binding:"required"`
	LastName    string `json:"last_name" binding:"required"`
	BirthDate   string `json:"birth_date" binding:"required"`
	PhoneNumber string `json:"phn_number" binding:"required"`
	Password    string `json:"password" binding:"required,password"`
	Role        string `json:"role" binding:"omitempty"`
}

type VerifyOTPRequest struct {
	Email string `json:"email" binding:"required,email"`
	OTP   string `json:"otp" binding:"required"`
}

type VerifyOTPResponse struct {
	Message string `json:"message"`
	Token   string `json:"token"`
	User    User   `json:"user"`
}

type ResendOTPRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type OTPResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ForgotPasswordRequest represents a request to reset password
type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ForgotPasswordResponse represents a response to a forgot password request
type ForgotPasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ResetPasswordRequest represents a request to set a new password
type ResetPasswordRequest struct {
	Email       string `json:"email" binding:"required,email"`
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,password"`
}

// ResetPasswordResponse represents a response to a reset password request
type ResetPasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ChangePasswordRequest represents a request to change the current password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,password"`
}

// ChangePasswordResponse represents a response to a change password request
type ChangePasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// CompleteProfileRequest represents the request to complete a user's profile
type CompleteProfileRequest struct {
	PhoneNumber string `json:"phn_number" binding:"required"`
	Address     string `json:"address" binding:"required"`
	City        string `json:"city" binding:"required"`
	State       string `json:"state" binding:"required"`
	Country     string `json:"country" binding:"required"`
	ZipCode     string `json:"zip_code" binding:"required"`
}

// UserDetailsResponse represents the response for user details endpoint
type UserDetailsResponse struct {
	UserID       primitive.ObjectID `json:"user_id"`
	FirstName    string             `json:"first_name"`
	LastName     string             `json:"last_name"`
	ProfileImage string             `json:"profile_image"`
	PhoneNumber  string             `json:"phone_number"`
	Address      string             `json:"address"`
	City         string             `json:"city"`
	State        string             `json:"state"`
	Country      string             `json:"country"`
	PostalCode   string             `json:"postal_code"`
}
