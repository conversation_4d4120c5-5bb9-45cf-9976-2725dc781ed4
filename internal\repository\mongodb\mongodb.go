package mongodb

import (
	"context"
	"time"

	"realestate-platform/internal/config"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoDBClient struct {
	client   *mongo.Client
	database *mongo.Database
}

func NewMongoDBClient(cfg config.MongoDBConfig) (*MongoDBClient, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	clientOptions := options.Client().ApplyURI(cfg.URI)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, err
	}

	// Ping the database
	if err := client.Ping(ctx, nil); err != nil {
		return nil, err
	}

	return &MongoDBClient{
		client:   client,
		database: client.Database(cfg.Database),
	}, nil
}

func (m *MongoDBClient) GetCollection(name string) *mongo.Collection {
	return m.database.Collection(name)
}

func (m *MongoDBClient) Disconnect(ctx context.Context) error {
	return m.client.Disconnect(ctx)
}

// Helper function to create indexes
func (m *MongoDBClient) CreateIndexes(ctx context.Context, collectionName string, indexes []mongo.IndexModel) error {
	collection := m.GetCollection(collectionName)
	_, err := collection.Indexes().CreateMany(ctx, indexes)
	return err
}
