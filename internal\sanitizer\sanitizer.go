package sanitizer

import (
	"fmt"
	"regexp"
	"strings"
)

var (
	// HTML tags regex
	htmlRegex = regexp.MustCompile(`<[^>]*>`)
	// SQL injection patterns - updated to better detect the specific case
	sqlInjectionRegex = regexp.MustCompile(`(?i)(select\s+|insert\s+into|update\s+|delete\s+from|drop\s+table|union\s+select|where\s+|;\s*--|xp_\w+|'\s+OR\s+'1'\s*=\s*'1)`)
	// XSS patterns
	xssRegex = regexp.MustCompile(`(?i)(javascript:|data:|vbscript:|expression\(|onload=)`)
)

// SanitizationResult represents the result of a sanitization operation
type SanitizationResult struct {
	SanitizedValue string
	IsValid        bool
	Feedback       string
	OriginalValue  string
}

// EmailSanitizationResult represents the result of email sanitization
type EmailSanitizationResult struct {
	Email    string
	IsValid  bool
	Message  string
	Original string
}

// SanitizeFieldWithFeedback is a generic function to sanitize any field with feedback
func SanitizeFieldWithFeedback(fieldName, input string) SanitizationResult {
	result := SanitizationResult{
		OriginalValue: input,
		IsValid:       true,
	}

	// Check if input is empty
	if input == "" {
		result.IsValid = false
		result.Feedback = fmt.Sprintf("%s: cannot be empty", fieldName)
		return result
	}

	// Remove HTML tags
	hasHTML := htmlRegex.MatchString(input)
	if hasHTML {
		result.IsValid = false
		result.Feedback = fmt.Sprintf("%s: removed HTML tags", fieldName)
		input = htmlRegex.ReplaceAllString(input, "")
	}

	// Remove SQL injection patterns
	hasSQL := sqlInjectionRegex.MatchString(input)
	if hasSQL {
		result.IsValid = false
		if result.Feedback != "" {
			result.Feedback += fmt.Sprintf(", removed SQL injection patterns from %s", fieldName)
		} else {
			result.Feedback = fmt.Sprintf("%s: removed SQL injection patterns", fieldName)
		}
		input = sqlInjectionRegex.ReplaceAllString(input, "")
	}

	// Remove XSS patterns
	hasXSS := xssRegex.MatchString(input)
	if hasXSS {
		result.IsValid = false
		if result.Feedback != "" {
			result.Feedback += fmt.Sprintf(", removed XSS patterns from %s", fieldName)
		} else {
			result.Feedback = fmt.Sprintf("%s: removed XSS patterns", fieldName)
		}
		input = xssRegex.ReplaceAllString(input, "")
	}

	// Trim whitespace
	trimmed := strings.TrimSpace(input)
	if trimmed != input {
		result.IsValid = false
		if result.Feedback != "" {
			result.Feedback += fmt.Sprintf(", trimmed whitespace from %s", fieldName)
		} else {
			result.Feedback = fmt.Sprintf("%s: trimmed whitespace", fieldName)
		}
		input = trimmed
	}

	result.SanitizedValue = input
	return result
}

// SanitizeStringWithFeedback sanitizes a string and provides feedback
func SanitizeStringWithFeedback(input string) SanitizationResult {
	return SanitizeFieldWithFeedback("Input", input)
}

// SanitizeEmail sanitizes email addresses
func SanitizeEmail(email string) string {
	if email == "" {
		return email
	}

	// Convert to lowercase
	email = strings.ToLower(email)

	// Remove whitespace
	email = strings.TrimSpace(email)

	// Basic email validation
	if !strings.Contains(email, "@") || !strings.Contains(email, ".") {
		return ""
	}

	return email
}

// SanitizePasswordWithFeedback sanitizes a password and provides feedback
func SanitizePasswordWithFeedback(password string) SanitizationResult {
	result := SanitizationResult{
		OriginalValue: password,
		IsValid:       true,
	}

	// Check if password is empty
	if password == "" {
		result.IsValid = false
		result.Feedback = "Password: cannot be empty"
		return result
	}

	// Remove HTML tags
	hasHTML := htmlRegex.MatchString(password)
	if hasHTML {
		result.IsValid = false
		result.Feedback = "Password: removed HTML tags"
		password = htmlRegex.ReplaceAllString(password, "")
	}

	// Trim whitespace
	trimmed := strings.TrimSpace(password)
	if trimmed != password {
		result.IsValid = false
		if result.Feedback != "" {
			result.Feedback += ", whitespace was removed from password"
		} else {
			result.Feedback = "Password: whitespace was removed"
		}
		password = trimmed
	}

	result.SanitizedValue = password
	return result
}

// SanitizeMapWithFeedback sanitizes all string values in a map and provides feedback
func SanitizeMapWithFeedback(data map[string]interface{}) (map[string]interface{}, []string) {
	sanitized := make(map[string]interface{})
	messages := make([]string, 0)

	for key, value := range data {
		switch v := value.(type) {
		case string:
			// Use the generic field sanitization function
			result := SanitizeFieldWithFeedback(key, v)
			sanitized[key] = result.SanitizedValue
			if !result.IsValid {
				messages = append(messages, result.Feedback)
			}
		case map[string]interface{}:
			subSanitized, subMessages := SanitizeMapWithFeedback(v)
			sanitized[key] = subSanitized
			messages = append(messages, subMessages...)
		default:
			sanitized[key] = value
		}
	}

	return sanitized, messages
}

// SanitizeEmailWithFeedback sanitizes an email address and provides feedback
func SanitizeEmailWithFeedback(email string) SanitizationResult {
	result := SanitizationResult{
		OriginalValue: email,
		IsValid:       true,
	}

	// Check if email is empty
	if email == "" {
		result.IsValid = false
		result.Feedback = "Email: cannot be empty"
		return result
	}

	// Check if email contains @ symbol
	if !strings.Contains(email, "@") {
		result.IsValid = false
		result.Feedback = "Email: must contain @ symbol"
		return result
	}

	// Check if email has a valid domain
	parts := strings.Split(email, "@")
	if len(parts) != 2 || parts[1] == "" {
		result.IsValid = false
		result.Feedback = "Email: must have a valid domain"
		return result
	}

	// Convert to lowercase
	lowercase := strings.ToLower(email)
	if lowercase != email {
		result.IsValid = false
		result.Feedback = "Email: converted to lowercase: " + lowercase
		email = lowercase
	}

	// Trim whitespace
	trimmed := strings.TrimSpace(email)
	if trimmed != email {
		result.IsValid = false
		if result.Feedback != "" {
			result.Feedback += ", whitespace was removed from email"
		} else {
			result.Feedback = "Email: whitespace was removed"
		}
		email = trimmed
	}

	result.SanitizedValue = email
	return result
}
