package favorite

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

var (
	ErrFavoriteExists   = errors.New("property already in favorites")
	ErrFavoriteNotFound = errors.New("favorite not found")
	ErrPropertyNotFound = errors.New("property not found")
)

type Service struct {
	db *mongodb.MongoDBClient
}

func NewService(db *mongodb.MongoDBClient) *Service {
	return &Service{db: db}
}

// AddFavorite adds a property to a user's favorites
func (s *Service) AddFavorite(ctx context.Context, userID, propertyID primitive.ObjectID) error {
	// Check if property exists
	propertyCollection := s.db.GetCollection("properties")
	var property models.Property
	err := propertyCollection.FindOne(ctx, bson.M{"_id": propertyID}).Decode(&property)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrPropertyNotFound
		}
		return err
	}

	// Check if already favorited
	favoriteCollection := s.db.GetCollection("favorites")
	count, err := favoriteCollection.CountDocuments(ctx, bson.M{
		"userId":     userID,
		"propertyId": propertyID,
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrFavoriteExists
	}

	// Add to favorites
	favorite := models.Favorite{
		UserID:     userID,
		PropertyID: propertyID,
		CreatedAt:  time.Now(),
	}

	_, err = favoriteCollection.InsertOne(ctx, favorite)
	return err
}

// RemoveFavorite removes a property from a user's favorites
func (s *Service) RemoveFavorite(ctx context.Context, userID, propertyID primitive.ObjectID) error {
	favoriteCollection := s.db.GetCollection("favorites")

	result, err := favoriteCollection.DeleteOne(ctx, bson.M{
		"userId":     userID,
		"propertyId": propertyID,
	})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return ErrFavoriteNotFound
	}

	return nil
}

// ListFavoritesByUserID retrieves all favorites for a user with filtering
func (s *Service) ListFavoritesByUserID(ctx context.Context, userID primitive.ObjectID, page, limit int64, filters bson.M) ([]models.Property, int64, error) {
	favoriteCollection := s.db.GetCollection("favorites")
	propertyCollection := s.db.GetCollection("properties")

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Get total count of favorites for this user
	total, err := favoriteCollection.CountDocuments(ctx, bson.M{"userId": userID})
	if err != nil {
		return nil, 0, err
	}

	// Find all favorite property IDs for this user with pagination
	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"createdAt": -1}) // Sort by creation date, newest first

	cursor, err := favoriteCollection.Find(ctx, bson.M{"userId": userID}, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Extract property IDs from favorites
	var favorites []models.Favorite
	if err := cursor.All(ctx, &favorites); err != nil {
		return nil, 0, err
	}

	if len(favorites) == 0 {
		return []models.Property{}, total, nil
	}

	// Create a slice of property IDs
	var propertyIDs []primitive.ObjectID
	for _, fav := range favorites {
		propertyIDs = append(propertyIDs, fav.PropertyID)
	}

	// Build property filter
	propertyFilter := bson.M{
		"_id": bson.M{"$in": propertyIDs},
	}

	// Add additional filters if provided
	if len(filters) > 0 {
		for key, value := range filters {
			propertyFilter[key] = value
		}
	}

	// Find all properties with these IDs and filters
	propertyCursor, err := propertyCollection.Find(ctx, propertyFilter)
	if err != nil {
		return nil, 0, err
	}
	defer propertyCursor.Close(ctx)

	// Decode properties
	var properties []models.Property
	if err := propertyCursor.All(ctx, &properties); err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

// CheckIfFavorite checks if a property is in a user's favorites
func (s *Service) CheckIfFavorite(ctx context.Context, userID, propertyID primitive.ObjectID) (bool, error) {
	favoriteCollection := s.db.GetCollection("favorites")

	count, err := favoriteCollection.CountDocuments(ctx, bson.M{
		"userId":     userID,
		"propertyId": propertyID,
	})
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckMultipleFavorites efficiently checks favorite status for multiple properties in a single database call
func (s *Service) CheckMultipleFavorites(ctx context.Context, userID primitive.ObjectID, propertyIDs []primitive.ObjectID) (map[primitive.ObjectID]bool, error) {
	favoriteCollection := s.db.GetCollection("favorites")

	// Initialize result map with all properties as false
	favoriteMap := make(map[primitive.ObjectID]bool, len(propertyIDs))
	for _, propertyID := range propertyIDs {
		favoriteMap[propertyID] = false
	}

	// Early return if no properties to check
	if len(propertyIDs) == 0 {
		return favoriteMap, nil
	}

	// Find all favorites for this user that match the given property IDs
	filter := bson.M{
		"userId": userID,
		"propertyId": bson.M{
			"$in": propertyIDs,
		},
	}

	// Only select the propertyId field to minimize data transfer
	opts := options.Find().SetProjection(bson.M{"propertyId": 1})

	cursor, err := favoriteCollection.Find(ctx, filter, opts)
	if err != nil {
		return favoriteMap, err
	}
	defer cursor.Close(ctx)

	// Process results and mark favorites as true
	var favorites []models.Favorite
	if err := cursor.All(ctx, &favorites); err != nil {
		return favoriteMap, err
	}

	// Update the map with actual favorite status
	for _, favorite := range favorites {
		favoriteMap[favorite.PropertyID] = true
	}

	return favoriteMap, nil
}
