package notification

import (
	"context"
	"fmt"
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type Service struct {
	db      *mongodb.MongoDBClient
	adminID primitive.ObjectID
}

// NewService creates a new notification service
func NewService(db *mongodb.MongoDBClient, adminID primitive.ObjectID) *Service {
	service := &Service{
		db:      db,
		adminID: adminID,
	}

	// Create indexes for better performance
	service.createIndexes()

	return service
}

// createIndexes creates database indexes for notifications collection
func (s *Service) createIndexes() {
	ctx := context.Background()
	collection := s.db.GetCollection("notifications")

	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "recipient_id", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "recipient_id", Value: 1},
				{Key: "is_read", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "type", Value: 1},
				{Key: "created_at", Value: -1},
			},
		},
	}

	_, err := collection.Indexes().CreateMany(ctx, indexes)
	if err != nil {
		// Log error but don't fail service creation
		// In production, you might want to handle this differently
	}
}

// CreateNotification creates a new notification
func (s *Service) CreateNotification(ctx context.Context, notification *models.Notification) error {
	notification.CreatedAt = time.Now()
	notification.UpdatedAt = time.Now()
	notification.IsRead = false

	collection := s.db.GetCollection("notifications")
	result, err := collection.InsertOne(ctx, notification)
	if err != nil {
		return err
	}

	notification.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

// NotifyAdmin creates a notification for the admin
func (s *Service) NotifyAdmin(ctx context.Context, notificationType, title, message string, senderID, relatedID primitive.ObjectID) error {
	notification := &models.Notification{
		RecipientID: s.adminID,
		SenderID:    senderID,
		Type:        notificationType,
		Title:       title,
		Message:     message,
		RelatedID:   relatedID,
	}

	return s.CreateNotification(ctx, notification)
}

// NotifyUser creates a notification for a specific user
func (s *Service) NotifyUser(ctx context.Context, userID primitive.ObjectID, notificationType, title, message string, relatedID primitive.ObjectID) error {
	notification := &models.Notification{
		RecipientID: userID,
		SenderID:    s.adminID, // Admin is usually the sender for user notifications
		Type:        notificationType,
		Title:       title,
		Message:     message,
		RelatedID:   relatedID,
	}

	return s.CreateNotification(ctx, notification)
}

// GetNotifications retrieves notifications for a user with pagination
func (s *Service) GetNotifications(ctx context.Context, userID primitive.ObjectID, req *models.GetNotificationsRequest) (*models.GetNotificationsResponse, error) {
	collection := s.db.GetCollection("notifications")

	// Set default values
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 || req.Limit > 50 {
		req.Limit = 20
	}

	// Build filter
	filter := bson.M{"recipient_id": userID}
	if req.OnlyUnread {
		filter["is_read"] = false
	}

	// Calculate skip
	skip := int64((req.Page - 1) * req.Limit)

	// Get total count
	totalCount, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Get unread count
	unreadFilter := bson.M{"recipient_id": userID, "is_read": false}
	unreadCount, err := collection.CountDocuments(ctx, unreadFilter)
	if err != nil {
		return nil, err
	}

	// Find notifications with pagination
	findOptions := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}}).
		SetSkip(skip).
		SetLimit(int64(req.Limit))

	cursor, err := collection.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var notifications []models.Notification
	if err := cursor.All(ctx, &notifications); err != nil {
		return nil, err
	}

	// Convert to response format
	notificationResponses := make([]models.NotificationResponse, len(notifications))
	for i, notification := range notifications {
		notificationResponses[i] = models.NotificationResponse{
			ID:        notification.ID.Hex(),
			Type:      notification.Type,
			Title:     notification.Title,
			Message:   notification.Message,
			IsRead:    notification.IsRead,
			CreatedAt: notification.CreatedAt,
		}
		if !notification.RelatedID.IsZero() {
			notificationResponses[i].RelatedID = notification.RelatedID.Hex()
		}
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(totalCount) / float64(req.Limit)))
	if totalPages == 0 {
		totalPages = 1
	}

	return &models.GetNotificationsResponse{
		Notifications: notificationResponses,
		TotalCount:    totalCount,
		UnreadCount:   unreadCount,
		CurrentPage:   req.Page,
		TotalPages:    totalPages,
		HasNext:       req.Page < totalPages,
		HasPrevious:   req.Page > 1,
	}, nil
}

// MarkAsRead marks a notification as read
func (s *Service) MarkAsRead(ctx context.Context, userID, notificationID primitive.ObjectID) error {
	collection := s.db.GetCollection("notifications")

	filter := bson.M{
		"_id":          notificationID,
		"recipient_id": userID,
	}

	update := bson.M{
		"$set": bson.M{
			"is_read":    true,
			"updated_at": time.Now(),
		},
	}

	result, err := collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// MarkAllAsRead marks all notifications as read for a user
func (s *Service) MarkAllAsRead(ctx context.Context, userID primitive.ObjectID) error {
	collection := s.db.GetCollection("notifications")

	filter := bson.M{
		"recipient_id": userID,
		"is_read":      false,
	}

	update := bson.M{
		"$set": bson.M{
			"is_read":    true,
			"updated_at": time.Now(),
		},
	}

	_, err := collection.UpdateMany(ctx, filter, update)
	return err
}

// GetNotificationCounts returns total and unread notification counts for a user
func (s *Service) GetNotificationCounts(ctx context.Context, userID primitive.ObjectID) (*models.NotificationCountResponse, error) {
	collection := s.db.GetCollection("notifications")

	// Get total count
	totalCount, err := collection.CountDocuments(ctx, bson.M{"recipient_id": userID})
	if err != nil {
		return nil, err
	}

	// Get unread count
	unreadCount, err := collection.CountDocuments(ctx, bson.M{"recipient_id": userID, "is_read": false})
	if err != nil {
		return nil, err
	}

	return &models.NotificationCountResponse{
		TotalCount:  totalCount,
		UnreadCount: unreadCount,
	}, nil
}

// Helper methods for creating specific notification types

// NotifyPropertyCreated notifies admin when a user creates a property
func (s *Service) NotifyPropertyCreated(ctx context.Context, userID, propertyID primitive.ObjectID, propertyTitle, userName string) error {
	title := "New Property Listed"
	message := fmt.Sprintf("%s has listed a new property '%s' for verification", userName, propertyTitle)
	return s.NotifyAdmin(ctx, models.NotificationTypePropertyCreated, title, message, userID, propertyID)
}

// NotifyPropertyVerified notifies user when admin verifies their property
func (s *Service) NotifyPropertyVerified(ctx context.Context, userID, propertyID primitive.ObjectID, propertyTitle string) error {
	title := "Property Verified"
	message := fmt.Sprintf("Your property '%s' has been verified and is now live", propertyTitle)
	return s.NotifyUser(ctx, userID, models.NotificationTypePropertyVerified, title, message, propertyID)
}

// NotifyPropertyRejected notifies user when admin rejects their property
func (s *Service) NotifyPropertyRejected(ctx context.Context, userID, propertyID primitive.ObjectID, propertyTitle string) error {
	title := "Property Rejected"
	message := fmt.Sprintf("Your property '%s' has been rejected. Please review and resubmit", propertyTitle)
	return s.NotifyUser(ctx, userID, models.NotificationTypePropertyRejected, title, message, propertyID)
}

// NotifyUserBlocked notifies user when admin blocks them
func (s *Service) NotifyUserBlocked(ctx context.Context, userID primitive.ObjectID) error {
	title := "Account Blocked"
	message := "Your account has been blocked by the administrator. Please contact support for assistance"
	return s.NotifyUser(ctx, userID, models.NotificationTypeUserBlocked, title, message, primitive.NilObjectID)
}

// NotifyUserUnblocked notifies user when admin unblocks them
func (s *Service) NotifyUserUnblocked(ctx context.Context, userID primitive.ObjectID) error {
	title := "Account Unblocked"
	message := "Your account has been unblocked. You can now access all features"
	return s.NotifyUser(ctx, userID, models.NotificationTypeUserUnblocked, title, message, primitive.NilObjectID)
}
