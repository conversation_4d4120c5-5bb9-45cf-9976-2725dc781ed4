package property

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type Service struct {
	db *mongodb.MongoDBClient
}

func NewService(db *mongodb.MongoDBClient) *Service {
	return &Service{db: db}
}

func (s *Service) CreateProperty(ctx context.Context, property *models.Property) error {
	property.CreatedAt = time.Now()
	property.UpdatedAt = time.Now()

	collection := s.db.GetCollection("properties")
	result, err := collection.InsertOne(ctx, property)
	if err != nil {
		return err
	}

	property.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (s *Service) GetProperty(ctx context.Context, id primitive.ObjectID) (*models.Property, error) {
	collection := s.db.GetCollection("properties")

	var property models.Property
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&property)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	return &property, nil
}

// GetPropertyWithLatestReview retrieves a property along with its latest review and like status
func (s *Service) GetPropertyWithLatestReview(ctx context.Context, id primitive.ObjectID, userID *primitive.ObjectID, isAdmin bool) (*models.PropertyDetailResponse, error) {
	// Create a context with timeout for better resource management
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Create channels for concurrent operations
	type result struct {
		property *models.Property
		owner    *models.User
		review   *models.Review
		err      error
	}

	propertyChan := make(chan result, 1)
	reviewChan := make(chan result, 1)

	// Get the property and owner details concurrently
	go func() {
		var res result
		property, err := s.GetProperty(ctx, id)
		if err != nil {
			res.err = err
			propertyChan <- res
			return
		}
		if property == nil {
			propertyChan <- res
			return
		}

		// Get owner details
		userCollection := s.db.GetCollection("users")
		var owner models.User
		if err := userCollection.FindOne(ctx, bson.M{"_id": property.OwnerID}).Decode(&owner); err != nil {
			res.err = err
			propertyChan <- res
			return
		}

		// Add owner's details to property
		property.OwnerEmail = owner.Email
		property.OwnerPhoneNumber = owner.PhoneNumber
		property.OwnerProfilePhoto = owner.ProfilePhoto

		res.property = property
		res.owner = &owner
		propertyChan <- res
	}()

	// Get the latest review concurrently
	go func() {
		var res result
		reviewCollection := s.db.GetCollection("reviews")

		// Create filter based on user role
		filter := bson.M{"property_id": id}
		if !isAdmin {
			filter["is_active"] = true
		}

		// Find the latest review for this property
		opts := options.FindOne().
			SetSort(bson.M{"created_at": -1})

		var review models.Review
		if err := reviewCollection.FindOne(ctx, filter, opts).Decode(&review); err != nil {
			if err != mongo.ErrNoDocuments {
				res.err = err
			}
			reviewChan <- res
			return
		}

		res.review = &review
		reviewChan <- res
	}()

	// Wait for property and owner details
	propertyResult := <-propertyChan
	if propertyResult.err != nil {
		return nil, propertyResult.err
	}
	if propertyResult.property == nil {
		return nil, nil
	}

	// Create response
	response := &models.PropertyDetailResponse{
		Property:        *propertyResult.property,
		Review:          nil,
		IsFavorite:      false,
		RelatedProperty: nil,
	}

	// Wait for review result
	reviewResult := <-reviewChan
	if reviewResult.err != nil {
		return nil, reviewResult.err
	}

	// If we found a review, check if user has liked/disliked it and add to response
	if reviewResult.review != nil {
		// Get reviewer's profile photo
		userCollection := s.db.GetCollection("users")
		var reviewer models.User
		if err := userCollection.FindOne(ctx, bson.M{"_id": reviewResult.review.UserID}).Decode(&reviewer); err != nil {
			return nil, err
		}

		reviewWithLikeStatus := &models.ReviewWithLikeStatus{
			ID:               reviewResult.review.ID,
			UserID:           reviewResult.review.UserID,
			PropertyID:       reviewResult.review.PropertyID,
			UserName:         reviewResult.review.UserName,
			Rating:           reviewResult.review.Rating,
			Comment:          reviewResult.review.Comment,
			Likes:            reviewResult.review.Likes,
			DisLikes:         reviewResult.review.DisLikes,
			Photos:           reviewResult.review.Photos,
			IsActive:         reviewResult.review.IsActive,
			CreatedAt:        reviewResult.review.CreatedAt,
			UpdatedAt:        reviewResult.review.UpdatedAt,
			IsLiked:          false,
			IsDisliked:       false,
			UserProfilePhoto: reviewer.ProfilePhoto,
		}

		// If user is logged in, check their like/dislike status
		if userID != nil {
			isLiked, isDisliked, err := s.checkUserReviewStatus(ctx, reviewResult.review.ID, *userID)
			if err == nil {
				reviewWithLikeStatus.IsLiked = isLiked
				reviewWithLikeStatus.IsDisliked = isDisliked
			}
		}

		response.Review = reviewWithLikeStatus
	}

	// Check if property is in user's favorites
	if userID != nil {
		favoriteCollection := s.db.GetCollection("favorites")
		count, err := favoriteCollection.CountDocuments(ctx, bson.M{
			"userId":     *userID,
			"propertyId": id,
		})
		if err == nil && count > 0 {
			response.IsFavorite = true
		}
	}

	// Get a related property from the same area/city
	propertiesCollection := s.db.GetCollection("properties")

	// First try to find a property in the same area (regardless of city)
	areaPropertyFilter := bson.M{
		"_id":                bson.M{"$ne": id},                                                                    // Exclude current property
		"location.area":      bson.M{"$regex": "^" + propertyResult.property.Location.Area + "$", "$options": "i"}, // Case-insensitive match
		"is_property_active": true,
	}

	// Find one related property from the same area
	var relatedProperty models.Property
	if err := propertiesCollection.FindOne(ctx, areaPropertyFilter, options.FindOne().SetSort(bson.M{"created_at": -1})).Decode(&relatedProperty); err != nil {
		// If no property found in the same area, try finding one in the same city
		cityPropertyFilter := bson.M{
			"_id":                bson.M{"$ne": id},                                                                    // Exclude current property
			"location.city":      bson.M{"$regex": "^" + propertyResult.property.Location.City + "$", "$options": "i"}, // Case-insensitive match
			"is_property_active": true,
		}

		if err := propertiesCollection.FindOne(ctx, cityPropertyFilter, options.FindOne().SetSort(bson.M{"created_at": -1})).Decode(&relatedProperty); err != nil {
			// No related property found, continue without error
			return response, nil
		}
	}

	// Convert to RelatedProperty format
	relatedPropertyResponse := &models.RelatedProperty{
		ID:         relatedProperty.ID,
		Title:      relatedProperty.Title,
		Status:     relatedProperty.Status,
		Type:       relatedProperty.Type,
		Area:       relatedProperty.Area,
		YearBuilt:  relatedProperty.YearBuilt,
		BHK:        relatedProperty.BHK,
		Bedroom:    relatedProperty.Bedroom,
		Bathroom:   relatedProperty.Bathroom,
		Location:   relatedProperty.Location,
		TotalPrice: relatedProperty.TotalPrice,
	}

	// Add first image if available
	if len(relatedProperty.Images) > 0 {
		relatedPropertyResponse.Image = relatedProperty.Images[0]
	}

	// Check if related property is in user's favorites
	if userID != nil {
		favoriteCollection := s.db.GetCollection("favorites")
		count, err := favoriteCollection.CountDocuments(ctx, bson.M{
			"userId":     *userID,
			"propertyId": relatedProperty.ID,
		})
		if err == nil && count > 0 {
			relatedPropertyResponse.IsFavorite = true
		}
	}

	response.RelatedProperty = relatedPropertyResponse
	return response, nil
}

// checkUserReviewStatus checks if a user has liked or disliked a specific review
func (s *Service) checkUserReviewStatus(ctx context.Context, reviewID, userID primitive.ObjectID) (bool, bool, error) {
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check for like
	likeCount, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "like",
	})
	if err != nil {
		return false, false, err
	}

	// Check for dislike
	dislikeCount, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "dislike",
	})
	if err != nil {
		return false, false, err
	}

	return likeCount > 0, dislikeCount > 0, nil
}

func (s *Service) ListProperties(ctx context.Context, filter bson.M, page, limit int64) ([]*models.Property, int64, error) {
	collection := s.db.GetCollection("properties")

	// Set up pagination
	opts := options.Find().
		SetSkip((page - 1) * limit).
		SetLimit(limit).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Decode results
	var properties []*models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

// ListPremiumProperties retrieves properties that are added by subscribed users
// Uses an optimized approach with MongoDB aggregation for better performance with large datasets
func (s *Service) ListPremiumProperties(ctx context.Context, page, limit int64) ([]*models.Property, int64, error) {
	propertiesCollection := s.db.GetCollection("properties")

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Create a pipeline for the aggregation
	// This performs a join between properties and users collections
	pipeline := mongo.Pipeline{
		// Stage 1: Lookup to join with users collection
		bson.D{
			{Key: "$lookup", Value: bson.D{
				{Key: "from", Value: "users"},
				{Key: "localField", Value: "owner_id"},
				{Key: "foreignField", Value: "_id"},
				{Key: "as", Value: "owner"},
			}},
		},
		// Stage 2: Unwind the owner array (converts it from array to object)
		bson.D{
			{Key: "$unwind", Value: bson.D{
				{Key: "path", Value: "$owner"},
				{Key: "preserveNullAndEmptyArrays", Value: false},
			}},
		},
		// Stage 3: Match only properties where owner is subscribed and property is active
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "owner.is_subscribed_user", Value: true},
				{Key: "is_property_active", Value: true},
			}},
		},
		// Stage 4: Project to remove the owner field (we don't need it anymore)
		bson.D{
			{Key: "$project", Value: bson.D{
				{Key: "owner", Value: 0},
			}},
		},
		// Stage 5: Sort by created_at in descending order
		bson.D{
			{Key: "$sort", Value: bson.D{
				{Key: "created_at", Value: -1},
			}},
		},
	}

	// Create a separate pipeline for counting total documents
	countPipeline := mongo.Pipeline{
		// Stage 1: Lookup to join with users collection
		bson.D{
			{Key: "$lookup", Value: bson.D{
				{Key: "from", Value: "users"},
				{Key: "localField", Value: "owner_id"},
				{Key: "foreignField", Value: "_id"},
				{Key: "as", Value: "owner"},
			}},
		},
		// Stage 2: Unwind the owner array
		bson.D{
			{Key: "$unwind", Value: bson.D{
				{Key: "path", Value: "$owner"},
				{Key: "preserveNullAndEmptyArrays", Value: false},
			}},
		},
		// Stage 3: Match only properties where owner is subscribed and property is active
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "owner.is_subscribed_user", Value: true},
				{Key: "is_property_active", Value: true},
			}},
		},
		// Stage 4: Count documents
		bson.D{
			{Key: "$count", Value: "total"},
		},
	}

	// Execute count pipeline to get total
	countCursor, err := propertiesCollection.Aggregate(ctx, countPipeline)
	if err != nil {
		return nil, 0, err
	}
	defer countCursor.Close(ctx)

	// Get total count
	var countResult []bson.M
	if err = countCursor.All(ctx, &countResult); err != nil {
		return nil, 0, err
	}

	// Default total to 0 if no results
	var total int64 = 0
	if len(countResult) > 0 {
		// MongoDB might return different integer types (int32, int64, etc.)
		// Use a safer approach to convert to int64
		if totalValue, ok := countResult[0]["total"]; ok {
			// Log the type for debugging
			fmt.Printf("Total count type: %T, value: %v\n", totalValue, totalValue)

			// Handle different types
			if intVal, ok := totalValue.(int32); ok {
				total = int64(intVal)
			} else if intVal, ok := totalValue.(int64); ok {
				total = intVal
			} else if intVal, ok := totalValue.(int); ok {
				total = int64(intVal)
			} else if floatVal, ok := totalValue.(float64); ok {
				total = int64(floatVal)
			}
		}
	}

	// Add pagination stages to the main pipeline
	pipeline = append(pipeline,
		bson.D{{Key: "$skip", Value: skip}},
		bson.D{{Key: "$limit", Value: limit}},
	)

	// Execute main pipeline
	cursor, err := propertiesCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode results
	var properties []*models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}

func (s *Service) UpdateProperty(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := s.db.GetCollection("properties")

	update["updated_at"] = time.Now()

	result, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

func (s *Service) DeleteProperty(ctx context.Context, id primitive.ObjectID) error {
	collection := s.db.GetCollection("properties")

	result, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// CreateIndexes creates necessary indexes for the properties collection
func (s *Service) CreateIndexes(ctx context.Context) error {
	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "location.city", Value: 1},
				{Key: "price", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "agent_id", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "status", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "type", Value: 1},
			},
		},
	}

	return s.db.CreateIndexes(ctx, "properties", indexes)
}

// GetTrendingAreasByCity retrieves all distinct areas for a given city
func (s *Service) GetTrendingAreasByCity(ctx context.Context, city string) ([]string, error) {
	collection := s.db.GetCollection("properties")

	// Use distinct to get unique areas for the city
	areas, err := collection.Distinct(ctx, "location.area", bson.M{
		"location.city":      city,
		"is_property_active": true,
	})
	if err != nil {
		return nil, err
	}

	// Convert interface{} slice to string slice
	var trendingAreas []string
	for _, area := range areas {
		if areaStr, ok := area.(string); ok && areaStr != "" {
			trendingAreas = append(trendingAreas, areaStr)
		}
	}

	return trendingAreas, nil
}

// GetPropertiesByLocationAndType retrieves properties filtered by location, type, and status with area priority
func (s *Service) GetPropertiesByLocationAndType(ctx context.Context, city, area, propertyType, status string, limit int64) ([]*models.Property, error) {
	collection := s.db.GetCollection("properties")

	// First try to get properties from the specified area
	areaFilter := bson.M{
		"location.city":      city,
		"location.area":      area,
		"type":               propertyType,
		"status":             status,
		"is_property_active": true,
	}

	// Set up query options
	opts := options.Find().
		SetLimit(limit).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Execute query for area-specific properties
	cursor, err := collection.Find(ctx, areaFilter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// Decode results
	var properties []*models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, err
	}

	// If we have enough properties from the area, return them
	if int64(len(properties)) >= limit {
		return properties, nil
	}

	// If not enough properties from area, get more from nearby areas in the same city
	remainingLimit := limit - int64(len(properties))
	if remainingLimit > 0 {
		// Get properties from other areas in the same city (excluding the area we already checked)
		cityFilter := bson.M{
			"location.city":      city,
			"location.area":      bson.M{"$ne": area}, // Exclude the area we already checked
			"type":               propertyType,
			"status":             status,
			"is_property_active": true,
		}

		cityOpts := options.Find().
			SetLimit(remainingLimit).
			SetSort(bson.D{{Key: "created_at", Value: -1}})

		cityCursor, err := collection.Find(ctx, cityFilter, cityOpts)
		if err != nil {
			return nil, err
		}
		defer cityCursor.Close(ctx)

		var cityProperties []*models.Property
		if err = cityCursor.All(ctx, &cityProperties); err != nil {
			return nil, err
		}

		// Combine area and city properties
		properties = append(properties, cityProperties...)
	}

	return properties, nil
}

// GetPremiumPropertiesByLocationAndType retrieves premium properties with area priority
func (s *Service) GetPremiumPropertiesByLocationAndType(ctx context.Context, city, area, propertyType, status string, limit int64) ([]*models.Property, error) {
	propertiesCollection := s.db.GetCollection("properties")

	// First try to get properties from user's area
	areaFilter := bson.M{
		"location.city":      city,
		"location.area":      area,
		"type":               propertyType,
		"status":             status,
		"is_property_active": true,
	}

	// Build aggregation pipeline for area-specific premium properties
	areaPipeline := mongo.Pipeline{
		// Stage 1: Match properties in user's area
		bson.D{{Key: "$match", Value: areaFilter}},
		// Stage 2: Lookup to join with users collection
		bson.D{
			{Key: "$lookup", Value: bson.D{
				{Key: "from", Value: "users"},
				{Key: "localField", Value: "owner_id"},
				{Key: "foreignField", Value: "_id"},
				{Key: "as", Value: "owner"},
			}},
		},
		// Stage 3: Unwind the owner array
		bson.D{
			{Key: "$unwind", Value: bson.D{
				{Key: "path", Value: "$owner"},
				{Key: "preserveNullAndEmptyArrays", Value: false},
			}},
		},
		// Stage 4: Match only properties where owner is subscribed
		bson.D{
			{Key: "$match", Value: bson.D{
				{Key: "owner.is_subscribed_user", Value: true},
			}},
		},
		// Stage 5: Sort by creation date
		bson.D{{Key: "$sort", Value: bson.D{{Key: "created_at", Value: -1}}}},
		// Stage 6: Limit results
		bson.D{{Key: "$limit", Value: limit}},
	}

	// Execute area-specific query
	cursor, err := propertiesCollection.Aggregate(ctx, areaPipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var properties []*models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, err
	}

	// If we have enough properties from the area, return them
	if int64(len(properties)) >= limit {
		return properties, nil
	}

	// If not enough properties from area, get more from the city (excluding the area we already checked)
	remainingLimit := limit - int64(len(properties))
	if remainingLimit > 0 {
		cityFilter := bson.M{
			"location.city":      city,
			"location.area":      bson.M{"$ne": area}, // Exclude the area we already checked
			"type":               propertyType,
			"status":             status,
			"is_property_active": true,
		}

		cityPipeline := mongo.Pipeline{
			// Stage 1: Match properties in city (excluding user's area)
			bson.D{{Key: "$match", Value: cityFilter}},
			// Stage 2: Lookup to join with users collection
			bson.D{
				{Key: "$lookup", Value: bson.D{
					{Key: "from", Value: "users"},
					{Key: "localField", Value: "owner_id"},
					{Key: "foreignField", Value: "_id"},
					{Key: "as", Value: "owner"},
				}},
			},
			// Stage 3: Unwind the owner array
			bson.D{
				{Key: "$unwind", Value: bson.D{
					{Key: "path", Value: "$owner"},
					{Key: "preserveNullAndEmptyArrays", Value: false},
				}},
			},
			// Stage 4: Match only properties where owner is subscribed
			bson.D{
				{Key: "$match", Value: bson.D{
					{Key: "owner.is_subscribed_user", Value: true},
				}},
			},
			// Stage 5: Sort by creation date
			bson.D{{Key: "$sort", Value: bson.D{{Key: "created_at", Value: -1}}}},
			// Stage 6: Limit results
			bson.D{{Key: "$limit", Value: remainingLimit}},
		}

		// Execute city-wide query
		cityCursor, err := propertiesCollection.Aggregate(ctx, cityPipeline)
		if err != nil {
			return nil, err
		}
		defer cityCursor.Close(ctx)

		var cityProperties []*models.Property
		if err = cityCursor.All(ctx, &cityProperties); err != nil {
			return nil, err
		}

		// Combine area and city properties
		properties = append(properties, cityProperties...)
	}

	return properties, nil
}

// GetTopPerformingAgents retrieves top performing agents filtered by city and area
func (s *Service) GetTopPerformingAgents(ctx context.Context, city, area string, limit int64) ([]models.User, error) {
	// Build filter for subscribed users with at least one property listed in the specified city/area
	filter := bson.M{
		"is_subscribed_user":      true,
		"total_listed_properties": bson.M{"$gt": 0}, // Only include users with at least one property listed
		"$or": []bson.M{
			{"location.city": city, "location.area": area}, // Users from the same area
			{"location.city": city},                        // Users from the same city
		},
	}

	// Find subscribed users with limit, sorted by total listed properties in descending order
	cursor, err := s.db.GetCollection("users").Find(ctx, filter, options.Find().
		SetLimit(limit).
		SetSort(bson.M{"total_listed_properties": -1})) // Sort by total listed properties, highest first
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// Decode users
	var users []models.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, err
	}

	return users, nil
}

// GetActivePropertyTypes retrieves all active property types
func (s *Service) GetActivePropertyTypes(ctx context.Context) ([]models.PropertyType, error) {
	collection := s.db.GetCollection("property_types")

	// Build filter for active property types
	filter := bson.M{
		"active": true,
	}

	// Set up query options - sort by order
	opts := options.Find().
		SetSort(bson.D{{Key: "order", Value: 1}})

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	// Decode results
	var propertyTypes []models.PropertyType
	if err = cursor.All(ctx, &propertyTypes); err != nil {
		return nil, err
	}

	return propertyTypes, nil
}

// ComprehensiveSearchOptimized performs all searches concurrently for better performance
func (s *Service) ComprehensiveSearchOptimized(ctx context.Context, city, area, propertyType, status string, trendingArea string) (*models.ComprehensiveSearchResponse, error) {
	// Use context with timeout for better resource management
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Create channels for concurrent operations
	type result struct {
		trendingAreas   []string
		properties      []*models.Property
		topAgents       []models.User
		premiumListings []*models.Property
		err             error
	}

	resultChan := make(chan result, 1)

	// Determine search area
	searchArea := area
	if trendingArea != "" {
		searchArea = trendingArea
	}

	// Execute all queries concurrently using goroutines
	go func() {
		var res result

		// Use WaitGroup for concurrent execution
		var wg sync.WaitGroup
		wg.Add(4) // Reduced from 5 to 4 since we removed property types

		// 1. Get trending areas concurrently
		go func() {
			defer wg.Done()
			areas, err := s.GetTrendingAreasByCity(ctx, city)
			if err != nil {
				res.err = fmt.Errorf("trending areas error: %w", err)
				return
			}
			res.trendingAreas = areas
		}()

		// 2. Get properties concurrently
		go func() {
			defer wg.Done()
			properties, err := s.GetPropertiesByLocationAndType(ctx, city, searchArea, propertyType, status, 3)
			if err != nil {
				res.err = fmt.Errorf("properties error: %w", err)
				return
			}
			res.properties = properties
		}()

		// 3. Get top agents concurrently
		go func() {
			defer wg.Done()
			agents, err := s.GetTopPerformingAgents(ctx, city, area, 5)
			if err != nil {
				res.err = fmt.Errorf("top agents error: %w", err)
				return
			}
			res.topAgents = agents
		}()

		// 4. Get premium listings concurrently
		go func() {
			defer wg.Done()
			listings, err := s.GetPremiumPropertiesByLocationAndType(ctx, city, searchArea, propertyType, status, 5)
			if err != nil {
				res.err = fmt.Errorf("premium listings error: %w", err)
				return
			}
			res.premiumListings = listings
		}()

		wg.Wait()
		resultChan <- res
	}()

	// Wait for all operations to complete
	select {
	case res := <-resultChan:
		if res.err != nil {
			return nil, res.err
		}

		return &models.ComprehensiveSearchResponse{
			TrendingAreas:   res.trendingAreas,
			Properties:      res.properties,
			TopAgents:       res.topAgents,
			PremiumListings: res.premiumListings,
		}, nil

	case <-ctx.Done():
		return nil, fmt.Errorf("comprehensive search timeout: %w", ctx.Err())
	}
}

// GetPropertiesByUserID retrieves all properties owned by a specific user with pagination (legacy method)
func (s *Service) GetPropertiesByUserID(ctx context.Context, userID primitive.ObjectID, page, limit int64) ([]*models.Property, int64, error) {
	// Use the new optimized method with default filters
	filters := bson.M{"owner_id": userID}
	return s.GetPropertiesByUserIDWithFilters(ctx, userID, page, limit, filters, "created_at", -1)
}

// GetPropertiesByUserIDWithFilters retrieves properties owned by a specific user with advanced filtering and sorting
func (s *Service) GetPropertiesByUserIDWithFilters(ctx context.Context, userID primitive.ObjectID, page, limit int64, filters bson.M, sortBy string, sortDirection int) ([]*models.Property, int64, error) {
	collection := s.db.GetCollection("properties")

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Ensure owner_id is always in the filter
	filters["owner_id"] = userID

	// Get total count of properties for this user with filters
	total, err := collection.CountDocuments(ctx, filters)
	if err != nil {
		return nil, 0, err
	}

	// Set up query options with pagination and sorting
	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.D{{Key: sortBy, Value: sortDirection}})

	// Execute query with filters
	cursor, err := collection.Find(ctx, filters, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode results
	var properties []*models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, 0, err
	}

	return properties, total, nil
}
