package property_metadata

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type Service struct {
	db *mongodb.MongoDBClient
}

func NewService(db *mongodb.MongoDBClient) *Service {
	return &Service{db: db}
}

// PropertyType CRUD operations
func (s *Service) CreatePropertyType(ctx context.Context, propertyType *models.PropertyType) error {
	propertyType.CreatedAt = time.Now()
	propertyType.UpdatedAt = time.Now()

	collection := s.db.GetCollection("property_types")
	result, err := collection.InsertOne(ctx, propertyType)
	if err != nil {
		return err
	}

	propertyType.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (s *Service) GetPropertyType(ctx context.Context, id primitive.ObjectID) (*models.PropertyType, error) {
	collection := s.db.GetCollection("property_types")

	var propertyType models.PropertyType
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&propertyType)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	return &propertyType, nil
}

func (s *Service) ListPropertyTypes(ctx context.Context, filter bson.M, page, limit int64) ([]*models.PropertyType, int64, error) {
	collection := s.db.GetCollection("property_types")

	// Set up pagination
	opts := options.Find().
		SetSkip((page - 1) * limit).
		SetLimit(limit).
		SetSort(bson.D{{Key: "order", Value: 1}})

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Decode results
	var propertyTypes []*models.PropertyType
	if err = cursor.All(ctx, &propertyTypes); err != nil {
		return nil, 0, err
	}

	return propertyTypes, total, nil
}

func (s *Service) UpdatePropertyType(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := s.db.GetCollection("property_types")

	update["updated_at"] = time.Now()

	result, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

func (s *Service) DeletePropertyType(ctx context.Context, id primitive.ObjectID) error {
	collection := s.db.GetCollection("property_types")

	result, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// Facility CRUD operations
func (s *Service) CreateFacility(ctx context.Context, facility *models.Facility) error {
	facility.CreatedAt = time.Now()
	facility.UpdatedAt = time.Now()

	collection := s.db.GetCollection("facilities")
	result, err := collection.InsertOne(ctx, facility)
	if err != nil {
		return err
	}

	facility.ID = result.InsertedID.(primitive.ObjectID)
	return nil
}

func (s *Service) GetFacility(ctx context.Context, id primitive.ObjectID) (*models.Facility, error) {
	collection := s.db.GetCollection("facilities")

	var facility models.Facility
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&facility)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	return &facility, nil
}

func (s *Service) ListFacilities(ctx context.Context, filter bson.M, page, limit int64) ([]*models.Facility, int64, error) {
	collection := s.db.GetCollection("facilities")

	// Set up pagination
	opts := options.Find().
		SetSkip((page - 1) * limit).
		SetLimit(limit).
		SetSort(bson.D{{Key: "name", Value: 1}})

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Decode results
	var facilities []*models.Facility
	if err = cursor.All(ctx, &facilities); err != nil {
		return nil, 0, err
	}

	return facilities, total, nil
}

func (s *Service) UpdateFacility(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	collection := s.db.GetCollection("facilities")

	update["updated_at"] = time.Now()

	result, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

func (s *Service) DeleteFacility(ctx context.Context, id primitive.ObjectID) error {
	collection := s.db.GetCollection("facilities")

	result, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// CreateIndexes creates necessary indexes for the property_types and facilities collections
func (s *Service) CreateIndexes(ctx context.Context) error {
	// Property Types indexes
	propertyTypeIndexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "name", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "order", Value: 1},
			},
		},
	}

	if err := s.db.CreateIndexes(ctx, "property_types", propertyTypeIndexes); err != nil {
		return err
	}

	// Facilities indexes
	facilityIndexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "name", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "active", Value: 1},
			},
		},
	}

	return s.db.CreateIndexes(ctx, "facilities", facilityIndexes)
}
