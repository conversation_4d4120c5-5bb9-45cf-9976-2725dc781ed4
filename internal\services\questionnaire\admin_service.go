package questionnaire

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type AdminService struct {
	db     *mongodb.MongoDBClient
	logger *zap.Logger
}

func NewAdminService(db *mongodb.MongoDBClient, logger *zap.Logger) *AdminService {
	return &AdminService{
		db:     db,
		logger: logger,
	}
}

// CreateQuestion creates a new questionnaire question
func (as *AdminService) CreateQuestion(ctx context.Context, req *models.CreateQuestionRequest) (*models.Question, error) {
	// Check if question with same order already exists
	existingQuestion, err := as.getQuestionByOrder(ctx, req.Order)
	if err == nil && existingQuestion != nil {
		return nil, fmt.Errorf("question with order %d already exists", req.Order)
	}

	// Create new question
	question := &models.Question{
		ID:         primitive.NewObjectID(),
		Text:       req.Text,
		Type:       req.Type,
		Order:      req.Order,
		IsRequired: req.IsRequired,
		Options:    req.Options,
		Validation: req.Validation,
		IsActive:   true,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	collection := as.db.GetCollection("questions")
	_, err = collection.InsertOne(ctx, question)
	if err != nil {
		return nil, fmt.Errorf("failed to create question: %w", err)
	}

	return question, nil
}

// ListQuestions retrieves all questions with pagination and filtering
func (as *AdminService) ListQuestions(ctx context.Context, page, limit int, activeFilter *bool) ([]models.Question, int64, error) {
	collection := as.db.GetCollection("questions")

	// Build filter
	filter := bson.M{}
	if activeFilter != nil {
		filter["is_active"] = *activeFilter
	}

	// Calculate skip value
	skip := (page - 1) * limit

	// Set up options
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "order", Value: 1}}) // Sort by order ascending

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to find questions: %w", err)
	}
	defer cursor.Close(ctx)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count questions: %w", err)
	}

	// Decode results
	var questions []models.Question
	if err = cursor.All(ctx, &questions); err != nil {
		return nil, 0, fmt.Errorf("failed to decode questions: %w", err)
	}

	return questions, total, nil
}

// GetQuestion retrieves a specific question by ID
func (as *AdminService) GetQuestion(ctx context.Context, questionID primitive.ObjectID) (*models.Question, error) {
	collection := as.db.GetCollection("questions")
	filter := bson.M{"_id": questionID}

	var question models.Question
	err := collection.FindOne(ctx, filter).Decode(&question)
	if err != nil {
		return nil, fmt.Errorf("failed to find question: %w", err)
	}

	return &question, nil
}

// UpdateQuestion updates an existing question
func (as *AdminService) UpdateQuestion(ctx context.Context, questionID primitive.ObjectID, req *models.UpdateQuestionRequest) (*models.Question, error) {
	collection := as.db.GetCollection("questions")

	// Build update document
	update := bson.M{
		"$set": bson.M{
			"updated_at": time.Now(),
		},
	}

	// Add fields to update if provided
	if req.Text != "" {
		update["$set"].(bson.M)["text"] = req.Text
	}
	if req.Type != "" {
		update["$set"].(bson.M)["type"] = req.Type
	}
	if req.Order > 0 {
		// Check if another question already has this order
		existingQuestion, err := as.getQuestionByOrder(ctx, req.Order)
		if err == nil && existingQuestion != nil && existingQuestion.ID != questionID {
			return nil, fmt.Errorf("question with order %d already exists", req.Order)
		}
		update["$set"].(bson.M)["order"] = req.Order
	}
	if req.IsRequired != nil {
		update["$set"].(bson.M)["is_required"] = *req.IsRequired
	}
	if req.Options != nil {
		update["$set"].(bson.M)["options"] = req.Options
	}
	if req.IsActive != nil {
		update["$set"].(bson.M)["is_active"] = *req.IsActive
	}

	// Update validation if provided
	update["$set"].(bson.M)["validation"] = req.Validation

	// Execute update
	_, err := collection.UpdateOne(ctx, bson.M{"_id": questionID}, update)
	if err != nil {
		return nil, fmt.Errorf("failed to update question: %w", err)
	}

	// Return updated question
	return as.GetQuestion(ctx, questionID)
}

// DeleteQuestion deletes a question (soft delete by setting is_active to false)
func (as *AdminService) DeleteQuestion(ctx context.Context, questionID primitive.ObjectID) error {
	collection := as.db.GetCollection("questions")

	update := bson.M{
		"$set": bson.M{
			"is_active":  false,
			"updated_at": time.Now(),
		},
	}

	_, err := collection.UpdateOne(ctx, bson.M{"_id": questionID}, update)
	if err != nil {
		return fmt.Errorf("failed to delete question: %w", err)
	}

	return nil
}

// Helper methods

func (as *AdminService) getQuestionByOrder(ctx context.Context, order int) (*models.Question, error) {
	collection := as.db.GetCollection("questions")
	filter := bson.M{
		"order":     order,
		"is_active": true,
	}

	var question models.Question
	err := collection.FindOne(ctx, filter).Decode(&question)
	if err != nil {
		return nil, err
	}

	return &question, nil
}

// SeedDefaultQuestions creates default questionnaire questions
func (as *AdminService) SeedDefaultQuestions(ctx context.Context) error {
	// Check if questions already exist
	count, err := as.db.GetCollection("questions").CountDocuments(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to check existing questions: %w", err)
	}

	if count > 0 {
		as.logger.Info("Questions already exist, skipping seed")
		return nil
	}

	// Define default questions
	defaultQuestions := []models.Question{
		{
			ID:         primitive.NewObjectID(),
			Text:       "What's your budget range for the property?",
			Type:       "single_choice",
			Order:      1,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "budget_1", Text: "₹10L - ₹25L", Value: "1000000-2500000"},
				{ID: "budget_2", Text: "₹25L - ₹50L", Value: "2500000-5000000"},
				{ID: "budget_3", Text: "₹50L - ₹1Cr", Value: "5000000-10000000"},
				{ID: "budget_4", Text: "₹1Cr - ₹2Cr", Value: "10000000-20000000"},
				{ID: "budget_5", Text: "Above ₹2Cr", Value: "20000000-50000000"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"cheap", "dirty", "whatever", "anything"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "What type of property are you looking for?",
			Type:       "single_choice",
			Order:      2,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "type_1", Text: "Apartment", Value: "apartment"},
				{ID: "type_2", Text: "House", Value: "house"},
				{ID: "type_3", Text: "Villa", Value: "villa"},
				{ID: "type_4", Text: "Commercial", Value: "commercial"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"whatever", "anything", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "Which city are you interested in?",
			Type:       "single_choice",
			Order:      3,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "city_1", Text: "Mumbai", Value: "mumbai"},
				{ID: "city_2", Text: "Delhi", Value: "delhi"},
				{ID: "city_3", Text: "Bangalore", Value: "bangalore"},
				{ID: "city_4", Text: "Pune", Value: "pune"},
				{ID: "city_5", Text: "Chennai", Value: "chennai"},
				{ID: "city_6", Text: "Hyderabad", Value: "hyderabad"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"anywhere", "whatever", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "Which area/neighborhood do you prefer?",
			Type:       "single_choice",
			Order:      4,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "area_1", Text: "Andheri", Value: "andheri"},
				{ID: "area_2", Text: "Bandra", Value: "bandra"},
				{ID: "area_3", Text: "Powai", Value: "powai"},
				{ID: "area_4", Text: "Thane", Value: "thane"},
				{ID: "area_5", Text: "Navi Mumbai", Value: "navi mumbai"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"anywhere", "whatever", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "How many bedrooms do you need?",
			Type:       "single_choice",
			Order:      5,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "bhk_1", Text: "1 BHK", Value: "1"},
				{ID: "bhk_2", Text: "2 BHK", Value: "2"},
				{ID: "bhk_3", Text: "3 BHK", Value: "3"},
				{ID: "bhk_4", Text: "4+ BHK", Value: "4+"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"whatever", "anything", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "Which amenities are important to you?",
			Type:       "multiple_choice",
			Order:      6,
			IsRequired: false,
			Options: []models.QuestionOption{
				{ID: "amenity_1", Text: "Parking", Value: "parking"},
				{ID: "amenity_2", Text: "Gym", Value: "gym"},
				{ID: "amenity_3", Text: "Swimming Pool", Value: "pool"},
				{ID: "amenity_4", Text: "Security", Value: "security"},
				{ID: "amenity_5", Text: "Garden", Value: "garden"},
				{ID: "amenity_6", Text: "Elevator", Value: "elevator"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"whatever", "anything", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "Are you looking to buy or rent?",
			Type:       "single_choice",
			Order:      7,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "purpose_1", Text: "Buy", Value: "buy"},
				{ID: "purpose_2", Text: "Rent", Value: "rent"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"whatever", "anything", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:         primitive.NewObjectID(),
			Text:       "What furnishing do you prefer?",
			Type:       "single_choice",
			Order:      8,
			IsRequired: true,
			Options: []models.QuestionOption{
				{ID: "furnishing_1", Text: "Fully Furnished", Value: "furnished"},
				{ID: "furnishing_2", Text: "Semi Furnished", Value: "semi-furnished"},
				{ID: "furnishing_3", Text: "Unfurnished", Value: "unfurnished"},
			},
			Validation: models.ValidationRules{
				ForbiddenWords: []string{"whatever", "anything", "don't care"},
			},
			IsActive:  true,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	// Insert all questions
	collection := as.db.GetCollection("questions")
	var documents []interface{}
	for _, question := range defaultQuestions {
		documents = append(documents, question)
	}

	_, err = collection.InsertMany(ctx, documents)
	if err != nil {
		return fmt.Errorf("failed to seed questions: %w", err)
	}

	as.logger.Info("Successfully seeded default questionnaire questions", zap.Int("count", len(defaultQuestions)))
	return nil
}
