package questionnaire

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type RecommendationService struct {
	db     *mongodb.MongoDBClient
	logger *zap.Logger
}

func NewRecommendationService(db *mongodb.MongoDBClient, logger *zap.Logger) *RecommendationService {
	return &RecommendationService{
		db:     db,
		logger: logger,
	}
}

// GetRecommendations retrieves property recommendations for a completed session
func (rs *RecommendationService) GetRecommendations(ctx context.Context, sessionID primitive.ObjectID) (*models.RecommendationsResponse, error) {
	// Get session
	session, err := rs.getSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is completed
	if session.Status != "completed" {
		return nil, fmt.Errorf("session is not completed: %s", session.Status)
	}

	// Get recommendation result
	recommendation, err := rs.getRecommendationResult(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get recommendation result: %w", err)
	}

	// Get properties details
	properties, err := rs.getPropertiesDetails(ctx, recommendation.Properties)
	if err != nil {
		return nil, fmt.Errorf("failed to get properties details: %w", err)
	}

	// Calculate match scores for properties
	propertyRecommendations := rs.calculateMatchScores(properties, recommendation.Criteria)

	// Analyze user preferences
	userPreferences := rs.buildUserPreferences(recommendation.Criteria)

	// Build filters applied
	filtersApplied := rs.buildFiltersApplied(userPreferences)

	return &models.RecommendationsResponse{
		RecommendationID: recommendation.ID,
		UserPreferences:  userPreferences,
		Properties:       propertyRecommendations,
		TotalMatches:     len(propertyRecommendations),
		Showing:          len(propertyRecommendations),
		FiltersApplied:   filtersApplied,
		GeneratedAt:      recommendation.CreatedAt,
	}, nil
}

// GetRecommendationByID retrieves a specific recommendation by ID
func (rs *RecommendationService) GetRecommendationByID(ctx context.Context, recommendationID primitive.ObjectID) (*models.RecommendationsResponse, error) {
	// Get recommendation result
	recommendation, err := rs.getRecommendationResultByID(ctx, recommendationID)
	if err != nil {
		return nil, fmt.Errorf("failed to get recommendation result: %w", err)
	}

	// Get properties details
	properties, err := rs.getPropertiesDetails(ctx, recommendation.Properties)
	if err != nil {
		return nil, fmt.Errorf("failed to get properties details: %w", err)
	}

	// Calculate match scores for properties
	propertyRecommendations := rs.calculateMatchScores(properties, recommendation.Criteria)

	// Analyze user preferences
	userPreferences := rs.buildUserPreferences(recommendation.Criteria)

	// Build filters applied
	filtersApplied := rs.buildFiltersApplied(userPreferences)

	return &models.RecommendationsResponse{
		RecommendationID: recommendation.ID,
		UserPreferences:  userPreferences,
		Properties:       propertyRecommendations,
		TotalMatches:     len(propertyRecommendations),
		Showing:          len(propertyRecommendations),
		FiltersApplied:   filtersApplied,
		GeneratedAt:      recommendation.CreatedAt,
	}, nil
}

// Helper methods

func (rs *RecommendationService) getSession(ctx context.Context, sessionID primitive.ObjectID) (*models.QuestionnaireSession, error) {
	collection := rs.db.GetCollection("questionnaire_sessions")
	filter := bson.M{"_id": sessionID}

	var session models.QuestionnaireSession
	err := collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		return nil, err
	}

	return &session, nil
}

func (rs *RecommendationService) getRecommendationResult(ctx context.Context, sessionID primitive.ObjectID) (*models.RecommendationResult, error) {
	collection := rs.db.GetCollection("recommendation_results")
	filter := bson.M{"session_id": sessionID}

	var recommendation models.RecommendationResult
	err := collection.FindOne(ctx, filter).Decode(&recommendation)
	if err != nil {
		return nil, err
	}

	return &recommendation, nil
}

func (rs *RecommendationService) getRecommendationResultByID(ctx context.Context, recommendationID primitive.ObjectID) (*models.RecommendationResult, error) {
	collection := rs.db.GetCollection("recommendation_results")
	filter := bson.M{"_id": recommendationID}

	var recommendation models.RecommendationResult
	err := collection.FindOne(ctx, filter).Decode(&recommendation)
	if err != nil {
		return nil, err
	}

	return &recommendation, nil
}

func (rs *RecommendationService) getPropertiesDetails(ctx context.Context, propertyIDs []primitive.ObjectID) ([]models.Property, error) {
	if len(propertyIDs) == 0 {
		return []models.Property{}, nil
	}

	collection := rs.db.GetCollection("properties")
	filter := bson.M{
		"_id":                bson.M{"$in": propertyIDs},
		"is_property_active": true,
	}

	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var properties []models.Property
	if err = cursor.All(ctx, &properties); err != nil {
		return nil, err
	}

	return properties, nil
}

func (rs *RecommendationService) calculateMatchScores(properties []models.Property, criteria map[string]interface{}) []models.PropertyRecommendation {
	var recommendations []models.PropertyRecommendation

	for _, property := range properties {
		score := rs.calculatePropertyScore(property, criteria)

		recommendation := models.PropertyRecommendation{
			Property:   property,
			MatchScore: score,
		}

		recommendations = append(recommendations, recommendation)
	}

	return recommendations
}

func (rs *RecommendationService) calculatePropertyScore(property models.Property, criteria map[string]interface{}) int {
	score := 0
	maxScore := 0

	// Property type match (20 points)
	maxScore += 20
	if propertyType, ok := criteria["property_type"].(string); ok {
		if strings.EqualFold(property.Type, propertyType) {
			score += 20
		}
	}

	// City match (20 points)
	maxScore += 20
	if city, ok := criteria["city"].(string); ok {
		if strings.EqualFold(property.Location.City, city) {
			score += 20
		}
	}

	// Area match (15 points)
	maxScore += 15
	if area, ok := criteria["area"].(string); ok {
		if strings.EqualFold(property.Location.Area, area) {
			score += 15
		}
	}

	// BHK match (15 points)
	maxScore += 15
	if bhk, ok := criteria["bhk"].(string); ok {
		propertyBHK := strconv.Itoa(property.BHK)
		if bhk == propertyBHK || (bhk == "4+" && property.BHK >= 4) {
			score += 15
		}
	}

	// Status match (15 points)
	maxScore += 15
	if status, ok := criteria["status"].(string); ok {
		if (status == "buy" && (property.Status == "sell" || property.Status == "sold")) ||
			(status == "rent" && (property.Status == "rent" || property.Status == "rented")) {
			score += 15
		}
	}

	// Furnishing match (10 points)
	maxScore += 10
	if furnishing, ok := criteria["furnishing"].(string); ok {
		if strings.EqualFold(property.Furniture, furnishing) {
			score += 10
		}
	}

	// Budget match (5 points)
	maxScore += 5
	if budgetRange, ok := criteria["budget_range"].(string); ok {
		if rs.isPropertyInBudget(property.TotalPrice, budgetRange) {
			score += 5
		}
	}

	// Convert to percentage
	if maxScore > 0 {
		return (score * 100) / maxScore
	}

	return 75 // Default score
}

func (rs *RecommendationService) isPropertyInBudget(price float64, budgetRange string) bool {
	if !strings.Contains(budgetRange, "-") {
		return true
	}

	parts := strings.Split(budgetRange, "-")
	if len(parts) != 2 {
		return true
	}

	minPrice, err1 := strconv.ParseFloat(parts[0], 64)
	maxPrice, err2 := strconv.ParseFloat(parts[1], 64)

	if err1 != nil || err2 != nil {
		return true
	}

	return price >= minPrice && price <= maxPrice
}

func (rs *RecommendationService) buildUserPreferences(criteria map[string]interface{}) models.UserPreferences {
	preferences := models.UserPreferences{}

	if budgetRange, ok := criteria["budget_range"].(string); ok {
		preferences.BudgetRange = budgetRange
	}

	if propertyType, ok := criteria["property_type"].(string); ok {
		preferences.PropertyType = propertyType
	}

	if city, ok := criteria["city"].(string); ok {
		preferences.City = city
	}

	if area, ok := criteria["area"].(string); ok {
		preferences.Area = area
	}

	if bhk, ok := criteria["bhk"].(string); ok {
		preferences.BHK = bhk
	}

	if status, ok := criteria["status"].(string); ok {
		preferences.Status = status
	}

	if furnishing, ok := criteria["furnishing"].(string); ok {
		preferences.Furnishing = furnishing
	}

	// Handle amenities (could be array or string)
	if amenities, ok := criteria["amenities"]; ok {
		switch v := amenities.(type) {
		case []interface{}:
			for _, amenity := range v {
				if amenityStr, ok := amenity.(string); ok {
					preferences.Amenities = append(preferences.Amenities, amenityStr)
				}
			}
		case []string:
			preferences.Amenities = v
		case string:
			preferences.Amenities = []string{v}
		}
	}

	return preferences
}

func (rs *RecommendationService) buildFiltersApplied(preferences models.UserPreferences) map[string]string {
	filters := make(map[string]string)

	if preferences.BudgetRange != "" {
		filters["budget"] = rs.formatBudgetRange(preferences.BudgetRange)
	}

	if preferences.PropertyType != "" {
		filters["type"] = strings.Title(preferences.PropertyType)
	}

	if preferences.City != "" && preferences.Area != "" {
		filters["location"] = fmt.Sprintf("%s, %s", strings.Title(preferences.City), strings.Title(preferences.Area))
	} else if preferences.City != "" {
		filters["location"] = strings.Title(preferences.City)
	}

	if preferences.BHK != "" {
		if preferences.BHK == "4+" {
			filters["bhk"] = "4BHK+"
		} else {
			filters["bhk"] = preferences.BHK + "BHK"
		}
	}

	if preferences.Status != "" {
		if preferences.Status == "buy" {
			filters["purpose"] = "Buy"
		} else if preferences.Status == "rent" {
			filters["purpose"] = "Rent"
		}
	}

	if preferences.Furnishing != "" {
		filters["furnishing"] = strings.Title(preferences.Furnishing)
	}

	return filters
}

func (rs *RecommendationService) formatBudgetRange(budgetRange string) string {
	if !strings.Contains(budgetRange, "-") {
		return budgetRange
	}

	parts := strings.Split(budgetRange, "-")
	if len(parts) != 2 {
		return budgetRange
	}

	min, err1 := strconv.ParseFloat(parts[0], 64)
	max, err2 := strconv.ParseFloat(parts[1], 64)

	if err1 != nil || err2 != nil {
		return budgetRange
	}

	return fmt.Sprintf("₹%s - ₹%s", rs.formatCurrency(min), rs.formatCurrency(max))
}

func (rs *RecommendationService) formatCurrency(amount float64) string {
	if amount >= 10000000 { // 1 Crore
		return fmt.Sprintf("%.1fCr", amount/10000000)
	} else if amount >= 100000 { // 1 Lakh
		return fmt.Sprintf("%.0fL", amount/100000)
	}
	return fmt.Sprintf("%.0f", amount)
}
