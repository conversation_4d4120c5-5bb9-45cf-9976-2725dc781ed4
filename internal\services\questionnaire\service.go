package questionnaire

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type Service struct {
	db     *mongodb.MongoDBClient
	logger *zap.Logger
}

func NewService(db *mongodb.MongoDBClient, logger *zap.Logger) *Service {
	return &Service{
		db:     db,
		logger: logger,
	}
}

// StartSession creates a new questionnaire session for a user
func (s *Service) StartSession(ctx context.Context, userID primitive.ObjectID) (*models.StartQuestionnaireResponse, error) {
	// Check if user has an active session
	existingSession, err := s.getActiveSession(ctx, userID)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, fmt.Errorf("failed to check existing session: %w", err)
	}

	// If active session exists, terminate it
	if existingSession != nil {
		err = s.terminateSession(ctx, existingSession.ID, "new_session_started")
		if err != nil {
			s.logger.Warn("Failed to terminate existing session", zap.Error(err))
		}
	}

	// Get total questions count
	totalQuestions, err := s.getTotalQuestionsCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total questions count: %w", err)
	}

	// Create new session
	session := &models.QuestionnaireSession{
		ID:              primitive.NewObjectID(),
		UserID:          userID,
		CurrentQuestion: 1,
		Status:          "active",
		Responses:       []models.UserResponse{},
		StartedAt:       time.Now(),
		LastActivity:    time.Now(),
	}

	collection := s.db.GetCollection("questionnaire_sessions")
	_, err = collection.InsertOne(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	return &models.StartQuestionnaireResponse{
		SessionID:       session.ID,
		Status:          session.Status,
		CurrentQuestion: session.CurrentQuestion,
		TotalQuestions:  totalQuestions,
		StartedAt:       session.StartedAt,
	}, nil
}

// GetCurrentQuestion retrieves the current question for a session
func (s *Service) GetCurrentQuestion(ctx context.Context, sessionID primitive.ObjectID) (*models.CurrentQuestionResponse, error) {
	// Get session
	session, err := s.getSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is active
	if session.Status != "active" {
		return nil, fmt.Errorf("session is not active: %s", session.Status)
	}

	// Check session timeout (30 minutes)
	if time.Since(session.LastActivity) > 30*time.Minute {
		err = s.terminateSession(ctx, sessionID, "session_timeout")
		if err != nil {
			s.logger.Error("Failed to terminate expired session", zap.Error(err))
		}
		return nil, fmt.Errorf("session has expired")
	}

	// Get current question
	question, err := s.getQuestionByOrder(ctx, session.CurrentQuestion)
	if err != nil {
		return nil, fmt.Errorf("failed to get question: %w", err)
	}

	// Get total questions count
	totalQuestions, err := s.getTotalQuestionsCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total questions count: %w", err)
	}

	// Calculate progress
	progress := s.calculateProgress(session.CurrentQuestion, totalQuestions)

	// Build response
	response := &models.CurrentQuestionResponse{
		Question: *question,
		Session: models.SessionInfo{
			ID:              session.ID,
			Status:          session.Status,
			CurrentQuestion: session.CurrentQuestion,
			TotalQuestions:  totalQuestions,
			ResponsesCount:  len(session.Responses),
		},
		Progress: progress,
	}

	return response, nil
}

// SubmitAnswer submits an answer for the current question
func (s *Service) SubmitAnswer(ctx context.Context, sessionID primitive.ObjectID, questionID primitive.ObjectID, answer interface{}) (*models.SubmitAnswerResponse, error) {
	// Get session
	session, err := s.getSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is active
	if session.Status != "active" {
		return nil, fmt.Errorf("session is not active: %s", session.Status)
	}

	// Get question
	question, err := s.getQuestion(ctx, questionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get question: %w", err)
	}

	// Validate that this is the current question
	if question.Order != session.CurrentQuestion {
		return nil, fmt.Errorf("invalid question order: expected %d, got %d", session.CurrentQuestion, question.Order)
	}

	// Validate answer
	isValid, reason := s.validateAnswer(question, answer)
	if !isValid {
		// Terminate session for inappropriate answers
		err = s.terminateSession(ctx, sessionID, reason)
		if err != nil {
			s.logger.Error("Failed to terminate session", zap.Error(err))
		}
		return nil, fmt.Errorf("inappropriate answer: %s", reason)
	}

	// Add response to session
	userResponse := models.UserResponse{
		QuestionID: questionID,
		Answer:     answer,
		AnsweredAt: time.Now(),
	}

	// Update session with new response
	err = s.updateSessionWithResponse(ctx, sessionID, userResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to update session: %w", err)
	}

	// Get total questions count
	totalQuestions, err := s.getTotalQuestionsCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total questions count: %w", err)
	}

	// Check if this was the last question
	isLastQuestion := session.CurrentQuestion >= totalQuestions
	nextQuestionAvailable := !isLastQuestion

	// Prepare response
	response := &models.SubmitAnswerResponse{
		AnswerAccepted:         true,
		NextQuestionAvailable:  nextQuestionAvailable,
		QuestionnaireCompleted: isLastQuestion,
		CurrentQuestion:        session.CurrentQuestion + 1,
		TotalQuestions:         totalQuestions,
	}

	if isLastQuestion {
		response.CompletionStatus = "ready_for_recommendations"
	}

	return response, nil
}

// CompleteQuestionnaire marks the questionnaire as completed and generates recommendations
func (s *Service) CompleteQuestionnaire(ctx context.Context, sessionID primitive.ObjectID) (*models.CompleteQuestionnaireResponse, error) {
	// Get session
	session, err := s.getSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is active
	if session.Status != "active" {
		return nil, fmt.Errorf("session is not active: %s", session.Status)
	}

	// Get total questions count
	totalQuestions, err := s.getTotalQuestionsCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total questions count: %w", err)
	}

	// Check if all questions are answered
	if len(session.Responses) < totalQuestions {
		return nil, fmt.Errorf("not all questions are answered: %d/%d", len(session.Responses), totalQuestions)
	}

	// Mark session as completed
	completedAt := time.Now()
	err = s.markSessionCompleted(ctx, sessionID, completedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to mark session as completed: %w", err)
	}

	// Generate recommendations
	recommendationID, err := s.generateRecommendations(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to generate recommendations: %w", err)
	}

	return &models.CompleteQuestionnaireResponse{
		SessionID:        sessionID,
		Status:           "completed",
		CompletedAt:      completedAt,
		RecommendationID: recommendationID,
		TotalResponses:   len(session.Responses),
	}, nil
}

// GetSessionStatus retrieves the current status of a session
func (s *Service) GetSessionStatus(ctx context.Context, sessionID primitive.ObjectID) (*models.SessionStatusResponse, error) {
	session, err := s.getSession(ctx, sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	totalQuestions, err := s.getTotalQuestionsCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total questions count: %w", err)
	}

	remainingQuestions := totalQuestions - len(session.Responses)
	isComplete := session.Status == "completed"
	nextAction := "answer_current_question"
	if isComplete {
		nextAction = "view_recommendations"
	} else if remainingQuestions == 0 {
		nextAction = "complete_questionnaire"
	}

	return &models.SessionStatusResponse{
		SessionID:          session.ID,
		Status:             session.Status,
		CurrentQuestion:    session.CurrentQuestion,
		TotalQuestions:     totalQuestions,
		ResponsesCount:     len(session.Responses),
		RemainingQuestions: remainingQuestions,
		IsComplete:         isComplete,
		NextAction:         nextAction,
		LastActivity:       session.LastActivity,
	}, nil
}

// TerminateSession manually terminates an active questionnaire session
func (s *Service) TerminateSession(ctx context.Context, sessionID primitive.ObjectID) error {
	// Get session first to check if it exists and is active
	session, err := s.getSession(ctx, sessionID)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}

	// Check if session is already terminated or completed
	if session.Status == "terminated" {
		return fmt.Errorf("session is already terminated")
	}
	if session.Status == "completed" {
		return fmt.Errorf("cannot terminate a completed session")
	}

	// Terminate the session
	err = s.terminateSession(ctx, sessionID, "manual_termination")
	if err != nil {
		return fmt.Errorf("failed to terminate session: %w", err)
	}

	return nil
}

// Helper methods

func (s *Service) getActiveSession(ctx context.Context, userID primitive.ObjectID) (*models.QuestionnaireSession, error) {
	collection := s.db.GetCollection("questionnaire_sessions")
	filter := bson.M{
		"user_id": userID,
		"status":  "active",
	}

	var session models.QuestionnaireSession
	err := collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		return nil, err
	}

	return &session, nil
}

func (s *Service) getSession(ctx context.Context, sessionID primitive.ObjectID) (*models.QuestionnaireSession, error) {
	collection := s.db.GetCollection("questionnaire_sessions")
	filter := bson.M{"_id": sessionID}

	var session models.QuestionnaireSession
	err := collection.FindOne(ctx, filter).Decode(&session)
	if err != nil {
		return nil, err
	}

	return &session, nil
}

func (s *Service) getQuestion(ctx context.Context, questionID primitive.ObjectID) (*models.Question, error) {
	collection := s.db.GetCollection("questions")
	filter := bson.M{
		"_id":       questionID,
		"is_active": true,
	}

	var question models.Question
	err := collection.FindOne(ctx, filter).Decode(&question)
	if err != nil {
		return nil, err
	}

	return &question, nil
}

func (s *Service) getQuestionByOrder(ctx context.Context, order int) (*models.Question, error) {
	collection := s.db.GetCollection("questions")
	filter := bson.M{
		"order":     order,
		"is_active": true,
	}

	var question models.Question
	err := collection.FindOne(ctx, filter).Decode(&question)
	if err != nil {
		return nil, err
	}

	return &question, nil
}

func (s *Service) getTotalQuestionsCount(ctx context.Context) (int, error) {
	collection := s.db.GetCollection("questions")
	filter := bson.M{"is_active": true}

	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return int(count), nil
}

func (s *Service) calculateProgress(current, total int) models.QuestionProgress {
	percentage := float64(current-1) / float64(total) * 100
	if percentage < 0 {
		percentage = 0
	}

	isLastQuestion := current >= total
	nextAction := "answer_current_question"
	if isLastQuestion {
		nextAction = "complete_questionnaire"
		percentage = 100
	}

	return models.QuestionProgress{
		Current:        current,
		Total:          total,
		Percentage:     percentage,
		IsLastQuestion: isLastQuestion,
		NextAction:     nextAction,
	}
}

func (s *Service) validateAnswer(question *models.Question, answer interface{}) (bool, string) {
	// Convert answer to string for validation
	answerStr := fmt.Sprintf("%v", answer)

	// Check for forbidden words
	for _, word := range question.Validation.ForbiddenWords {
		if strings.Contains(strings.ToLower(answerStr), strings.ToLower(word)) {
			return false, "inappropriate_content"
		}
	}

	// Check for inappropriate patterns
	inappropriatePatterns := []string{
		`(?i)\b(fuck|shit|damn|hell|bitch|asshole|stupid|idiot|moron)\b`,
		`(?i)\b(cheap and dirty|don't care|whatever|anything)\b`,
		`(?i)\b(spam|test|asdf|qwerty|123456)\b`,
	}

	for _, pattern := range inappropriatePatterns {
		matched, _ := regexp.MatchString(pattern, answerStr)
		if matched {
			return false, "inappropriate_content"
		}
	}

	// Validate based on question type
	switch question.Type {
	case "single_choice":
		return s.validateSingleChoice(question, answerStr)
	case "multiple_choice":
		return s.validateMultipleChoice(question, answer)
	case "range":
		return s.validateRange(question, answer)
	case "text":
		return s.validateText(question, answerStr)
	}

	return true, ""
}

func (s *Service) validateSingleChoice(question *models.Question, answer string) (bool, string) {
	for _, option := range question.Options {
		if option.Value == answer {
			return true, ""
		}
	}
	return false, "invalid_option"
}

func (s *Service) validateMultipleChoice(question *models.Question, answer interface{}) (bool, string) {
	// Answer should be an array of strings
	answers, ok := answer.([]interface{})
	if !ok {
		return false, "invalid_format"
	}

	validOptions := make(map[string]bool)
	for _, option := range question.Options {
		validOptions[option.Value] = true
	}

	for _, ans := range answers {
		ansStr := fmt.Sprintf("%v", ans)
		if !validOptions[ansStr] {
			return false, "invalid_option"
		}
	}

	return true, ""
}

func (s *Service) validateRange(question *models.Question, answer interface{}) (bool, string) {
	// Answer should be a number or range string
	answerStr := fmt.Sprintf("%v", answer)

	// Check if it's a valid range format (e.g., "1000000-2500000")
	rangePattern := `^\d+-\d+$`
	matched, _ := regexp.MatchString(rangePattern, answerStr)
	if matched {
		return true, ""
	}

	return false, "invalid_range_format"
}

func (s *Service) validateText(question *models.Question, answer string) (bool, string) {
	// Check length constraints
	if question.Validation.MinLength != nil && len(answer) < *question.Validation.MinLength {
		return false, "answer_too_short"
	}
	if question.Validation.MaxLength != nil && len(answer) > *question.Validation.MaxLength {
		return false, "answer_too_long"
	}

	// Check for meaningful content (not just spaces or repeated characters)
	trimmed := strings.TrimSpace(answer)
	if len(trimmed) == 0 {
		return false, "empty_answer"
	}

	// Check for repeated characters (like "aaaaaaa")
	if len(trimmed) > 3 {
		firstChar := trimmed[0]
		allSame := true
		for _, char := range trimmed {
			if char != rune(firstChar) {
				allSame = false
				break
			}
		}
		if allSame {
			return false, "invalid_content"
		}
	}

	return true, ""
}

func (s *Service) terminateSession(ctx context.Context, sessionID primitive.ObjectID, reason string) error {
	collection := s.db.GetCollection("questionnaire_sessions")
	terminatedAt := time.Now()

	update := bson.M{
		"$set": bson.M{
			"status":             "terminated",
			"terminated_at":      terminatedAt,
			"termination_reason": reason,
			"last_activity":      terminatedAt,
		},
	}

	_, err := collection.UpdateOne(ctx, bson.M{"_id": sessionID}, update)
	return err
}

func (s *Service) updateSessionWithResponse(ctx context.Context, sessionID primitive.ObjectID, response models.UserResponse) error {
	collection := s.db.GetCollection("questionnaire_sessions")

	update := bson.M{
		"$push": bson.M{"responses": response},
		"$inc":  bson.M{"current_question": 1},
		"$set":  bson.M{"last_activity": time.Now()},
	}

	_, err := collection.UpdateOne(ctx, bson.M{"_id": sessionID}, update)
	return err
}

func (s *Service) markSessionCompleted(ctx context.Context, sessionID primitive.ObjectID, completedAt time.Time) error {
	collection := s.db.GetCollection("questionnaire_sessions")

	update := bson.M{
		"$set": bson.M{
			"status":        "completed",
			"completed_at":  completedAt,
			"last_activity": completedAt,
		},
	}

	_, err := collection.UpdateOne(ctx, bson.M{"_id": sessionID}, update)
	return err
}

func (s *Service) generateRecommendations(ctx context.Context, session *models.QuestionnaireSession) (primitive.ObjectID, error) {
	// Analyze user preferences from responses
	preferences := s.analyzeUserPreferences(session.Responses)

	// Find matching properties (simplified for now)
	properties, err := s.findMatchingProperties(ctx, preferences)
	if err != nil {
		return primitive.NilObjectID, fmt.Errorf("failed to find matching properties: %w", err)
	}

	// Create recommendation result
	recommendation := &models.RecommendationResult{
		ID:         primitive.NewObjectID(),
		SessionID:  session.ID,
		UserID:     session.UserID,
		Properties: properties,
		Score:      85.0, // Default score
		Criteria:   preferences,
		CreatedAt:  time.Now(),
	}

	collection := s.db.GetCollection("recommendation_results")
	_, err = collection.InsertOne(ctx, recommendation)
	if err != nil {
		return primitive.NilObjectID, fmt.Errorf("failed to save recommendation: %w", err)
	}

	return recommendation.ID, nil
}

func (s *Service) analyzeUserPreferences(responses []models.UserResponse) map[string]interface{} {
	preferences := make(map[string]interface{})

	// Map responses to preferences based on question order
	for i, response := range responses {
		switch i + 1 { // Question order starts from 1
		case 1: // Budget
			preferences["budget_range"] = response.Answer
		case 2: // Property Type
			preferences["property_type"] = response.Answer
		case 3: // City
			preferences["city"] = response.Answer
		case 4: // Area
			preferences["area"] = response.Answer
		case 5: // BHK
			preferences["bhk"] = response.Answer
		case 6: // Amenities
			preferences["amenities"] = response.Answer
		case 7: // Buy/Rent
			preferences["status"] = response.Answer
		case 8: // Furnishing
			preferences["furnishing"] = response.Answer
		}
	}

	return preferences
}

func (s *Service) findMatchingProperties(ctx context.Context, preferences map[string]interface{}) ([]primitive.ObjectID, error) {
	collection := s.db.GetCollection("properties")

	// Build filter based on preferences
	filter := bson.M{
		"is_property_active":   true,
		"is_property_verified": true,
	}

	// Add filters based on preferences
	if propertyType, ok := preferences["property_type"].(string); ok {
		filter["type"] = propertyType
	}

	if city, ok := preferences["city"].(string); ok {
		filter["location.city"] = city
	}

	if area, ok := preferences["area"].(string); ok {
		filter["location.area"] = area
	}

	if status, ok := preferences["status"].(string); ok {
		// Map buy/rent to property status
		if status == "buy" {
			filter["status"] = bson.M{"$in": []string{"sell", "sold"}}
		} else if status == "rent" {
			filter["status"] = bson.M{"$in": []string{"rent", "rented"}}
		}
	}

	if bhk, ok := preferences["bhk"].(string); ok {
		// Convert BHK string to number
		switch bhk {
		case "1":
			filter["bhk"] = 1
		case "2":
			filter["bhk"] = 2
		case "3":
			filter["bhk"] = 3
		case "4+":
			filter["bhk"] = bson.M{"$gte": 4}
		}
	}

	// Add budget filter if present
	if budgetRange, ok := preferences["budget_range"].(string); ok {
		if strings.Contains(budgetRange, "-") {
			parts := strings.Split(budgetRange, "-")
			if len(parts) == 2 {
				// Parse min and max values
				// This is a simplified implementation
				filter["total_price"] = bson.M{
					"$gte": 1000000,  // Default min
					"$lte": 50000000, // Default max
				}
			}
		}
	}

	// Find properties with limit
	opts := options.Find().SetLimit(20).SetSort(bson.D{{Key: "created_at", Value: -1}})
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var propertyIDs []primitive.ObjectID
	for cursor.Next(ctx) {
		var property models.Property
		if err := cursor.Decode(&property); err != nil {
			continue
		}
		propertyIDs = append(propertyIDs, property.ID)
	}

	return propertyIDs, nil
}
