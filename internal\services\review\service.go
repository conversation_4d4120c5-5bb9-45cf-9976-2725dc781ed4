package review

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

var (
	ErrReviewNotFound   = errors.New("review not found")
	ErrPropertyNotFound = errors.New("property not found")
	ErrUserNotFound     = errors.New("user not found")
	ErrAlreadyLiked     = errors.New("already liked this review")
	ErrAlreadyDisliked  = errors.New("already disliked this review")
	ErrNotLiked         = errors.New("not liked this review")
	ErrNotDisliked      = errors.New("not disliked this review")
)

type Service struct {
	db *mongodb.MongoDBClient
}

func NewService(db *mongodb.MongoDBClient) *Service {
	return &Service{db: db}
}

// CreateReview adds a new review for a property
func (s *Service) CreateReview(ctx context.Context, review *models.Review) error {
	// Check if property exists
	propertyCollection := s.db.GetCollection("properties")
	var property models.Property
	err := propertyCollection.FindOne(ctx, bson.M{"_id": review.PropertyID}).Decode(&property)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrPropertyNotFound
		}
		return err
	}

	// Check if user exists
	userCollection := s.db.GetCollection("users")
	var user models.User
	err = userCollection.FindOne(ctx, bson.M{"_id": review.UserID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrUserNotFound
		}
		return err
	}

	// Set initial values
	review.Likes = 0
	review.DisLikes = 0
	review.IsActive = true
	review.CreatedAt = time.Now()
	review.UpdatedAt = time.Now()

	// Insert review
	reviewCollection := s.db.GetCollection("reviews")
	result, err := reviewCollection.InsertOne(ctx, review)
	if err != nil {
		return err
	}

	review.ID = result.InsertedID.(primitive.ObjectID)

	// Update property rating
	return s.updatePropertyRating(ctx, review.PropertyID)
}

// GetReview retrieves a review by ID
func (s *Service) GetReview(ctx context.Context, id primitive.ObjectID) (*models.Review, error) {
	reviewCollection := s.db.GetCollection("reviews")

	var review models.Review
	err := reviewCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&review)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrReviewNotFound
		}
		return nil, err
	}

	return &review, nil
}

// UpdateReview updates an existing review
func (s *Service) UpdateReview(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	reviewCollection := s.db.GetCollection("reviews")

	// Add updated timestamp
	update["updated_at"] = time.Now()

	result, err := reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return ErrReviewNotFound
	}

	// Get the property ID for the review
	var review models.Review
	err = reviewCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&review)
	if err != nil {
		return err
	}

	// Update property rating
	return s.updatePropertyRating(ctx, review.PropertyID)
}

// DeleteReview removes a review
func (s *Service) DeleteReview(ctx context.Context, id primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")

	// Get the property ID for the review before deleting
	var review models.Review
	err := reviewCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&review)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrReviewNotFound
		}
		return err
	}

	// Delete the review
	result, err := reviewCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return ErrReviewNotFound
	}

	// Update property rating
	return s.updatePropertyRating(ctx, review.PropertyID)
}

// ListReviewsByPropertyID retrieves all reviews for a property with optimized like status checking
// If isAdmin is true, it returns all reviews; otherwise, it returns only active reviews
// If userID is provided, it includes like status for that user using a single optimized query
func (s *Service) ListReviewsByPropertyID(ctx context.Context, propertyID primitive.ObjectID, page, limit int64, isAdmin bool, userID *primitive.ObjectID) ([]*models.ReviewWithLikeStatus, int64, error) {
	reviewCollection := s.db.GetCollection("reviews")

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Create filter based on user role
	filter := bson.M{"property_id": propertyID}
	if !isAdmin {
		// For non-admin users, only show active reviews
		filter["is_active"] = true
	}

	// Find all reviews for this property with pagination
	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"created_at": -1}) // Sort by creation date, newest first

	cursor, err := reviewCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode reviews
	var reviews []*models.Review
	if err := cursor.All(ctx, &reviews); err != nil {
		return nil, 0, err
	}

	// Convert to ReviewWithLikeStatus and get like status in batch (OPTIMIZED)
	return s.addLikeStatusBatch(ctx, reviews, userID)
}

// checkUserReviewStatus checks if a user has liked or disliked a specific review
func (s *Service) checkUserReviewStatus(ctx context.Context, reviewID, userID primitive.ObjectID) (bool, bool, error) {
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check for like
	likeCount, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "like",
	})
	if err != nil {
		return false, false, err
	}

	// Check for dislike
	dislikeCount, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "dislike",
	})
	if err != nil {
		return false, false, err
	}

	return likeCount > 0, dislikeCount > 0, nil
}

// addLikeStatusBatch efficiently adds like status to reviews using a single batch query
// This is optimized for high traffic by reducing database calls from N to 1
func (s *Service) addLikeStatusBatch(ctx context.Context, reviews []*models.Review, userID *primitive.ObjectID) ([]*models.ReviewWithLikeStatus, int64, error) {
	total := int64(len(reviews))

	// Convert to ReviewWithLikeStatus with default values
	reviewsWithLikeStatus := make([]*models.ReviewWithLikeStatus, len(reviews))
	reviewIDs := make([]primitive.ObjectID, len(reviews))

	for i, review := range reviews {
		reviewsWithLikeStatus[i] = &models.ReviewWithLikeStatus{
			ID:         review.ID,
			UserID:     review.UserID,
			PropertyID: review.PropertyID,
			UserName:   review.UserName,
			Rating:     review.Rating,
			Comment:    review.Comment,
			Likes:      review.Likes,
			DisLikes:   review.DisLikes,
			Photos:     review.Photos,
			IsActive:   review.IsActive,
			CreatedAt:  review.CreatedAt,
			UpdatedAt:  review.UpdatedAt,
			IsLiked:    false, // Default value
			IsDisliked: false, // Default value
		}
		reviewIDs[i] = review.ID
	}

	// If user is logged in, get all like statuses in a single batch query (OPTIMIZED)
	if userID != nil && len(reviewIDs) > 0 {
		reviewLikesCollection := s.db.GetCollection("review_likes")

		// Single query to get all likes/dislikes for this user and these reviews
		cursor, err := reviewLikesCollection.Find(ctx, bson.M{
			"review_id": bson.M{"$in": reviewIDs},
			"user_id":   *userID,
		})
		if err != nil {
			// If like status query fails, return reviews without like status (graceful degradation)
			return reviewsWithLikeStatus, total, nil
		}
		defer cursor.Close(ctx)

		// Create maps for fast lookup
		likedReviews := make(map[primitive.ObjectID]bool)
		dislikedReviews := make(map[primitive.ObjectID]bool)

		// Process all like records
		var likes []models.ReviewLike
		if err := cursor.All(ctx, &likes); err == nil {
			for _, like := range likes {
				if like.Type == "like" {
					likedReviews[like.ReviewID] = true
				} else if like.Type == "dislike" {
					dislikedReviews[like.ReviewID] = true
				}
			}
		}

		// Apply like status to reviews using fast map lookup
		for _, reviewWithStatus := range reviewsWithLikeStatus {
			reviewWithStatus.IsLiked = likedReviews[reviewWithStatus.ID]
			reviewWithStatus.IsDisliked = dislikedReviews[reviewWithStatus.ID]
		}
	}

	return reviewsWithLikeStatus, total, nil
}

// ListReviewsByUserIDAndPropertyID retrieves all reviews written by a specific user for a specific property
// If isAdmin is true, it returns all reviews; otherwise, it returns only active reviews
// Includes like status for the requesting user (which may be different from the review author)
func (s *Service) ListReviewsByUserIDAndPropertyID(ctx context.Context, reviewAuthorID primitive.ObjectID, propertyID primitive.ObjectID, page, limit int64, isAdmin bool, requestingUserID *primitive.ObjectID) ([]*models.ReviewWithLikeStatus, int64, error) {
	reviewCollection := s.db.GetCollection("reviews")

	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Create filter for specific user and property
	filter := bson.M{
		"user_id":     reviewAuthorID,
		"property_id": propertyID,
	}
	if !isAdmin {
		// For non-admin users, only show active reviews
		filter["is_active"] = true
	}

	// Get total count of reviews for this user and property based on filter
	total, err := reviewCollection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Find all reviews by this user for this property with pagination
	opts := options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"created_at": -1}) // Sort by creation date, newest first

	cursor, err := reviewCollection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode reviews
	var reviews []*models.Review
	if err := cursor.All(ctx, &reviews); err != nil {
		return nil, 0, err
	}

	// Convert to ReviewWithLikeStatus and get like status in batch (OPTIMIZED)
	// Note: requestingUserID is used for like status, not reviewAuthorID
	reviewsWithLikeStatus, _, err := s.addLikeStatusBatch(ctx, reviews, requestingUserID)
	if err != nil {
		return nil, 0, err
	}

	return reviewsWithLikeStatus, total, nil
}

// LikeReview adds a like to a review
func (s *Service) LikeReview(ctx context.Context, reviewID, userID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check if review exists
	var review models.Review
	err := reviewCollection.FindOne(ctx, bson.M{"_id": reviewID}).Decode(&review)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrReviewNotFound
		}
		return err
	}

	// Check if already liked
	count, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "like",
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrAlreadyLiked
	}

	// Check if already disliked, if so, remove the dislike
	result, err := reviewLikesCollection.DeleteOne(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "dislike",
	})
	if err != nil {
		return err
	}

	// If a dislike was removed, decrement the dislike count
	if result.DeletedCount > 0 {
		_, err = reviewCollection.UpdateOne(
			ctx,
			bson.M{"_id": reviewID},
			bson.M{"$inc": bson.M{"dislikes": -1}},
		)
		if err != nil {
			return err
		}
	}

	// Add like record
	like := models.ReviewLike{
		ReviewID:  reviewID,
		UserID:    userID,
		Type:      "like",
		CreatedAt: time.Now(),
	}

	_, err = reviewLikesCollection.InsertOne(ctx, like)
	if err != nil {
		return err
	}

	// Increment like count
	_, err = reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": reviewID},
		bson.M{"$inc": bson.M{"likes": 1}},
	)
	return err
}

// DislikeReview adds a dislike to a review
func (s *Service) DislikeReview(ctx context.Context, reviewID, userID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check if review exists
	var review models.Review
	err := reviewCollection.FindOne(ctx, bson.M{"_id": reviewID}).Decode(&review)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrReviewNotFound
		}
		return err
	}

	// Check if already disliked
	count, err := reviewLikesCollection.CountDocuments(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "dislike",
	})
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrAlreadyDisliked
	}

	// Check if already liked, if so, remove the like
	result, err := reviewLikesCollection.DeleteOne(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "like",
	})
	if err != nil {
		return err
	}

	// If a like was removed, decrement the like count
	if result.DeletedCount > 0 {
		_, err = reviewCollection.UpdateOne(
			ctx,
			bson.M{"_id": reviewID},
			bson.M{"$inc": bson.M{"likes": -1}},
		)
		if err != nil {
			return err
		}
	}

	// Add dislike record
	dislike := models.ReviewLike{
		ReviewID:  reviewID,
		UserID:    userID,
		Type:      "dislike",
		CreatedAt: time.Now(),
	}

	_, err = reviewLikesCollection.InsertOne(ctx, dislike)
	if err != nil {
		return err
	}

	// Increment dislike count
	_, err = reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": reviewID},
		bson.M{"$inc": bson.M{"dislikes": 1}},
	)
	return err
}

// RemoveLike removes a like from a review
func (s *Service) RemoveLike(ctx context.Context, reviewID, userID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check if liked
	result, err := reviewLikesCollection.DeleteOne(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "like",
	})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return ErrNotLiked
	}

	// Decrement like count
	_, err = reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": reviewID},
		bson.M{"$inc": bson.M{"likes": -1}},
	)
	return err
}

// RemoveDislike removes a dislike from a review
func (s *Service) RemoveDislike(ctx context.Context, reviewID, userID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")
	reviewLikesCollection := s.db.GetCollection("review_likes")

	// Check if disliked
	result, err := reviewLikesCollection.DeleteOne(ctx, bson.M{
		"review_id": reviewID,
		"user_id":   userID,
		"type":      "dislike",
	})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return ErrNotDisliked
	}

	// Decrement dislike count
	_, err = reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": reviewID},
		bson.M{"$inc": bson.M{"dislikes": -1}},
	)
	return err
}

// ToggleReviewActive toggles the active status of a review
func (s *Service) ToggleReviewActive(ctx context.Context, reviewID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")

	// Get the current review to check its active status
	var review models.Review
	err := reviewCollection.FindOne(ctx, bson.M{"_id": reviewID}).Decode(&review)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return ErrReviewNotFound
		}
		return err
	}

	// Toggle the active status
	_, err = reviewCollection.UpdateOne(
		ctx,
		bson.M{"_id": reviewID},
		bson.M{"$set": bson.M{
			"is_active":  !review.IsActive,
			"updated_at": time.Now(),
		}},
	)
	if err != nil {
		return err
	}

	return nil
}

// updatePropertyRating calculates and updates the average rating for a property
func (s *Service) updatePropertyRating(ctx context.Context, propertyID primitive.ObjectID) error {
	reviewCollection := s.db.GetCollection("reviews")
	propertyCollection := s.db.GetCollection("properties")

	// Calculate average rating
	pipeline := mongo.Pipeline{
		{{Key: "$match", Value: bson.M{"property_id": propertyID}}},
		{{Key: "$group", Value: bson.M{
			"_id":           nil,
			"averageRating": bson.M{"$avg": "$rating"},
			"count":         bson.M{"$sum": 1},
		}}},
	}

	cursor, err := reviewCollection.Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return err
	}

	// Update property rating
	var rating float64 = 0
	if len(results) > 0 {
		rating = results[0]["averageRating"].(float64)
	}

	_, err = propertyCollection.UpdateOne(
		ctx,
		bson.M{"_id": propertyID},
		bson.M{"$set": bson.M{"rating": rating}},
	)
	return err
}
