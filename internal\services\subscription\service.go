package subscription

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"realestate-platform/internal/models"
	"realestate-platform/internal/repository/mongodb"
)

type Service struct {
	db *mongodb.MongoDBClient
}

func NewService(db *mongodb.MongoDBClient) *Service {
	return &Service{db: db}
}

// CreateSubscriptionPackage creates a new subscription package
func (s *Service) CreateSubscriptionPackage(ctx context.Context, req *models.CreatePackageRequest) (*models.Package, error) {
	package_ := &models.Package{
		PackageId:      req.PackageId,
		Email:          req.Email,
		UserID:         req.UserID,
		PlanDuration:   req.PlanDuration,
		Description:    req.Description,
		PerWeekPrice:   req.PerWeekPrice,
		TotalPrice:     req.TotalPrice,
		Discount:       req.Discount,
		IsOffer:        req.<PERSON>Offer,
		IsPopular:      req.IsPop<PERSON>,
		PackageType:    req.PackageType,
		ProductPackage: req.ProductPackage,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	collection := s.db.GetCollection("subscription_packages")
	result, err := collection.InsertOne(ctx, package_)
	if err != nil {
		return nil, err
	}

	package_.ID = result.InsertedID.(primitive.ObjectID)
	return package_, nil
}

// GetSubscriptionPackage retrieves a subscription package by ID
func (s *Service) GetSubscriptionPackage(ctx context.Context, id primitive.ObjectID) (*models.Package, error) {
	collection := s.db.GetCollection("subscription_packages")

	var package_ models.Package
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(&package_)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, nil
		}
		return nil, err
	}

	return &package_, nil
}

// ListSubscriptionPackages retrieves all subscription packages with optional filtering
func (s *Service) ListSubscriptionPackages(ctx context.Context, filter bson.M, page, limit int64) ([]*models.Package, int64, error) {
	collection := s.db.GetCollection("subscription_packages")

	// Set up pagination
	opts := options.Find().
		SetSkip((page - 1) * limit).
		SetLimit(limit).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	// Execute query
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Decode results
	var packages []*models.Package
	if err = cursor.All(ctx, &packages); err != nil {
		return nil, 0, err
	}

	return packages, total, nil
}

// UpdateSubscriptionPackage updates an existing subscription package
func (s *Service) UpdateSubscriptionPackage(ctx context.Context, id primitive.ObjectID, req *models.UpdatePackageRequest) error {
	collection := s.db.GetCollection("subscription_packages")

	update := bson.M{
		"updated_at": time.Now(),
	}

	// Only update fields that are provided
	if req.PlanDuration != "" {
		update["plan_duration"] = req.PlanDuration
	}
	if req.Description != "" {
		update["description"] = req.Description
	}
	if req.PerWeekPrice != "" {
		update["per_week_price"] = req.PerWeekPrice
	}
	if req.TotalPrice != "" {
		update["total_price"] = req.TotalPrice
	}
	if req.Discount != 0 {
		update["discount"] = req.Discount
	}
	update["is_offer"] = req.IsOffer
	update["is_popular"] = req.IsPopular
	if req.PackageType != nil {
		update["package_type"] = req.PackageType
	}
	if req.Package != nil {
		update["product_package"] = req.Package
	}

	result, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": id},
		bson.M{"$set": update},
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// DeleteSubscriptionPackage deletes a subscription package
func (s *Service) DeleteSubscriptionPackage(ctx context.Context, id primitive.ObjectID) error {
	collection := s.db.GetCollection("subscription_packages")

	result, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	if result.DeletedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// GetPopularPackages retrieves popular subscription packages
func (s *Service) GetPopularPackages(ctx context.Context) ([]*models.Package, error) {
	collection := s.db.GetCollection("subscription_packages")

	filter := bson.M{"is_popular": true}
	opts := options.Find().
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var packages []*models.Package
	if err = cursor.All(ctx, &packages); err != nil {
		return nil, err
	}

	return packages, nil
}
