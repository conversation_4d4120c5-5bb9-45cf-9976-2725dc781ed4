package user

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/crypto/bcrypt"

	"realestate-platform/internal/models"
	"realestate-platform/internal/utils"
)

var (
	ErrUserExists         = errors.New("user already exists")
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrInvalidOTP         = errors.New("invalid OTP")
	ErrInvalidToken       = errors.New("invalid or expired token")
	ErrUserNotFound       = errors.New("user not found")
	ErrSamePassword       = errors.New("new password cannot be the same as the current password")
	ErrEmailNotVerified   = errors.New("email not verified")
)

type Service struct {
	db        *mongo.Database
	jwtSecret []byte
}

func NewService(db *mongo.Database, jwtSecret string) *Service {
	return &Service{
		db:        db,
		jwtSecret: []byte(jwtSecret),
	}
}

func (s *Service) Register(ctx context.Context, req *models.RegisterRequest) (*models.User, error) {
	// Check if user already exists
	var existingUser models.User

	// Check if email already exists
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": req.Email}).Decode(&existingUser)
	if err == nil {
		return nil, errors.New("a user with this email already exists")
	} else if err != mongo.ErrNoDocuments {
		return nil, err
	}

	// Check if first name + last name already exist together
	err = s.db.Collection("users").FindOne(ctx, bson.M{
		"first_name": req.FirstName,
		"last_name":  req.LastName,
	}).Decode(&existingUser)

	if err == nil {
		return nil, errors.New("a user with this full name already exists")
	} else if err != mongo.ErrNoDocuments {
		return nil, err
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Parse birth date if provided
	var birthDate time.Time
	if req.BirthDate != "" {
		birthDate, err = time.Parse("2006-01-02", req.BirthDate)
		if err != nil {
			return nil, errors.New("invalid birth date format. Use YYYY-MM-DD")
		}
	}

	// Set default role if not provided
	role := req.Role
	if role == "" {
		role = "user" // Default role
	}

	// Create new user
	user := &models.User{
		Email:                 req.Email,
		Password:              string(hashedPassword),
		FirstName:             req.FirstName,
		LastName:              req.LastName,
		Role:                  role,
		PhoneNumber:           req.PhoneNumber,
		BirthDate:             birthDate,
		IsProfileComplete:     false,
		IsSubscribedUser:      false,
		IsBlocked:             false,
		TotalListedProperties: 0,
		IsDocumentVerified:    false,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}

	// Insert user into database
	result, err := s.db.Collection("users").InsertOne(ctx, user)
	if err != nil {
		return nil, err
	}

	user.ID = result.InsertedID.(primitive.ObjectID)
	return user, nil
}

func (s *Service) Login(ctx context.Context, req *models.LoginRequest) (*models.AuthResponse, error) {
	// Find user by email
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": req.Email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrInvalidCredentials
		}
		return nil, err
	}

	// Compare passwords
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	// Check if user has completed email verification
	if user.SignupVerificationCode != "" {
		return nil, ErrEmailNotVerified
	}

	// Generate JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":    user.ID.Hex(),
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"role":       user.Role,
		"exp":        time.Now().Add(time.Hour * 24).Unix(), // Token expires in 24 hours
	})

	tokenString, err := token.SignedString(s.jwtSecret)
	if err != nil {
		return nil, err
	}

	return &models.AuthResponse{
		Token: tokenString,
		User:  user,
	}, nil
}

func (s *Service) ListUsers(ctx context.Context, filter bson.M, page, limit int64) ([]models.User, int64, error) {
	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Get total count
	total, err := s.db.Collection("users").CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Find users with pagination
	cursor, err := s.db.Collection("users").Find(ctx, filter, options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"created_at": -1})) // Sort by creation date, newest first
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode users
	var users []models.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// ListTopPerformingAgents retrieves subscribed users with at least one property listed,
// sorted by total listed properties in descending order
func (s *Service) ListTopPerformingAgents(ctx context.Context, page, limit int64) ([]models.User, int64, error) {
	// Calculate skip value for pagination
	skip := (page - 1) * limit

	// Build filter for subscribed users with at least one property listed
	filter := bson.M{
		"is_subscribed_user":      true,
		"total_listed_properties": bson.M{"$gt": 0}, // Only include users with at least one property listed
	}

	// Get total count of subscribed users with properties
	total, err := s.db.Collection("users").CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Find subscribed users with pagination, sorted by total listed properties in descending order
	cursor, err := s.db.Collection("users").Find(ctx, filter, options.Find().
		SetSkip(skip).
		SetLimit(limit).
		SetSort(bson.M{"total_listed_properties": -1})) // Sort by total listed properties, highest first
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	// Decode users
	var users []models.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

func (s *Service) GetUser(ctx context.Context, id primitive.ObjectID) (*models.User, error) {
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"_id": id}).Decode(&user)
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUserBlockStatus updates a user's blocked status
func (s *Service) UpdateUserBlockStatus(ctx context.Context, id primitive.ObjectID, isBlocked bool) error {
	update := bson.M{
		"$set": bson.M{
			"is_blocked": isBlocked,
			"updated_at": time.Now(),
		},
	}

	result, err := s.db.Collection("users").UpdateOne(
		ctx,
		bson.M{"_id": id},
		update,
	)
	if err != nil {
		return err
	}

	if result.MatchedCount == 0 {
		return mongo.ErrNoDocuments
	}

	return nil
}

// UpdateUserProfile updates a user's profile information
func (s *Service) UpdateUserProfile(ctx context.Context, id primitive.ObjectID, req *models.UpdateUserRequest) (*models.User, error) {
	// Create update document
	updateDoc := bson.M{}

	// Add fields to update only if they are provided
	if req.FirstName != "" {
		updateDoc["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updateDoc["last_name"] = req.LastName
	}
	if req.PhoneNumber != "" {
		updateDoc["phn_number"] = req.PhoneNumber
	}
	if req.BirthDate != "" {
		// Parse birth date
		birthDate, err := time.Parse("2006-01-02", req.BirthDate)
		if err != nil {
			return nil, errors.New("invalid birth date format. Use YYYY-MM-DD")
		}
		updateDoc["birth_date"] = birthDate
	}
	if req.ProfilePhoto != "" {
		updateDoc["profile_photo"] = req.ProfilePhoto
	}
	if req.Metadata != nil {
		updateDoc["metadata"] = req.Metadata
	}

	// Handle location update if provided
	if req.Location != nil {
		location := models.Location{
			Address: req.Location.Address,
			City:    req.Location.City,
			Area:    req.Location.Area,
			State:   req.Location.State,
			Country: req.Location.Country,
			ZipCode: req.Location.ZipCode,
		}

		// Only add coordinates if they are provided
		if req.Location.Latitude != 0 {
			location.Latitude = req.Location.Latitude
		}
		if req.Location.Longitude != 0 {
			location.Longitude = req.Location.Longitude
		}
		if req.Location.Neighborhood != "" {
			location.Neighborhood = req.Location.Neighborhood
		}

		updateDoc["location"] = location
	}

	// Always update profile completion status and timestamp
	updateDoc["is_profile_complete"] = req.IsProfileComplete
	updateDoc["updated_at"] = time.Now()

	// Create the update operation
	update := bson.M{
		"$set": updateDoc,
	}

	// Use FindOneAndUpdate to update and return the document in a single operation
	var updatedUser models.User
	err := s.db.Collection("users").FindOneAndUpdate(
		ctx,
		bson.M{"_id": id},
		update,
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&updatedUser)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	return &updatedUser, nil
}

// SendOTP generates and stores an OTP for the given email
func (s *Service) SendOTP(ctx context.Context, req *models.SendOTPRequest, emailJSConfig utils.EmailJSConfig) (*models.OTPResponse, error) {
	// Check if email already exists
	var existingUser models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": req.Email}).Decode(&existingUser)
	if err == nil {
		return nil, errors.New("a user with this email already exists")
	} else if err != mongo.ErrNoDocuments {
		return nil, err
	}

	// Check if first name + last name already exist together
	err = s.db.Collection("users").FindOne(ctx, bson.M{
		"first_name": req.FirstName,
		"last_name":  req.LastName,
	}).Decode(&existingUser)

	if err == nil {
		return nil, errors.New("a user with this full name already exists")
	} else if err != mongo.ErrNoDocuments {
		return nil, err
	}

	// Generate OTP
	otp := utils.GenerateOTP()

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Parse birth date if provided
	var birthDate time.Time
	if req.BirthDate != "" {
		birthDate, err = time.Parse("2006-01-02", req.BirthDate)
		if err != nil {
			return nil, errors.New("invalid birth date format. Use YYYY-MM-DD")
		}
	}

	// Set default role if not provided
	role := req.Role
	if role == "" {
		role = "user" // Default role
	}

	// Create temporary user document with OTP
	user := &models.User{
		Email:                  req.Email,
		Password:               string(hashedPassword),
		FirstName:              req.FirstName,
		LastName:               req.LastName,
		Role:                   role,
		PhoneNumber:            req.PhoneNumber,
		BirthDate:              birthDate,
		SignupVerificationCode: otp,
		IsProfileComplete:      false,
		IsSubscribedUser:       false,
		IsBlocked:              false,
		TotalListedProperties:  0,
		IsDocumentVerified:     false,
		CreatedAt:              time.Now(),
		UpdatedAt:              time.Now(),
	}

	// Insert user into database
	result, err := s.db.Collection("users").InsertOne(ctx, user)
	if err != nil {
		return nil, err
	}

	user.ID = result.InsertedID.(primitive.ObjectID)

	// Send OTP via EmailJS
	err = utils.SendOTPEmail(emailJSConfig, req.Email, req.FirstName, otp)
	if err != nil {
		// If email sending fails, delete the user and return error
		_, deleteErr := s.db.Collection("users").DeleteOne(ctx, bson.M{"_id": user.ID})
		if deleteErr != nil {
			// Log this error but return the original error
			return nil, errors.New("failed to send OTP email and cleanup failed: " + err.Error())
		}
		return nil, errors.New("failed to send OTP email: " + err.Error())
	}

	return &models.OTPResponse{
		Success: true,
		Message: "OTP sent successfully to " + req.Email,
	}, nil
}

// VerifyOTP verifies the OTP and completes the registration process
func (s *Service) VerifyOTP(ctx context.Context, req *models.VerifyOTPRequest) (*models.VerifyOTPResponse, error) {
	// Find user by email
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": req.Email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	// Verify OTP
	if user.SignupVerificationCode != req.OTP {
		return nil, ErrInvalidOTP
	}

	// Clear OTP and mark as verified
	update := bson.M{
		"$set": bson.M{
			"signup_verification_code": "",
			"updated_at":               time.Now(),
		},
	}

	_, err = s.db.Collection("users").UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		return nil, err
	}

	// Clear OTP from response
	user.SignupVerificationCode = ""

	// Generate JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":    user.ID.Hex(),
		"email":      user.Email,
		"first_name": user.FirstName,
		"last_name":  user.LastName,
		"role":       user.Role,
		"exp":        time.Now().Add(time.Hour * 24).Unix(), // Token expires in 24 hours
	})

	tokenString, err := token.SignedString(s.jwtSecret)
	if err != nil {
		return nil, err
	}

	return &models.VerifyOTPResponse{
		Message: "OTP verified successfully",
		Token:   tokenString,
		User:    user,
	}, nil
}

// ResendOTP generates a new OTP for an existing user registration request
func (s *Service) ResendOTP(ctx context.Context, email string, emailJSConfig utils.EmailJSConfig) (*models.OTPResponse, error) {
	// Find user by email
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	// Check if user is already verified (no OTP present)
	if user.SignupVerificationCode == "" {
		return nil, errors.New("user is already verified")
	}

	// Generate new OTP
	otp := utils.GenerateOTP()

	// Update user with new OTP
	update := bson.M{
		"$set": bson.M{
			"signup_verification_code": otp,
			"updated_at":               time.Now(),
		},
	}

	_, err = s.db.Collection("users").UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		return nil, err
	}

	// Send new OTP via EmailJS
	err = utils.SendOTPEmail(emailJSConfig, user.Email, user.FirstName, otp)
	if err != nil {
		return nil, errors.New("failed to send OTP email: " + err.Error())
	}

	return &models.OTPResponse{
		Success: true,
		Message: "OTP resent successfully to " + user.Email,
	}, nil
}

// ForgotPassword initiates the password reset process
func (s *Service) ForgotPassword(ctx context.Context, email string, frontendURL string, emailJSConfig utils.EmailJSConfig) (*models.ForgotPasswordResponse, error) {
	// Find user by email
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrUserNotFound
		}
		return nil, err
	}

	// Generate reset token
	token, err := utils.GenerateResetToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate reset token: %w", err)
	}

	// Set token expiry (1 hour from now)
	expiry := time.Now().Add(time.Hour)

	// Update user with reset token and expiry
	update := bson.M{
		"$set": bson.M{
			"password_reset_token":  token,
			"password_reset_expiry": expiry,
			"updated_at":            time.Now(),
		},
	}

	_, err = s.db.Collection("users").UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		return nil, err
	}

	// Create reset link
	resetLink := fmt.Sprintf("%s/reset-password?token=%s&email=%s", frontendURL, token, email)

	// Send password reset email
	err = utils.SendPasswordResetEmail(emailJSConfig, user.Email, user.FirstName, resetLink)
	if err != nil {
		return nil, fmt.Errorf("failed to send password reset email: %w", err)
	}

	return &models.ForgotPasswordResponse{
		Success: true,
		Message: "Password reset link sent to " + email,
	}, nil
}

// ResetPassword completes the password reset process
func (s *Service) ResetPassword(ctx context.Context, req *models.ResetPasswordRequest) (*models.ResetPasswordResponse, error) {
	// Find user by email
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"email": req.Email}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrUserNotFound
		}
		return nil, err
	}

	// Verify token
	if user.PasswordResetToken != req.Token {
		return nil, ErrInvalidToken
	}

	// Check if token is expired
	if user.PasswordResetExpiry.Before(time.Now()) {
		return nil, ErrInvalidToken
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Update user with new password and clear reset token
	update := bson.M{
		"$set": bson.M{
			"password":              string(hashedPassword),
			"password_reset_token":  "",
			"password_reset_expiry": time.Time{}, // Zero time
			"updated_at":            time.Now(),
		},
	}

	_, err = s.db.Collection("users").UpdateOne(ctx, bson.M{"_id": user.ID}, update)
	if err != nil {
		return nil, err
	}

	return &models.ResetPasswordResponse{
		Success: true,
		Message: "Password has been reset successfully",
	}, nil
}

// ChangePassword changes the user's password if the current password is correct
func (s *Service) ChangePassword(ctx context.Context, userID primitive.ObjectID, req *models.ChangePasswordRequest) (*models.ChangePasswordResponse, error) {
	// Find user by ID
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"_id": userID}).Decode(&user)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, ErrUserNotFound
		}
		return nil, err
	}

	// Verify current password
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.CurrentPassword))
	if err != nil {
		return nil, ErrInvalidCredentials
	}

	// Check if new password is the same as the current password
	if req.CurrentPassword == req.NewPassword {
		return nil, ErrSamePassword
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// Update user with new password
	update := bson.M{
		"$set": bson.M{
			"password":   string(hashedPassword),
			"updated_at": time.Now(),
		},
	}

	_, err = s.db.Collection("users").UpdateOne(ctx, bson.M{"_id": userID}, update)
	if err != nil {
		return nil, err
	}

	return &models.ChangePasswordResponse{
		Success: true,
		Message: "Password has been changed successfully",
	}, nil
}

// GetUserDetails retrieves complete user details
func (s *Service) GetUserDetails(ctx context.Context, id primitive.ObjectID) (*models.User, error) {
	var user models.User
	err := s.db.Collection("users").FindOne(ctx, bson.M{"_id": id}).Decode(&user)
	if err != nil {
		return nil, err
	}

	return &user, nil
}
