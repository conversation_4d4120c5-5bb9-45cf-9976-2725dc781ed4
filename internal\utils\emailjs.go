package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"time"
)

// EmailJSConfig holds the configuration for EmailJS service
type EmailJSConfig struct {
	ServiceID  string
	TemplateID string
	UserID     string
}

// EmailJSPayload represents the payload to send to EmailJS
type EmailJSPayload struct {
	ServiceID      string            `json:"service_id"`
	TemplateID     string            `json:"template_id"`
	UserID         string            `json:"user_id"`
	TemplateParams map[string]string `json:"template_params"`
}

// EmailJSResponse represents the response from EmailJS
type EmailJSResponse struct {
	Status int    `json:"status"`
	Text   string `json:"text"`
}

// GenerateOTP generates a random 6-digit OTP
func GenerateOTP() string {
	// Create a local random generator with a random source
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Generate a random 6-digit number
	otp := r.Intn(900000) + 100000

	return fmt.Sprintf("%d", otp)
}

// SendOTPEmail sends an OTP email using EmailJS
func SendOTPEmail(config EmailJSConfig, email, firstName, otp string) error {
	// Validate config
	if config.ServiceID == "" || config.TemplateID == "" || config.UserID == "" {
		return fmt.Errorf("missing required EmailJS configuration")
	}

	// Create template parameters with all possible recipient parameter names
	templateParams := map[string]string{
		"recipient":  email,     // Primary key for recipient email
		"to_name":    firstName, // For the greeting in the template
		"otp":        otp,       // The OTP code
		"to_email":   email,     // Alternative parameter name
		"email":      email,     // Alternative parameter name
		"user_email": email,     // Alternative parameter name
		"to":         email,     // Alternative parameter name
	}

	// Create the payload
	payload := EmailJSPayload{
		ServiceID:      config.ServiceID,
		TemplateID:     config.TemplateID,
		UserID:         config.UserID,
		TemplateParams: templateParams,
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to prepare email request")
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.emailjs.com/api/v1.0/email/send", bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create email request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Origin", "http://localhost")

	// Send request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: network error")
	}
	defer resp.Body.Close()

	// Discard response body
	io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		switch resp.StatusCode {
		case 400:
			return fmt.Errorf("failed to send email: invalid template configuration")
		case 403:
			return fmt.Errorf("failed to send email: authentication failed")
		case 429:
			return fmt.Errorf("failed to send email: rate limit exceeded")
		default:
			return fmt.Errorf("failed to send email: service error (code: %d)", resp.StatusCode)
		}
	}

	return nil
}

// GenerateResetToken generates a secure random token for password reset
func GenerateResetToken() (string, error) {
	// Generate 32 bytes of random data
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}

	// Convert to base64 for URL-safe token
	return base64.URLEncoding.EncodeToString(b), nil
}

// SendPasswordResetEmail sends a password reset link using EmailJS
func SendPasswordResetEmail(config EmailJSConfig, email, firstName, resetLink string) error {
	// Validate config
	if config.ServiceID == "" || config.TemplateID == "" || config.UserID == "" {
		return fmt.Errorf("missing required EmailJS configuration")
	}

	// Create template parameters
	templateParams := map[string]string{
		"recipient":  email,     // Primary key for recipient email
		"to_name":    firstName, // For the greeting in the template
		"reset_link": resetLink, // The password reset link
		"to_email":   email,     // Alternative parameter name
		"email":      email,     // Alternative parameter name
		"user_email": email,     // Alternative parameter name
		"to":         email,     // Alternative parameter name
	}

	// Create the payload
	payload := EmailJSPayload{
		ServiceID:      config.ServiceID,
		TemplateID:     config.TemplateID,
		UserID:         config.UserID,
		TemplateParams: templateParams,
	}

	// Convert payload to JSON
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to prepare email request")
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", "https://api.emailjs.com/api/v1.0/email/send", bytes.NewBuffer(jsonPayload))
	if err != nil {
		return fmt.Errorf("failed to create email request")
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Origin", "http://localhost")

	// Send request
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send email: network error")
	}
	defer resp.Body.Close()

	// Discard response body
	io.ReadAll(resp.Body)

	// Check response status
	if resp.StatusCode != http.StatusOK {
		switch resp.StatusCode {
		case 400:
			return fmt.Errorf("failed to send email: invalid template configuration")
		case 403:
			return fmt.Errorf("failed to send email: authentication failed")
		case 429:
			return fmt.Errorf("failed to send email: rate limit exceeded")
		default:
			return fmt.Errorf("failed to send email: service error (code: %d)", resp.StatusCode)
		}
	}

	return nil
}
