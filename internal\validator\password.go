package validator

import (
	"regexp"
	"strings"
	"unicode"

	"github.com/go-playground/validator/v10"
)

// RegisterPasswordValidation registers custom password validation with the validator
func RegisterPasswordValidation(v *validator.Validate) {
	v.RegisterValidation("password", validatePassword)
}

// validatePassword checks if the password meets the following requirements:
// - At least 8 characters long
// - Contains at least one uppercase letter
// - Contains at least one lowercase letter
// - Contains at least one number
// - Contains at least one special character
func validatePassword(fl validator.FieldLevel) bool {
	password := fl.Field().String()

	// Check minimum length
	if len(password) < 8 {
		return false
	}

	// Check for at least one uppercase letter
	hasUpper := false
	// Check for at least one lowercase letter
	hasLower := false
	// Check for at least one number
	hasNumber := false
	// Check for at least one special character
	hasSpecial := false

	// Regular expression for special characters
	specialCharRegex := regexp.MustCompile(`[!@#$%^&*(),.?":{}|<>]`)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case specialCharRegex.MatchString(string(char)):
			hasSpecial = true
		}
	}

	return hasUpper && hasLower && hasNumber && hasSpecial
}

// GetPasswordValidationError returns a specific error message about what's missing from the password
func GetPasswordValidationError(password string) string {
	var errors []string

	// Check minimum length
	if len(password) < 8 {
		errors = append(errors, "at least 8 characters long")
	}

	// Check for at least one uppercase letter
	hasUpper := false
	// Check for at least one lowercase letter
	hasLower := false
	// Check for at least one number
	hasNumber := false
	// Check for at least one special character
	hasSpecial := false

	// Regular expression for special characters
	specialCharRegex := regexp.MustCompile(`[!@#$%^&*(),.?":{}|<>]`)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case specialCharRegex.MatchString(string(char)):
			hasSpecial = true
		}
	}

	if !hasUpper {
		errors = append(errors, "at least one uppercase letter")
	}
	if !hasLower {
		errors = append(errors, "at least one lowercase letter")
	}
	if !hasNumber {
		errors = append(errors, "at least one number")
	}
	if !hasSpecial {
		errors = append(errors, "at least one special character")
	}

	if len(errors) == 0 {
		return ""
	}

	return "Password must be " + strings.Join(errors, ", ")
}
