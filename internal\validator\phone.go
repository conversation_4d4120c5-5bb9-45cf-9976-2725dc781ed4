package validator

import (
	"errors"
	"strings"

	"github.com/nyaruka/phonenumbers"
)

// ValidatePhoneNumber validates phone number using Google's libphonenumber library
// Expects format: "+[country_code] [phone_number]" (e.g., "+91 7878715456")
func ValidatePhoneNumber(phoneNumber string) error {
	// Remove extra spaces and normalize
	phoneNumber = strings.TrimSpace(phoneNumber)

	// Basic format validation
	if !strings.HasPrefix(phoneNumber, "+") {
		return errors.New("phone number must start with country code (e.g., +91 7878715456)")
	}

	if !strings.Contains(phoneNumber, " ") {
		return errors.New("phone number must have space between country code and number (e.g., +91 7878715456)")
	}

	// Split country code and number
	parts := strings.SplitN(phoneNumber, " ", 2)
	if len(parts) != 2 {
		return errors.New("invalid phone number format. Use: +[country_code] [phone_number]")
	}

	// Remove spaces from the number part and reconstruct
	countryCode := parts[0]
	number := strings.ReplaceAll(parts[1], " ", "")
	fullNumber := countryCode + number

	// Parse the phone number - let libphonenumber auto-detect the region
	parsedNumber, err := phonenumbers.Parse(fullNumber, "")
	if err != nil {
		return errors.New("failed to parse phone number: " + err.Error())
	}

	// Validate the parsed number
	if !phonenumbers.IsValidNumber(parsedNumber) {
		return errors.New("invalid phone number for the given country")
	}

	// Additional checks for number type
	numberType := phonenumbers.GetNumberType(parsedNumber)
	if numberType == phonenumbers.UNKNOWN || numberType == phonenumbers.VOICEMAIL {
		return errors.New("unsupported phone number type")
	}

	return nil
}
