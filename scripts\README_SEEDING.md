# Database Seeding Guide

This directory contains scripts for seeding the database with initial data for the Real Estate Platform.

## Overview

The seeding system is designed to be flexible and modular, allowing you to seed different types of data independently or all at once. The main seeding script (`seed_db.go`) supports seeding:

- Users (with various roles: admin, agent, user)
- Property Types (e.g., Apartment, House, Villa)
- Facilities (e.g., Swimming Pool, Gym, Parking)
- Properties (with random details, linked to agent users)

## Prerequisites

Before running the seeding script, ensure:

1. MongoDB is running on port 27018
2. The application's configuration is properly set up
3. You have Go installed (version 1.20 or higher)

## Usage

### Basic Usage

Run the script with no flags to see available options:

```bash
go run scripts/seed_db.go
```

This will display:
```
No seeding operation specified. Use one of the following flags:
  -users           : Seed users
  -property-types  : Seed property types
  -facilities      : Seed facilities
  -properties      : Seed properties
  -all             : Seed all data

Example: go run scripts/seed_db.go -property-types -facilities
```

### Seeding Specific Data

You can seed specific types of data using command-line flags:

```bash
# Seed only users
go run scripts/seed_db.go -users

# Seed only property types
go run scripts/seed_db.go -property-types

# Seed only facilities
go run scripts/seed_db.go -facilities

# Seed only properties
go run scripts/seed_db.go -properties

# Seed multiple types of data
go run scripts/seed_db.go -property-types -facilities

# Seed all data
go run scripts/seed_db.go -all
```

### Using Make Command

Alternatively, you can use the provided make command:

```bash
# Seed all data
make seed-db

# To seed specific data, you'll need to modify the command in the Makefile
# or run the go command directly
```

## Seeded Data Details

### Users

The script seeds 25 users with the following characteristics:
- Random names and email addresses
- Password: "Password1!" (hashed)
- Roles distributed as:
  - 70% regular users
  - 25% agents
  - 5% admins
- Random locations across major US cities
- Random profile photos from randomuser.me
- Various metadata like profile completion status, subscription status, etc.

### Property Types

Seeds the following property types:
1. Apartment
2. House
3. Villa
4. Commercial
5. Land

Each property type includes:
- Name
- Order (for display)
- Image URL

### Facilities

Seeds the following facilities:
1. Swimming Pool
2. Gym
3. Parking
4. Security
5. Elevator
6. Garden
7. Playground
8. Power Backup
9. Water Supply
10. Internet

Each facility includes:
- Name
- Icon URL
- Availability status
- Active status

### Properties

Seeds 10 properties with the following characteristics:
- Random titles and descriptions
- Various property types (apartment, house, villa, commercial, land)
- Random statuses (available, sold, rented)
- Locations based on agent locations
- Random specifications (area, bedrooms, bathrooms, etc.)
- Random facilities (3-6 per property)
- Random prices based on property type and size
- Linked to random agent users as owners

## Database Connection

The script connects to MongoDB using the following configuration:
- Host: localhost
- Port: 27018
- Database: As specified in your application config

## Error Handling

The script includes error handling for:
- Database connection issues
- Duplicate entries
- Invalid data
- Failed insertions

Each seeding operation reports its success/failure status, and the script continues even if some operations fail.

## Adding New Seeding Data

To add new types of data to seed:

1. Add a new command-line flag in the `main()` function
2. Create a new seeding function
3. Add the condition to run it in `main()`
4. Add your sample data as a variable

Example structure:
```go
// Add flag
seedNewData := flag.Bool("new-data", false, "Seed new data type")

// Add condition
if *seedNewData || *seedAll {
    seedNewDataFunction(ctx, db)
}

// Add function
func seedNewDataFunction(ctx context.Context, db *mongo.Database) {
    // Seeding logic here
}
```

## Best Practices

1. Always run seeding in a development environment
2. Back up your database before seeding
3. Use specific flags to seed only what you need
4. Check the console output for any errors
5. Verify the seeded data in your application

## Troubleshooting

Common issues and solutions:

1. **Connection Error**
   - Ensure MongoDB is running
   - Check if the port (27018) is correct
   - Verify your MongoDB connection string

2. **Duplicate Key Error**
   - The data might already exist
   - Clear the collections before seeding
   - Use `-property-types` or `-facilities` to seed only specific data

3. **Permission Error**
   - Check MongoDB user permissions
   - Ensure you have write access to the database

## Contributing

When adding new seeding data:
1. Follow the existing code structure
2. Add appropriate error handling
3. Update this documentation with new information
4. Test the seeding process thoroughly
5. Ensure the data is realistic and useful for development