package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/crypto/bcrypt"

	"realestate-platform/internal/config"
	"realestate-platform/internal/models"
)

// User represents the user model for seeding
type Location struct {
	Address      string  `json:"address" bson:"address"`
	City         string  `json:"city" bson:"city"`
	Area         string  `json:"area" bson:"area"`
	State        string  `json:"state" bson:"state"`
	Country      string  `json:"country" bson:"country"`
	ZipCode      string  `json:"zip_code" bson:"zip_code"`
	Latitude     float64 `json:"latitude" bson:"latitude"`
	Longitude    float64 `json:"longitude" bson:"longitude"`
	Neighborhood string  `json:"neighborhood" bson:"neighborhood"`
}

type User struct {
	ID                     primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Email                  string             `json:"email" bson:"email"`
	FirstName              string             `json:"first_name" bson:"first_name"`
	LastName               string             `json:"last_name" bson:"last_name"`
	Password               string             `json:"password" bson:"password"`
	Role                   string             `json:"role" bson:"role"` // admin, agent, user
	PhoneNumber            string             `json:"phn_number" bson:"phn_number"`
	BirthDate              time.Time          `json:"birth_date" bson:"birth_date"`
	Location               Location           `json:"location" bson:"location"`
	SignupVerificationCode string             `json:"signup_verification_code" bson:"signup_verification_code"`
	ProfilePhoto           string             `json:"profile_photo" bson:"profile_photo"`
	Metadata               map[string]any     `json:"metadata" bson:"metadata"`
	IsProfileComplete      bool               `json:"is_profile_complete" bson:"is_profile_complete"`
	IsSubscribedUser       bool               `json:"is_subscribed_user" bson:"is_subscribed_user"`
	IsBlocked              bool               `json:"is_blocked" bson:"is_blocked"`
	TotalListedProperties  int                `json:"total_listed_properties" bson:"total_listed_properties"`
	IsDocumentVerified     bool               `json:"is_document_verified" bson:"is_document_verified"`
	CreatedAt              time.Time          `json:"created_at" bson:"created_at"`
	UpdatedAt              time.Time          `json:"updated_at" bson:"updated_at"`
}

// Sample data for generating random users
var (
	firstNames = []string{
		"Aarav", "Arjun", "Rohan", "Vikram", "Karan", "Rahul", "Amit", "Suresh", "Rajesh", "Pradeep",
		"Priya", "Anita", "Kavya", "Sneha", "Pooja", "Meera", "Sunita", "Deepika", "Riya", "Neha",
	}

	lastNames = []string{
		"Sharma", "Patel", "Singh", "Kumar", "Gupta", "Agarwal", "Jain", "Verma", "Shah", "Mehta",
		"Reddy", "Nair", "Iyer", "Rao", "Chopra", "Malhotra", "Bansal", "Sinha", "Joshi", "Tiwari",
	}

	cities = []string{
		"Mumbai", "Delhi", "Bangalore", "Hyderabad", "Ahmedabad", "Chennai", "Kolkata", "Surat", "Pune", "Jaipur",
		"Lucknow", "Kanpur", "Nagpur", "Indore", "Thane", "Bhopal", "Visakhapatnam", "Pimpri-Chinchwad", "Patna", "Vadodara",
		"Noida", "Gurgaon", "Faridabad", "Ghaziabad", "Agra", "Meerut", "Varanasi", "Allahabad", "Coimbatore", "Madurai",
		"Kochi", "Thiruvananthapuram", "Mysore", "Mangalore", "Hubli", "Belgaum", "Rajkot", "Bhavnagar", "Jamnagar", "Gandhinagar",
	}

	states = []string{
		"Maharashtra", "Delhi", "Karnataka", "Telangana", "Gujarat", "Tamil Nadu", "West Bengal", "Gujarat", "Maharashtra", "Rajasthan",
		"Uttar Pradesh", "Uttar Pradesh", "Maharashtra", "Madhya Pradesh", "Maharashtra", "Madhya Pradesh", "Andhra Pradesh", "Maharashtra", "Bihar", "Gujarat",
		"Uttar Pradesh", "Haryana", "Uttar Pradesh", "Uttar Pradesh", "Uttar Pradesh", "Uttar Pradesh", "Uttar Pradesh", "Uttar Pradesh", "Tamil Nadu", "Tamil Nadu",
		"Kerala", "Kerala", "Karnataka", "Karnataka", "Karnataka", "Karnataka", "Gujarat", "Gujarat", "Gujarat", "Gujarat",
	}

	areas = []string{
		// Mumbai areas
		"Andheri", "Bandra", "Juhu", "Powai", "Malad", "Borivali", "Kandivali", "Goregaon", "Versova", "Lokhandwala",
		// Delhi areas
		"Connaught Place", "Karol Bagh", "Lajpat Nagar", "Saket", "Vasant Kunj", "Dwarka", "Rohini", "Pitampura", "Janakpuri", "Rajouri Garden",
		// Bangalore areas
		"Koramangala", "Whitefield", "Indiranagar", "Jayanagar", "BTM Layout", "HSR Layout", "Electronic City", "Marathahalli", "Bellandur", "Sarjapur",
		// Hyderabad areas
		"Gachibowli", "Hitech City", "Banjara Hills", "Jubilee Hills", "Kondapur", "Madhapur", "Kukatpally", "Miyapur", "Secunderabad", "Begumpet",
		// Chennai areas
		"Adyar", "T Nagar", "Anna Nagar", "Velachery", "OMR", "Porur", "Tambaram", "Chrompet", "Mylapore", "Nungambakkam",
		// Pune areas
		"Kothrud", "Wakad", "Hinjewadi", "Baner", "Aundh", "Viman Nagar", "Koregaon Park", "Camp", "Hadapsar", "Magarpatta",
		// Ahmedabad areas
		"Vastrapur", "Satellite", "Prahlad Nagar", "Bodakdev", "Ambawadi", "Navrangpura", "CG Road", "SG Highway", "Bopal", "Gota",
	}

	neighborhoods = []string{
		"IT Park", "Business District", "Commercial Complex", "Residential Society", "Tech Hub", "Financial Center", "Shopping Mall Area", "University Campus", "Medical District", "Industrial Area",
		"Heritage Zone", "New Town", "Garden City", "Lake View", "Hill Station", "Metro Station", "Airport Road", "Ring Road", "Bypass Road", "City Center",
	}

	roles = []string{"user", "admin"}

	// Weighted roles to ensure most users are regular users
	roleWeights = map[string]int{
		"user":  90, // 90% chance
		"admin": 10, // 10% chance
	}
)

// Sample data for property types with Unsplash images
var propertyTypes = []models.PropertyType{
	{
		Name:     "Apartment",
		Order:    1,
		ImageURL: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		Active:   true,
	},
	{
		Name:     "House",
		Order:    2,
		ImageURL: "https://images.unsplash.com/photo-1568605114967-8130f3a36994?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		Active:   true,
	},
	{
		Name:     "Villa",
		Order:    3,
		ImageURL: "https://images.unsplash.com/photo-1613977257363-707ba9348227?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		Active:   true,
	},
	{
		Name:     "Commercial",
		Order:    4,
		ImageURL: "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		Active:   true,
	},
	{
		Name:     "Land",
		Order:    5,
		ImageURL: "https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		Active:   true,
	},
}

// Sample data for facilities
var facilities = []models.Facility{
	{
		Name:      "Swimming Pool",
		IconURL:   "https://example.com/icons/pool.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Gym",
		IconURL:   "https://example.com/icons/gym.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Parking",
		IconURL:   "https://example.com/icons/parking.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Security",
		IconURL:   "https://example.com/icons/security.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Elevator",
		IconURL:   "https://example.com/icons/elevator.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Garden",
		IconURL:   "https://example.com/icons/garden.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Playground",
		IconURL:   "https://example.com/icons/playground.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Power Backup",
		IconURL:   "https://example.com/icons/power-backup.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Water Supply",
		IconURL:   "https://example.com/icons/water.png",
		Available: true,
		Active:    true,
	},
	{
		Name:      "Internet",
		IconURL:   "https://example.com/icons/internet.png",
		Available: true,
		Active:    true,
	},
}

// Sample property titles and descriptions for seeding
var (
	propertyTitles = []string{
		"Luxury Apartment with City View",
		"Spacious Family Home in Quiet Neighborhood",
		"Modern Villa with Private Pool",
		"Commercial Space in Business District",
		"Prime Land for Development",
		"Cozy Studio in Downtown",
		"Penthouse with Panoramic Views",
		"Townhouse with Garden",
		"Office Space in Tech Hub",
		"Beachfront Property with Direct Access",
		"Historic Home with Character",
		"New Construction with Modern Amenities",
		"Investment Property with High ROI",
		"Vacation Home in Resort Area",
		"Starter Home for First-Time Buyers",
	}

	propertyDescriptions = []string{
		"This beautiful property offers stunning views and modern amenities. Perfect for those seeking luxury living in the heart of the city.",
		"A spacious and well-maintained property in a family-friendly neighborhood. Features include a large backyard, updated kitchen, and proximity to schools and parks.",
		"Elegant property with high-end finishes throughout. The open floor plan, gourmet kitchen, and outdoor entertainment area make this perfect for those who love to entertain.",
		"Prime location with excellent visibility and foot traffic. This property offers versatile space suitable for retail, office, or mixed-use development.",
		"Rare opportunity to own a piece of land in a rapidly developing area. Zoned for residential or commercial use with all utilities available.",
		"Compact and efficient space with clever storage solutions and modern design. Ideal for professionals or students looking for convenience and style.",
		"Exclusive property featuring premium materials, smart home technology, and unparalleled views. A true statement of success and taste.",
		"Charming property with character and history. Original features have been preserved while modern conveniences have been thoughtfully integrated.",
		"Energy-efficient property with sustainable features that reduce environmental impact and operating costs. Built to last with quality materials.",
		"Investment opportunity with strong rental history and potential for appreciation. Currently tenanted with reliable long-term occupants.",
	}

	facingDirections = []string{
		"North", "South", "East", "West",
		"Northeast", "Northwest", "Southeast", "Southwest",
	}

	furnitureTypes = []string{
		"furnished", "semi-furnished", "unfurnished",
	}

	// Sample property images from Unsplash
	propertyImages = [][]string{
		{
			"https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		},
		{
			"https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600585154526-990dced4db0d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		},
		{
			"https://images.unsplash.com/photo-1600047509807-ba8f99d2cdde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600573472550-8090b5e0745e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
			"https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		},
		{
			"https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		},
		{
			"https://images.unsplash.com/photo-1600210492493-0946911123ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1474&q=80",
			"https://images.unsplash.com/photo-1600047508788-26bb381b0b49?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
		},
	}

	// Property statuses
	propertyStatuses = []string{
		"sell", "sold", "rent", "rented",
	}
)

func main() {
	// Add command line flags for different seeding operations
	seedUsers := flag.Bool("users", false, "Seed users")
	seedPropertyTypes := flag.Bool("property-types", false, "Seed property types")
	seedFacilities := flag.Bool("facilities", false, "Seed facilities")
	seedProperties := flag.Bool("properties", false, "Seed properties")
	seedQuestions := flag.Bool("questions", false, "Seed questionnaire questions")
	seedAll := flag.Bool("all", false, "Seed all data (users, property types, facilities, properties, and questions)")
	flag.Parse()

	// If no flags are provided, show usage
	if !*seedUsers && !*seedPropertyTypes && !*seedFacilities && !*seedProperties && !*seedQuestions && !*seedAll {
		fmt.Println("No seeding operation specified. Use one of the following flags:")
		fmt.Println("  -users           : Seed users")
		fmt.Println("  -property-types  : Seed property types")
		fmt.Println("  -facilities      : Seed facilities")
		fmt.Println("  -properties      : Seed properties")
		fmt.Println("  -questions       : Seed questionnaire questions")
		fmt.Println("  -all             : Seed all data")
		fmt.Println("\nExample: go run scripts/seed_db.go -property-types -facilities")
		return
	}

	// Initialize random source (Go 1.20+ approach)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Override MongoDB URI to use the correct port (27018)
	mongoURI := "mongodb://localhost:27018"

	// Print MongoDB connection details
	fmt.Printf("Using MongoDB URI: %s, Database: %s\n", mongoURI, cfg.MongoDB.Database)

	// Connect to MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	clientOptions := options.Client().ApplyURI(mongoURI)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	// Ping the database
	if err := client.Ping(ctx, nil); err != nil {
		log.Fatalf("Failed to ping MongoDB: %v", err)
	}

	fmt.Println("Connected to MongoDB!")

	// Get the database
	db := client.Database(cfg.MongoDB.Database)

	// Seed users if requested
	if *seedUsers || *seedAll {
		seedUsersData(ctx, db, r)
	}

	// Seed property types if requested
	if *seedPropertyTypes || *seedAll {
		seedPropertyTypesData(ctx, db)
	}

	// Seed facilities if requested
	if *seedFacilities || *seedAll {
		seedFacilitiesData(ctx, db)
	}

	// Seed properties if requested
	if *seedProperties || *seedAll {
		seedPropertiesData(ctx, db, r)
	}

	// Seed questions if requested
	if *seedQuestions || *seedAll {
		seedQuestionsData(ctx, db)
	}
}

// seedUsersData handles user seeding
func seedUsersData(ctx context.Context, db *mongo.Database, r *rand.Rand) {
	usersCollection := db.Collection("users")
	numUsers := 25 // Generate 25 users
	successCount := 0

	fmt.Printf("\nSeeding users...")
	fmt.Printf("Attempting to insert %d users into collection: 'users'\n", numUsers)

	for i := 0; i < numUsers; i++ {
		user := generateRandomUser(i, r)

		// Print user details before insertion
		fmt.Printf("Inserting user %d: %s %s (%s) with role %s\n",
			i+1, user.FirstName, user.LastName, user.Email, user.Role)

		result, err := usersCollection.InsertOne(ctx, user)
		if err != nil {
			log.Printf("ERROR: Failed to insert user %d: %v", i, err)
			continue
		}

		successCount++
		fmt.Printf("Success! Inserted user %d with ID: %v\n", i+1, result.InsertedID)
	}

	fmt.Printf("\nUsers Summary: Successfully seeded %d/%d users into the database!\n", successCount, numUsers)
}

// seedPropertyTypesData handles property types seeding
func seedPropertyTypesData(ctx context.Context, db *mongo.Database) {
	propertyTypesCollection := db.Collection("property_types")
	successCount := 0

	fmt.Println("\nSeeding property types...")
	for _, pt := range propertyTypes {
		pt.CreatedAt = time.Now()
		pt.UpdatedAt = time.Now()

		result, err := propertyTypesCollection.InsertOne(ctx, pt)
		if err != nil {
			log.Printf("ERROR: Failed to insert property type %s: %v", pt.Name, err)
			continue
		}

		successCount++
		fmt.Printf("Success! Inserted property type: %s with ID: %v\n", pt.Name, result.InsertedID)
	}

	fmt.Printf("\nProperty Types Summary: Successfully seeded %d/%d property types\n", successCount, len(propertyTypes))
}

// seedFacilitiesData handles facilities seeding
func seedFacilitiesData(ctx context.Context, db *mongo.Database) {
	facilitiesCollection := db.Collection("facilities")
	successCount := 0

	fmt.Println("\nSeeding facilities...")
	for _, facility := range facilities {
		facility.CreatedAt = time.Now()
		facility.UpdatedAt = time.Now()

		result, err := facilitiesCollection.InsertOne(ctx, facility)
		if err != nil {
			log.Printf("ERROR: Failed to insert facility %s: %v", facility.Name, err)
			continue
		}

		successCount++
		fmt.Printf("Success! Inserted facility: %s with ID: %v\n", facility.Name, result.InsertedID)
	}

	fmt.Printf("\nFacilities Summary: Successfully seeded %d/%d facilities\n", successCount, len(facilities))
}

func generateRandomUser(index int, r *rand.Rand) User {
	// Generate a random user
	firstName := firstNames[r.Intn(len(firstNames))]
	lastName := lastNames[r.Intn(len(lastNames))]
	email := fmt.Sprintf("%<EMAIL>", firstName, lastName, index)
	email = strings.ToLower(email)

	// Generate a random birth date between 18 and 70 years ago
	now := time.Now()
	age := r.Intn(52) + 18 // 18-70 years old
	birthYear := now.Year() - age
	birthMonth := r.Intn(12) + 1
	birthDay := r.Intn(28) + 1 // Simplified to avoid month-specific day counts
	birthDate := time.Date(birthYear, time.Month(birthMonth), birthDay, 0, 0, 0, 0, time.UTC)

	// Hash a simple password
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("Password1!"), bcrypt.DefaultCost)

	// Generate a random location
	city := cities[r.Intn(len(cities))]
	state := states[r.Intn(len(states))]
	area := areas[r.Intn(len(areas))]
	neighborhood := neighborhoods[r.Intn(len(neighborhoods))]

	// Generate random coordinates (simplified) - India coordinates
	latitude := 8.0 + r.Float64()*29.0   // Random latitude around India (8°N to 37°N)
	longitude := 68.0 + r.Float64()*29.0 // Random longitude around India (68°E to 97°E)

	// Select a role based on weighted probability
	role := selectWeightedRole(r)

	// Create the user
	user := User{
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Password:  string(hashedPassword),
		Role:      role,
		PhoneNumber: fmt.Sprintf("+91 %d%d%d%d%d%d%d%d%d%d",
			6+r.Intn(4), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10),
			r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10)),
		BirthDate: birthDate,
		Location: Location{
			Address:      fmt.Sprintf("%d %s Road", r.Intn(999)+1, firstName),
			City:         city,
			Area:         area,
			State:        state,
			Country:      "India",
			ZipCode:      fmt.Sprintf("%d%d%d%d%d%d", 1+r.Intn(8), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10)),
			Latitude:     latitude,
			Longitude:    longitude,
			Neighborhood: neighborhood,
		},
		ProfilePhoto:          fmt.Sprintf("https://randomuser.me/api/portraits/%s/%d.jpg", getGender(firstName), r.Intn(99)),
		Metadata:              map[string]any{},
		IsProfileComplete:     r.Intn(2) == 1,                                               // 50% chance of being complete
		IsSubscribedUser:      r.Intn(5) == 0,                                               // 20% chance of being subscribed
		IsBlocked:             false,                                                        // No blocked users in seed data
		TotalListedProperties: 0,                                                            // Start with 0 properties
		IsDocumentVerified:    r.Intn(2) == 1,                                               // 50% chance of being verified
		CreatedAt:             time.Now().Add(-time.Duration(r.Intn(365)) * 24 * time.Hour), // Random creation date within the last year
		UpdatedAt:             time.Now(),
	}

	// Set properties for users (only users can own properties)
	if user.Role == "user" {
		user.TotalListedProperties = r.Intn(15) // Users can have properties
	}

	// Set properties for admins
	if user.Role == "admin" {
		user.IsDocumentVerified = true
		user.IsProfileComplete = true
		user.TotalListedProperties = 0 // Admins cannot own properties
	}

	return user
}

// selectWeightedRole selects a role based on weighted probability
func selectWeightedRole(r *rand.Rand) string {
	total := 0
	for _, weight := range roleWeights {
		total += weight
	}

	randVal := r.Intn(total)
	cumulative := 0

	for role, weight := range roleWeights {
		cumulative += weight
		if randVal < cumulative {
			return role
		}
	}

	return "user" // Default fallback
}

// getGender returns a simple gender guess based on first name for profile photo URL
// This is a very simplified approach just for generating random avatars
func getGender(firstName string) string {
	// Check if the name is in the female list
	for i := 10; i < 20; i++ {
		if firstName == firstNames[i] {
			return "women"
		}
	}
	return "men"
}

// seedPropertiesData handles property seeding
func seedPropertiesData(ctx context.Context, db *mongo.Database, r *rand.Rand) {
	propertiesCollection := db.Collection("properties")
	usersCollection := db.Collection("users")
	numProperties := 10 // Generate 10 properties
	successCount := 0

	fmt.Printf("\nSeeding properties...")
	fmt.Printf("Attempting to insert %d properties into collection: 'properties'\n", numProperties)

	// Find only users (not admins) to use as property owners
	cursor, err := usersCollection.Find(ctx, map[string]interface{}{"role": "user"})
	if err != nil {
		log.Printf("ERROR: Failed to find user role users: %v", err)
		return
	}
	defer cursor.Close(ctx)

	var propertyOwners []User
	if err = cursor.All(ctx, &propertyOwners); err != nil {
		log.Printf("ERROR: Failed to decode user role users: %v", err)
		return
	}

	if len(propertyOwners) == 0 {
		log.Printf("ERROR: No user role users found. Please seed users first.")
		return
	}

	// Get facility names for random selection
	var facilityNames []string
	for _, facility := range facilities {
		facilityNames = append(facilityNames, facility.Name)
	}

	// Generate and insert properties
	for i := 0; i < numProperties; i++ {
		// Select a random user as the owner
		owner := propertyOwners[r.Intn(len(propertyOwners))]

		// Generate a random property
		property := generateRandomProperty(i, owner, r, facilityNames)

		// Print property details before insertion
		fmt.Printf("Inserting property %d: %s (Type: %s, Status: %s)\n",
			i+1, property.Title, property.Type, property.Status)

		result, err := propertiesCollection.InsertOne(ctx, property)
		if err != nil {
			log.Printf("ERROR: Failed to insert property %d: %v", i, err)
			continue
		}

		successCount++
		fmt.Printf("Success! Inserted property %d with ID: %v\n", i+1, result.InsertedID)

		// Update the owner's total listed properties count
		_, err = usersCollection.UpdateOne(
			ctx,
			map[string]interface{}{"_id": owner.ID},
			map[string]interface{}{
				"$inc": map[string]interface{}{
					"total_listed_properties": 1,
				},
				"$set": map[string]interface{}{
					"updated_at": time.Now(),
				},
			},
		)
		if err != nil {
			log.Printf("WARNING: Failed to update owner's total listed properties: %v", err)
		}
	}

	fmt.Printf("\nProperties Summary: Successfully seeded %d/%d properties into the database!\n", successCount, numProperties)
}

// generateRandomProperty creates a random property for seeding
func generateRandomProperty(index int, owner User, r *rand.Rand, facilityNames []string) models.Property {
	// Select random property type
	propertyType := propertyTypes[r.Intn(len(propertyTypes))].Name

	// Select random title and description
	title := propertyTitles[r.Intn(len(propertyTitles))]
	description := propertyDescriptions[r.Intn(len(propertyDescriptions))]

	// Select random status
	status := propertyStatuses[r.Intn(len(propertyStatuses))]

	// Generate random location (use owner's city and state for consistency)
	city := owner.Location.City
	state := owner.Location.State
	area := areas[r.Intn(len(areas))]
	neighborhood := neighborhoods[r.Intn(len(neighborhoods))]

	// Generate random coordinates (near the owner's location)
	latOffset := (r.Float64() - 0.5) * 0.1 // +/- 0.05 degrees
	longOffset := (r.Float64() - 0.5) * 0.1
	latitude := owner.Location.Latitude + latOffset
	longitude := owner.Location.Longitude + longOffset

	// Select random images
	images := propertyImages[r.Intn(len(propertyImages))]

	// Generate random specifications
	propertyArea := 500.0 + r.Float64()*2000.0 // 500-2500 sq ft
	yearBuilt := 1980 + r.Intn(43)             // 1980-2023
	bhk := 1 + r.Intn(4)                       // 1-4 BHK
	bedroom := bhk                             // Same as BHK
	bathroom := 1 + r.Intn(3)                  // 1-3 bathrooms
	noOfParking := r.Intn(3)                   // 0-2 parking spots

	// Select random facilities (3-6 facilities)
	numFacilities := 3 + r.Intn(4)
	var selectedFacilities []models.PropertyFacility

	// Create a copy of facility names to shuffle
	availableFacilities := make([]string, len(facilityNames))
	copy(availableFacilities, facilityNames)

	// Shuffle the facilities
	r.Shuffle(len(availableFacilities), func(i, j int) {
		availableFacilities[i], availableFacilities[j] = availableFacilities[j], availableFacilities[i]
	})

	// Select the first numFacilities and convert to PropertyFacility
	if numFacilities > len(availableFacilities) {
		numFacilities = len(availableFacilities)
	}
	for i := 0; i < numFacilities; i++ {
		selectedFacilities = append(selectedFacilities, models.PropertyFacility{
			FacilityID: primitive.NewObjectID(),
			Name:       availableFacilities[i],
			ImageURL:   fmt.Sprintf("https://example.com/icons/%s.png", strings.ToLower(strings.ReplaceAll(availableFacilities[i], " ", "-"))),
		})
	}

	// Select random facing direction
	facing := facingDirections[r.Intn(len(facingDirections))]

	// Select random furniture type
	furniture := furnitureTypes[r.Intn(len(furnitureTypes))]

	// Generate random price based on property type and size
	basePrice := 0.0
	switch propertyType {
	case "Apartment":
		basePrice = 150000
	case "House":
		basePrice = 250000
	case "Villa":
		basePrice = 500000
	case "Commercial":
		basePrice = 350000
	case "Land":
		basePrice = 100000
	}
	priceVariation := 0.5 + r.Float64() // 0.5-1.5 multiplier
	totalPrice := basePrice * priceVariation * (propertyArea / 1000.0)

	// Generate random floor information
	totalFloor := 0.0
	if propertyType != "Land" {
		totalFloor = float64(1 + r.Intn(20)) // 1-20 floors
	}

	// Create the property
	property := models.Property{
		Title:       title,
		Description: description,
		Type:        strings.ToLower(propertyType),
		Status:      status,
		Location: models.Location{
			Address:      fmt.Sprintf("%d %s Road", 1+r.Intn(999), neighborhood),
			City:         city,
			Area:         area,
			State:        state,
			Country:      "India",
			ZipCode:      fmt.Sprintf("%d%d%d%d%d%d", 1+r.Intn(8), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10), r.Intn(10)),
			Latitude:     latitude,
			Longitude:    longitude,
			Neighborhood: neighborhood,
		},
		Images:             images,
		Area:               propertyArea,
		YearBuilt:          yearBuilt,
		BHK:                bhk,
		Bedroom:            bedroom,
		Bathroom:           bathroom,
		NoOfParking:        noOfParking,
		Rating:             3.0 + r.Float64()*2.0, // 3.0-5.0 rating
		OwnerID:            owner.ID,
		OwnerName:          fmt.Sprintf("%s %s", owner.FirstName, owner.LastName),
		Facilities:         selectedFacilities,
		Facing:             facing,
		Furniture:          furniture,
		TotalPrice:         totalPrice,
		TotalFloor:         totalFloor,
		IsPropertyVerified: r.Intn(2) == 1,                                              // 50% chance of being verified
		IsPropertyActive:   true,                                                        // All seeded properties are active
		CreatedAt:          time.Now().Add(-time.Duration(r.Intn(90)) * 24 * time.Hour), // Random creation date within the last 90 days
		UpdatedAt:          time.Now(),
	}

	return property
}

// seedQuestionsData handles questionnaire questions seeding
func seedQuestionsData(ctx context.Context, db *mongo.Database) {
	questionsCollection := db.Collection("questions")
	successCount := 0

	fmt.Println("\nSeeding questionnaire questions...")

	// Check if questions already exist
	count, err := questionsCollection.CountDocuments(ctx, map[string]interface{}{})
	if err != nil {
		log.Printf("ERROR: Failed to check existing questions: %v", err)
		return
	}

	if count > 0 {
		fmt.Printf("Questions already exist (%d found), skipping seed\n", count)
		return
	}

	// Define default questions
	defaultQuestions := []interface{}{
		map[string]interface{}{
			"text":        "What's your budget range for the property?",
			"type":        "single_choice",
			"order":       1,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "budget_1", "text": "₹10L - ₹25L", "value": "1000000-2500000"},
				{"id": "budget_2", "text": "₹25L - ₹50L", "value": "2500000-5000000"},
				{"id": "budget_3", "text": "₹50L - ₹1Cr", "value": "5000000-10000000"},
				{"id": "budget_4", "text": "₹1Cr - ₹2Cr", "value": "10000000-20000000"},
				{"id": "budget_5", "text": "Above ₹2Cr", "value": "20000000-50000000"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"cheap", "dirty", "whatever", "anything"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "What type of property are you looking for?",
			"type":        "single_choice",
			"order":       2,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "type_1", "text": "Apartment", "value": "apartment"},
				{"id": "type_2", "text": "House", "value": "house"},
				{"id": "type_3", "text": "Villa", "value": "villa"},
				{"id": "type_4", "text": "Commercial", "value": "commercial"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"whatever", "anything", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "Which city are you interested in?",
			"type":        "single_choice",
			"order":       3,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "city_1", "text": "Mumbai", "value": "mumbai"},
				{"id": "city_2", "text": "Delhi", "value": "delhi"},
				{"id": "city_3", "text": "Bangalore", "value": "bangalore"},
				{"id": "city_4", "text": "Pune", "value": "pune"},
				{"id": "city_5", "text": "Chennai", "value": "chennai"},
				{"id": "city_6", "text": "Hyderabad", "value": "hyderabad"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"anywhere", "whatever", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "Which area/neighborhood do you prefer?",
			"type":        "single_choice",
			"order":       4,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "area_1", "text": "Andheri", "value": "andheri"},
				{"id": "area_2", "text": "Bandra", "value": "bandra"},
				{"id": "area_3", "text": "Powai", "value": "powai"},
				{"id": "area_4", "text": "Thane", "value": "thane"},
				{"id": "area_5", "text": "Navi Mumbai", "value": "navi mumbai"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"anywhere", "whatever", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "How many bedrooms do you need?",
			"type":        "single_choice",
			"order":       5,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "bhk_1", "text": "1 BHK", "value": "1"},
				{"id": "bhk_2", "text": "2 BHK", "value": "2"},
				{"id": "bhk_3", "text": "3 BHK", "value": "3"},
				{"id": "bhk_4", "text": "4+ BHK", "value": "4+"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"whatever", "anything", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "Which amenities are important to you?",
			"type":        "multiple_choice",
			"order":       6,
			"is_required": false,
			"options": []map[string]interface{}{
				{"id": "amenity_1", "text": "Parking", "value": "parking"},
				{"id": "amenity_2", "text": "Gym", "value": "gym"},
				{"id": "amenity_3", "text": "Swimming Pool", "value": "pool"},
				{"id": "amenity_4", "text": "Security", "value": "security"},
				{"id": "amenity_5", "text": "Garden", "value": "garden"},
				{"id": "amenity_6", "text": "Elevator", "value": "elevator"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"whatever", "anything", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "Are you looking to buy or rent?",
			"type":        "single_choice",
			"order":       7,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "purpose_1", "text": "Buy", "value": "buy"},
				{"id": "purpose_2", "text": "Rent", "value": "rent"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"whatever", "anything", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
		map[string]interface{}{
			"text":        "What furnishing do you prefer?",
			"type":        "single_choice",
			"order":       8,
			"is_required": true,
			"options": []map[string]interface{}{
				{"id": "furnishing_1", "text": "Fully Furnished", "value": "furnished"},
				{"id": "furnishing_2", "text": "Semi Furnished", "value": "semi-furnished"},
				{"id": "furnishing_3", "text": "Unfurnished", "value": "unfurnished"},
			},
			"validation": map[string]interface{}{
				"forbidden_words": []string{"whatever", "anything", "don't care"},
			},
			"is_active":  true,
			"created_at": time.Now(),
			"updated_at": time.Now(),
		},
	}

	// Insert all questions
	result, err := questionsCollection.InsertMany(ctx, defaultQuestions)
	if err != nil {
		log.Printf("ERROR: Failed to insert questions: %v", err)
		return
	}

	successCount = len(result.InsertedIDs)
	fmt.Printf("Success! Inserted %d questionnaire questions\n", successCount)
	fmt.Printf("\nQuestions Summary: Successfully seeded %d/%d questionnaire questions\n", successCount, len(defaultQuestions))
}
