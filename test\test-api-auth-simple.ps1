# Simple test script for API authentication endpoints

# Configuration
$baseUrl = "http://localhost:8080"
$apiUrl = "$baseUrl/api/v1"

Write-Host "=== Real Estate Platform API Authentication Testing ===" -ForegroundColor Cyan
Write-Host "Testing authentication endpoints for input sanitization and validation" -ForegroundColor Cyan
Write-Host "Base URL: $baseUrl" -ForegroundColor Cyan
Write-Host ""

# Check if the API is running
try {
    $healthCheck = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "API is running. Health check: $($healthCheck | ConvertTo-Json)" -ForegroundColor Green
}
catch {
    Write-Host "API is not running. Please start the API server first." -ForegroundColor Red
    Write-Host "You can use 'make docker-compose-dev-d' to start the API server." -ForegroundColor Cyan
    exit 1
}

# Test valid registration
Write-Host ""
Write-Host "Testing valid registration..." -ForegroundColor Cyan
$validUser = @{
    email = "<EMAIL>"
    password = "Password1!"
    first_name = "<PERSON>"
    last_name = "Do<PERSON>"
}

try {
    $registerResponse = Invoke-RestMethod -Uri "$apiUrl/auth/register" -Method POST -Body ($validUser | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Registration successful: $($registerResponse | ConvertTo-Json)" -ForegroundColor Green
}
catch {
    Write-Host "Registration failed: $_" -ForegroundColor Red
}

# Test invalid email
Write-Host ""
Write-Host "Testing invalid email..." -ForegroundColor Cyan
$invalidEmailUser = @{
    email = "invalid-email"
    password = "Password1!"
    first_name = "John"
    last_name = "Doe"
}

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/register" -Method POST -Body ($invalidEmailUser | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Test failed: Invalid email was accepted" -ForegroundColor Red
}
catch {
    Write-Host "Test passed: Invalid email was rejected" -ForegroundColor Green
}

# Test invalid password
Write-Host ""
Write-Host "Testing invalid password..." -ForegroundColor Cyan
$invalidPasswordUser = @{
    email = "<EMAIL>"
    password = "password"  # Missing uppercase, number, and special char
    first_name = "John"
    last_name = "Doe"
}

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/register" -Method POST -Body ($invalidPasswordUser | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Test failed: Invalid password was accepted" -ForegroundColor Red
}
catch {
    Write-Host "Test passed: Invalid password was rejected" -ForegroundColor Green
}

# Test valid login
Write-Host ""
Write-Host "Testing valid login..." -ForegroundColor Cyan
$validLogin = @{
    email = "<EMAIL>"
    password = "Password1!"
}

try {
    $loginResponse = Invoke-RestMethod -Uri "$apiUrl/auth/login" -Method POST -Body ($validLogin | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Login successful: $($loginResponse | ConvertTo-Json)" -ForegroundColor Green
}
catch {
    Write-Host "Login failed: $_" -ForegroundColor Red
}

# Test SQL injection in login
Write-Host ""
Write-Host "Testing SQL injection in login..." -ForegroundColor Cyan
$sqlInjectionLogin = @{
    email = "' OR '1'='1"
    password = "anything"
}

try {
    $response = Invoke-RestMethod -Uri "$apiUrl/auth/login" -Method POST -Body ($sqlInjectionLogin | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Test failed: SQL injection was accepted" -ForegroundColor Red
}
catch {
    Write-Host "Test passed: SQL injection was rejected" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor Cyan
Write-Host "Basic authentication tests completed. Check the output above for results." -ForegroundColor Cyan
