# Test script for API endpoints using Swagger

# Configuration
$baseUrl = "http://localhost:8080"
$swaggerUrl = "$baseUrl/swagger/index.html"
$apiUrl = "$baseUrl/api/v1"

# Colors for output
$colorSuccess = "Green"
$colorError = "Red"
$colorInfo = "Cyan"
$colorWarning = "Yellow"

# Function to make API requests
function Invoke-ApiRequest {
    param (
        [string]$Method,
        [string]$Endpoint,
        [object]$Body = $null,
        [hashtable]$Headers = @{},
        [string]$Description = ""
    )
    
    $url = "$apiUrl$Endpoint"
    
    Write-Host "Testing: $Description" -ForegroundColor $colorInfo
    Write-Host "  $Method $url" -ForegroundColor $colorInfo
    
    if ($Body) {
        Write-Host "  Body: $($Body | ConvertTo-Json -Compress)" -ForegroundColor $colorInfo
    }
    
    try {
        $params = @{
            Method = $Method
            Uri = $url
            ContentType = "application/json"
            Headers = $Headers
        }
        
        if ($Body -and $Method -ne "GET") {
            $params.Body = ($Body | ConvertTo-Json)
        }
        
        $response = Invoke-RestMethod @params -ErrorVariable responseError
        Write-Host "  Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor $colorSuccess
        Write-Host "  Status: Success" -ForegroundColor $colorSuccess
        return $response
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $statusDescription = $_.Exception.Response.StatusDescription
        
        Write-Host "  Status: $statusCode $statusDescription" -ForegroundColor $colorError
        
        if ($responseError) {
            Write-Host "  Error: $responseError" -ForegroundColor $colorError
        }
        
        # Try to get response body for more details
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            $reader.Close()
            
            if ($responseBody) {
                Write-Host "  Response Body: $responseBody" -ForegroundColor $colorError
            }
        }
        catch {
            # Ignore errors when trying to read the response body
        }
        
        return $null
    }
    finally {
        Write-Host ""
    }
}

# Function to test user registration
function Test-UserRegistration {
    param (
        [string]$Email,
        [string]$Password,
        [string]$FirstName,
        [string]$LastName,
        [string]$Description,
        [bool]$ExpectSuccess = $true
    )
    
    $body = @{
        email = $Email
        password = $Password
        first_name = $FirstName
        last_name = $LastName
    }
    
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/register" -Body $body -Description $Description
    
    if ($ExpectSuccess) {
        if ($response) {
            Write-Host "✓ Registration test passed: $Description" -ForegroundColor $colorSuccess
            return $true
        }
        else {
            Write-Host "✗ Registration test failed: $Description" -ForegroundColor $colorError
            return $false
        }
    }
    else {
        if (-not $response) {
            Write-Host "✓ Registration test passed (expected failure): $Description" -ForegroundColor $colorSuccess
            return $true
        }
        else {
            Write-Host "✗ Registration test failed (unexpected success): $Description" -ForegroundColor $colorError
            return $false
        }
    }
}

# Function to test user login
function Test-UserLogin {
    param (
        [string]$Email,
        [string]$Password,
        [string]$Description,
        [bool]$ExpectSuccess = $true
    )
    
    $body = @{
        email = $Email
        password = $Password
    }
    
    $response = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/login" -Body $body -Description $Description
    
    if ($ExpectSuccess) {
        if ($response -and $response.token) {
            Write-Host "✓ Login test passed: $Description" -ForegroundColor $colorSuccess
            return $response.token
        }
        else {
            Write-Host "✗ Login test failed: $Description" -ForegroundColor $colorError
            return $null
        }
    }
    else {
        if (-not $response) {
            Write-Host "✓ Login test passed (expected failure): $Description" -ForegroundColor $colorSuccess
            return $null
        }
        else {
            Write-Host "✗ Login test failed (unexpected success): $Description" -ForegroundColor $colorError
            return $null
        }
    }
}

# Main test script
Write-Host "=== Real Estate Platform API Testing ===" -ForegroundColor $colorInfo
Write-Host "Testing API endpoints using Swagger documentation" -ForegroundColor $colorInfo
Write-Host "Base URL: $baseUrl" -ForegroundColor $colorInfo
Write-Host "Swagger URL: $swaggerUrl" -ForegroundColor $colorInfo
Write-Host ""

# Check if the API is running
try {
    $healthCheck = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "API is running. Health check: $($healthCheck | ConvertTo-Json)" -ForegroundColor $colorSuccess
}
catch {
    Write-Host "API is not running. Please start the API server first." -ForegroundColor $colorError
    Write-Host "You can use 'make docker-compose-dev-d' to start the API server." -ForegroundColor $colorInfo
    exit 1
}

Write-Host ""
Write-Host "=== Testing User Registration ===" -ForegroundColor $colorInfo

# Test valid registration
$validRegistration = Test-UserRegistration -Email "<EMAIL>" -Password "Password1!" -FirstName "John" -LastName "Doe" -Description "Valid registration"

# Test invalid registrations
Test-UserRegistration -Email "" -Password "Password1!" -FirstName "John" -LastName "Doe" -Description "Empty email" -ExpectSuccess $false
Test-UserRegistration -Email "invalid-email" -Password "Password1!" -FirstName "John" -LastName "Doe" -Description "Invalid email format" -ExpectSuccess $false
Test-UserRegistration -Email "<EMAIL>" -Password "weak" -FirstName "John" -LastName "Doe" -Description "Weak password" -ExpectSuccess $false
Test-UserRegistration -Email "<EMAIL>" -Password "Password1!" -FirstName "" -LastName "Doe" -Description "Empty first name" -ExpectSuccess $false
Test-UserRegistration -Email "<EMAIL>" -Password "Password1!" -FirstName "John" -LastName "" -Description "Empty last name" -ExpectSuccess $false

# Test HTML injection
Test-UserRegistration -Email "<EMAIL>" -Password "Password1!" -FirstName "John<script>alert('xss')</script>" -LastName "Doe" -Description "HTML injection in first name" -ExpectSuccess $true

# Test SQL injection
Test-UserRegistration -Email "<EMAIL>" -Password "Password1!" -FirstName "John" -LastName "Doe' OR '1'='1" -Description "SQL injection in last name" -ExpectSuccess $true

Write-Host ""
Write-Host "=== Testing User Login ===" -ForegroundColor $colorInfo

# Test valid login
$token = Test-UserLogin -Email "<EMAIL>" -Password "Password1!" -Description "Valid login"

# Test invalid logins
Test-UserLogin -Email "" -Password "Password1!" -Description "Empty email" -ExpectSuccess $false
Test-UserLogin -Email "invalid-email" -Password "Password1!" -Description "Invalid email format" -ExpectSuccess $false
Test-UserLogin -Email "<EMAIL>" -Password "wrong" -Description "Wrong password" -ExpectSuccess $false
Test-UserLogin -Email "<EMAIL>" -Password "Password1!" -Description "Non-existent user" -ExpectSuccess $false

# Test HTML injection
Test-UserLogin -Email "<EMAIL><script>alert('xss')</script>" -Password "Password1!" -Description "HTML injection in email" -ExpectSuccess $false

# Test SQL injection
Test-UserLogin -Email "<EMAIL>' OR '1'='1" -Password "Password1!" -Description "SQL injection in email" -ExpectSuccess $false

Write-Host ""
Write-Host "=== Test Summary ===" -ForegroundColor $colorInfo

if ($validRegistration -and $token) {
    Write-Host "✓ Basic authentication tests passed" -ForegroundColor $colorSuccess
    Write-Host "You can explore the full API documentation at: $swaggerUrl" -ForegroundColor $colorInfo
}
else {
    Write-Host "✗ Some tests failed. Please check the output above." -ForegroundColor $colorError
}

Write-Host ""
Write-Host "=== End of Tests ===" -ForegroundColor $colorInfo
