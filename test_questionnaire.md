# Property Recommendation System - Testing Guide

## Overview
This document provides a comprehensive testing guide for the property recommendation system implementation.

## Prerequisites
1. MongoDB running on port 27018
2. Go application running
3. Valid JWT token for authentication

## Step 1: Seed the Database

First, seed the questionnaire questions:

```bash
go run scripts/seed_db.go -questions
```

You should see output like:
```
Seeding questionnaire questions...
Success! Inserted 8 questionnaire questions
Questions Summary: Successfully seeded 8/8 questionnaire questions
```

## Step 2: Test API Endpoints

### 1. Start Questionnaire Session

```bash
curl -X POST http://localhost:8080/api/v1/questionnaire/start \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "session_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "status": "active",
    "current_question": 1,
    "total_questions": 8,
    "started_at": "2024-06-18T10:30:00Z"
  },
  "message": "Questionnaire session started successfully"
}
```

### 2. Get Current Question

```bash
curl -X GET http://localhost:8080/api/v1/questionnaire/session/SESSION_ID/current-question \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "question": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d1",
      "text": "What's your budget range for the property?",
      "type": "single_choice",
      "order": 1,
      "is_required": true,
      "options": [
        {"id": "budget_1", "text": "₹10L - ₹25L", "value": "1000000-2500000"},
        {"id": "budget_2", "text": "₹25L - ₹50L", "value": "2500000-5000000"}
      ]
    },
    "session": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "status": "active",
      "current_question": 1,
      "total_questions": 8,
      "responses_count": 0
    },
    "progress": {
      "current": 1,
      "total": 8,
      "percentage": 0,
      "is_last_question": false,
      "next_action": "answer_current_question"
    }
  }
}
```

### 3. Submit Answer

```bash
curl -X POST http://localhost:8080/api/v1/questionnaire/session/SESSION_ID/answer \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question_id": "QUESTION_ID",
    "answer": "2500000-5000000"
  }'
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "answer_accepted": true,
    "next_question_available": true,
    "questionnaire_completed": false,
    "current_question": 2,
    "total_questions": 8
  },
  "message": "Answer submitted successfully"
}
```

### 4. Test Inappropriate Answer

```bash
curl -X POST http://localhost:8080/api/v1/questionnaire/session/SESSION_ID/answer \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "question_id": "QUESTION_ID",
    "answer": "I don't care, give me anything cheap and dirty"
  }'
```

Expected Response (Session Terminated):
```json
{
  "success": false,
  "error": {
    "code": "INAPPROPRIATE_ANSWER",
    "message": "Your response contains inappropriate content. The questionnaire session has been terminated.",
    "details": "Please provide relevant and appropriate answers related to property preferences."
  },
  "data": {
    "session_status": "terminated",
    "termination_reason": "inappropriate_content"
  }
}
```

### 5. Complete Questionnaire

After answering all 8 questions:

```bash
curl -X POST http://localhost:8080/api/v1/questionnaire/session/SESSION_ID/complete \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "session_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "status": "completed",
    "completed_at": "2024-06-18T10:40:00Z",
    "recommendation_id": "64f8a1b2c3d4e5f6a7b8c9e1",
    "total_responses": 8
  },
  "message": "Questionnaire completed successfully. Generating recommendations..."
}
```

### 6. Get Recommendations

```bash
curl -X GET http://localhost:8080/api/v1/questionnaire/session/SESSION_ID/recommendations \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

Expected Response:
```json
{
  "success": true,
  "data": {
    "recommendation_id": "64f8a1b2c3d4e5f6a7b8c9e1",
    "user_preferences": {
      "budget_range": "2500000-5000000",
      "property_type": "apartment",
      "city": "mumbai",
      "area": "andheri",
      "bhk": "2",
      "amenities": ["parking", "gym"],
      "status": "buy",
      "furnishing": "semi-furnished"
    },
    "properties": [
      {
        "id": "64f8a1b2c3d4e5f6a7b8c9f1",
        "title": "Spacious 2BHK in Andheri West",
        "type": "apartment",
        "status": "sell",
        "total_price": 4500000,
        "match_score": 95
      }
    ],
    "total_matches": 5,
    "showing": 5,
    "filters_applied": {
      "budget": "₹25L - ₹50L",
      "type": "Apartment",
      "location": "Mumbai, Andheri",
      "bhk": "2BHK"
    },
    "generated_at": "2024-06-18T10:40:30Z"
  },
  "message": "Found 5 properties matching your preferences"
}
```

## Step 3: Test Frontend Integration

### Frontend Flow Example (React.js)

```javascript
// 1. Start questionnaire
const startQuestionnaire = async () => {
  const response = await fetch('/api/v1/questionnaire/start', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();
  setSessionId(data.data.session_id);
  setTotalQuestions(data.data.total_questions);
};

// 2. Get current question
const getCurrentQuestion = async () => {
  const response = await fetch(`/api/v1/questionnaire/session/${sessionId}/current-question`);
  const data = await response.json();
  
  setCurrentQuestion(data.data.question);
  setProgress(data.data.progress);
  
  // Check if last question
  if (data.data.progress.is_last_question) {
    setButtonText("Complete Questionnaire");
  } else {
    setButtonText("Next Question");
  }
};

// 3. Submit answer
const submitAnswer = async (questionId, answer) => {
  const response = await fetch(`/api/v1/questionnaire/session/${sessionId}/answer`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ question_id: questionId, answer })
  });
  
  const data = await response.json();
  
  if (!response.ok) {
    if (data.error.code === 'INAPPROPRIATE_ANSWER') {
      showTerminationMessage();
      return;
    }
  }
  
  if (data.data.questionnaire_completed) {
    completeQuestionnaire();
  } else {
    getCurrentQuestion();
  }
};

// 4. Complete and get recommendations
const completeQuestionnaire = async () => {
  await fetch(`/api/v1/questionnaire/session/${sessionId}/complete`, {
    method: 'POST'
  });
  
  const response = await fetch(`/api/v1/questionnaire/session/${sessionId}/recommendations`);
  const data = await response.json();
  
  setRecommendations(data.data);
  showResults();
};
```

## Step 4: Admin Testing

### Create New Question (Admin Only)

```bash
curl -X POST http://localhost:8080/api/v1/admin/questionnaire/questions \
  -H "Authorization: Bearer ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "What is your preferred floor level?",
    "type": "single_choice",
    "order": 9,
    "is_required": false,
    "options": [
      {"id": "floor_1", "text": "Ground Floor", "value": "0"},
      {"id": "floor_2", "text": "1-3 Floors", "value": "1-3"},
      {"id": "floor_3", "text": "4-10 Floors", "value": "4-10"},
      {"id": "floor_4", "text": "Above 10 Floors", "value": "10+"}
    ]
  }'
```

## Expected Database Collections

After successful testing, you should see these collections in MongoDB:

1. **questions** - Contains 8 questionnaire questions
2. **questionnaire_sessions** - Contains user sessions
3. **recommendation_results** - Contains generated recommendations

## Troubleshooting

### Common Issues:

1. **Session Expired Error**: Sessions expire after 30 minutes of inactivity
2. **Invalid Question Order**: Questions must be answered in sequence
3. **Inappropriate Content**: System terminates sessions with inappropriate answers
4. **No Properties Found**: Ensure properties are seeded in database

### Debug Commands:

```bash
# Check if questions exist
mongo --port 27018 --eval "db.questions.count()"

# Check active sessions
mongo --port 27018 --eval "db.questionnaire_sessions.find({status: 'active'})"

# Check recommendations
mongo --port 27018 --eval "db.recommendation_results.find()"
```

This completes the testing guide for the property recommendation system.
